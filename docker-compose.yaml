version: '3.5'
services:
  api:
    image: ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}
    build:
      target: main
      context: .
      dockerfile: Dockerfile.prod
    networks:
      - build_network
  db:
    image: postgres:10
    command: postgres -c fsync=off -c synchronous_commit=off -c full_page_writes=off
    ports:
      - 5432
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
    networks:
      - build_network
networks:
  build_network:
    name: ${SERVICE}-${BRANCH_NAME_NORMALIZED}
