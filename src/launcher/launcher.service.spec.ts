import "module-alias/register";
import { suite, test } from "mocha-typescript";
import { Test } from "@nestjs/testing";
import { Names } from "@names";
import { LauncherService } from "@launcher/launcher.service";
import { BaseHttpService, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { IntegrationGameLaunchRequest } from "@entities/integration.entities";
import { expect } from "chai";
import { testing } from "@skywind-group/sw-utils";
import * as request from "superagent";
import { checkRequests } from "@utils/test/testing";
import status200 = testing.status200;
import * as sinon from "sinon";
import { OperatorJurisdiction, OperatorPlatformType } from "@entities/operator.entities";

const launchRequest: IntegrationGameLaunchRequest = {
    GameCode: "sw_al",
    PublicToken: "I'm a token",
    ChannelID: "111",
    FreeSpinToken: "fs_tocken",
    language: "ru",
    jurisdiction: OperatorJurisdiction.MT,
    playmode: PlayMode.REAL,
    platform: OperatorPlatformType.desktop
};

@suite()
class LauncherServiceSpec {
    private launcherService: LauncherService;
    private requestMock: testing.RequestMock = testing.requestMock(request);

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                LauncherService,
                { useValue: new BaseHttpService(config.swOperatorUrl), provide: Names.BaseHttpService }
            ]
        }).compile();
        this.launcherService = moduleRef.get<LauncherService>(LauncherService);
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    @test()
    async happyPath() {
        this.requestMock.post(
            "http://localhost:3007/v1/merchants/game/url",
            status200({
                token:
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmdWNrIjoidGhpcyBpbnRlZ3JhaW9uIn0.U7ZsVmdumb9eF9z1YGYu-TJ66N5L9Z7d9fr1VLDRQVY",
                url: "http://tut.by"
            })
        );
        const result = await this.launcherService.getGameUrl(launchRequest, "168.228.666.1");

        const expectedApiRequest = {
            channelId: 111,
            gameCode: "sw_al",
            ip: "168.228.666.1",
            jurisdiction: "MT",
            merchantCode: `b365__${OperatorJurisdiction.MT}`,
            merchantType: "b365",
            playmode: PlayMode.REAL,
            platform: OperatorPlatformType.desktop,
            publicToken: "I'm a token",
            history_url: undefined,
            lobby: undefined,
            cashier: undefined,
            language: "ru",
            freeSpinToken: "fs_tocken",
            ADM: undefined,
            giocoresponsabile: undefined
        };
        checkRequests(this.requestMock, [
            {
                body: expectedApiRequest,
                method: "POST",
                query: {},
                url: "http://localhost:3007/v1/merchants/game/url",
                headers: {},
                keysToVoid: { headers: ["authorization", "accept"] }
            }
        ]);
        expect(result).to.equal("http://tut.by");
    }

    @test()
    async errorDuringLogin() {
        this.requestMock.post(
            "http://localhost:3007/v1/merchants/game/url",
            status200({
                token:
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmdWNrIjoidGhpcyBpbnRlZ3JhaW9uIiwibG9naW5GYWlsZWQiOnRydWUsInJlbGF1bmNoVXJsIjoiaHR0cDovL29ubGluZXIuYnkvcmVsYXVuY2gifQ.3dChzKIR5y95cIDWGSUFvg5nB_peXrq6YbOTOTbKmfU",
                url: "http://tut.by"
            })
        );
        const result = await this.launcherService.getGameUrl(launchRequest, "168.228.666.1");
        expect(result).to.equal("http://onliner.by/relaunch");
    }

    @test()
    async JurisdictionDe() {
        this.requestMock.post("http://localhost:3007/v1/merchants/game/url", status200({}));
        launchRequest.jurisdiction = OperatorJurisdiction.DE;
        await this.launcherService.getGameUrl(launchRequest, "168.228.666.1");
        const expectedApiRequest = {
            channelId: 111,
            gameCode: "sw_al",
            ip: "168.228.666.1",
            jurisdiction: OperatorJurisdiction.DE,
            merchantCode: `b365__${OperatorJurisdiction.DE}`,
            merchantType: "b365",
            playmode: PlayMode.REAL,
            platform: OperatorPlatformType.desktop,
            publicToken: "I'm a token",
            history_url: undefined,
            lobby: undefined,
            cashier: undefined,
            language: "ru",
            freeSpinToken: "fs_tocken",
            ADM: undefined,
            giocoresponsabile: undefined
        };
        checkRequests(this.requestMock, [
            {
                body: expectedApiRequest,
                method: "POST",
                query: {},
                url: "http://localhost:3007/v1/merchants/game/url",
                headers: {},
                keysToVoid: { headers: ["authorization", "accept"] }
            }
        ]);
    }
}
