import { Controller, Get, HttpStatus, Query, Redirect, UseFilters } from "@nestjs/common";
import { ClientIp } from "@skywind-group/sw-integration-core";
import { LauncherService } from "@launcher/launcher.service";
import { IntegrationGameLaunchRequest } from "@entities/integration.entities";
import { BaseErrorsFilter } from "@launcher/errors.filter";
import { ValidationPipe } from "@utils/validator.pipe";

@Controller("game")
export class LauncherController {
    constructor(private launcherService: LauncherService) {}

    @Get("/url")
    @UseFilters(BaseErrorsFilter)
    @Redirect(undefined, HttpStatus.FOUND)
    async getGameUrl(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }

    @Get("/url/noredirect")
    @UseFilters(BaseErrorsFilter)
    async getGameUrlWithoutRedirection(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }
}
