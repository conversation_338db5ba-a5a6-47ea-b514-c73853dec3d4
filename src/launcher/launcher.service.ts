import { Inject, Injectable } from "@nestjs/common";
import { BaseHttpService, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { Names } from "@names";

import { measures, token as jwt } from "@skywind-group/sw-utils";
import {
    IntegrationGameLaunchRequest,
    IntegrationInitRequest,
    IntegrationStartGameTokenData
} from "@entities/integration.entities";
import { OperatorJurisdiction } from "@entities/operator.entities";
import { SWJurisdiction } from "@entities/sw.entities";
import config from "@config";
import { ValidationError } from "@errors/sw.errors";
import { buildMerchantCode } from "@utils/common";
import measure = measures.measure;

@Injectable()
export class LauncherService {
    public static FALCON_GAME_URL = "v1/merchants/game/url";

    constructor(@Inject(Names.BaseHttpService) private baseHttpService: BaseHttpService) {}

    @measure({ name: "B365LauncherService.getGameUrl", isAsync: true })
    async getGameUrl(data: IntegrationGameLaunchRequest, ip: string): Promise<string> {
        if (data.playmode === PlayMode.REAL && !data.PublicToken) {
            throw new ValidationError("PublicToken is required for real mode");
        }
        if (data.playmode === PlayMode.FUN && data.jurisdiction !== OperatorJurisdiction.IT) {
            throw new ValidationError(`Fun mode is not supported for ${data.jurisdiction}`);
        }
        const sanitizedData = this.sanitizeData(data);
        const options = this.mapOperatorToSW(sanitizedData, ip);
        const { token, url } = await this.baseHttpService.post<any>(LauncherService.FALCON_GAME_URL, options);
        if (token) {
            const startGameToken: IntegrationStartGameTokenData = jwt.parse(token);
            if (startGameToken.loginFailed) {
                return startGameToken.relaunchUrl;
            }
        }

        return url;
    }

    private mapOperatorToSW(data: IntegrationGameLaunchRequest, ip: string): IntegrationInitRequest {
        const jurisdiction = this.getJurisdiction(data.jurisdiction);
        return {
            merchantType: data.merchantType || config.merchantType,
            merchantCode: buildMerchantCode(jurisdiction, data.merchantCode),
            gameCode: data.GameCode,
            playmode: data.playmode as PlayMode,
            publicToken: data.PublicToken,
            ip,
            jurisdiction: jurisdiction,
            channelId: +data.ChannelID,
            language: data.language,
            history_url: data.historyUrl,
            lobby: data.lobbyUrl,
            cashier: data.cashierUrl,
            platform: data.platform,
            freeSpinToken: data.FreeSpinToken || data.freeSpinToken,
            ADM: data.ADM,
            giocoresponsabile: data.giocoresponsabile
        };
    }

    private getJurisdiction(jurisdiction?: string) {
        switch (jurisdiction) {
            case OperatorJurisdiction.UK:
                return SWJurisdiction.GB2;
            case OperatorJurisdiction.COM:
                return SWJurisdiction.GI;
            case OperatorJurisdiction.IT:
                return SWJurisdiction.IT;
            case OperatorJurisdiction.NL:
                return SWJurisdiction.NL;
            case OperatorJurisdiction.DE:
                return SWJurisdiction.DE;
            case OperatorJurisdiction.BR:
                return SWJurisdiction.BR;
            case OperatorJurisdiction.MX:
                return SWJurisdiction.MX;
            case OperatorJurisdiction.GR:
                return SWJurisdiction.GR;
            case OperatorJurisdiction.ES:
                return SWJurisdiction.ES;
            default:
                return SWJurisdiction.MT;
        }
    }

    private sanitizeData(data: IntegrationGameLaunchRequest): IntegrationGameLaunchRequest {
        return Object.keys(data).reduce(
            (query: any, param: string) => ({
                ...query,
                [param]: Array.isArray(data[param]) ? data[param][0] : data[param]
            }),
            {}
        );
    }
}
