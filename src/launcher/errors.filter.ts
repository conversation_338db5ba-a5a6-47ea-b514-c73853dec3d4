import { ExceptionFilter, Catch, ArgumentsHost, HttpStatus } from "@nestjs/common";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { FastifyReply } from "fastify";

@Catch()
export class BaseErrorsFilter implements ExceptionFilter {
    catch(error: any, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<FastifyReply<any>>();
        const request = ctx.getRequest();
        const asSwError = error as SWError;
        const status = asSwError.responseStatus || HttpStatus.INTERNAL_SERVER_ERROR;

        response.status(status).send({
            statusCode: status,
            errorCode: asSwError.code || undefined,
            errorMessage: asSwError.message || "Unknown error"
        });
    }
}
