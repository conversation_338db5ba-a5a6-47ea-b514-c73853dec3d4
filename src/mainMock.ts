// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import "module-alias/register";
import { bootstrapMock } from "@skywind-group/sw-integration-core";
import config from "@config";
import { MockModule } from "@mock/mock.module";

bootstrapMock({
    serviceName: "sw-b365-mock",
    versionFile: "./lib/version",
    module: MockModule,
    internalPort: config.internalServer.port,
    port: config.server.mockPort,
    actions: [
        "login",
        "getBalance",
        "startGameRound",
        "endGameRound",
        "transaction:stake",
        "transaction:return",
        "freeBetsTransaction:stake",
        "freeBetsTransaction:return",
        "getFreeBets",
        "cancelTransaction",
        "realityCheckPlayerChoice",
        "realityCheck",
        "verifyPublicToken",
        "reportGameChecksums",
        "reportPlatformChecksums"
    ]
});
