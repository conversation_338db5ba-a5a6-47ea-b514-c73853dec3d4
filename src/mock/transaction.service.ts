import { Injectable } from "@nestjs/common";
import { Customer, Merchant } from "./merchant";
import * as uuid from "uuid";

export enum WalletAction {
    Debit = "debit", // BET
    ZeroWin = "zero_win",
    Credit = "credit", // WIN
    Rollback = "rollback"
}

export interface Transaction {
    amount: number;
    trxId: string;
    externalId: string;
    rollback: boolean;
    customerId: string;
    merchantId: string;
    createdAt: Date;
    updatedAt: Date;
    action: WalletAction;
}

@Injectable()
export class TransactionService {
    private transactions: Transaction[] = [];

    public create(
        merchantId: string,
        customerId: string,
        data: { trxId: string; amount: number; action: WalletAction }
    ): Transaction {
        const transaction: Transaction = {
            trxId: data.trxId,
            action: data.action,
            amount: Math.abs(+data.amount) * this.multiplier(data.action),
            externalId: uuid.v4(),
            customerId,
            merchantId,
            rollback: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.transactions.push(transaction);

        return transaction;
    }

    public getById(trxId: string, action: WalletAction): Transaction {
        return this.transactions.find((trx) => trx.trxId === trxId && trx.action === action);
    }

    public setUp(transactions: Transaction[]) {
        this.transactions = transactions;
    }

    public getAll(merchantId: string, customerId: string, trxId?: string): Transaction[] {
        const transactions = this.transactions.filter(
            (trx) => trx.merchantId === merchantId && trx.customerId === customerId
        );

        if (trxId) {
            return transactions.filter((trx) => trx.trxId === trxId);
        }

        return transactions;
    }

    public rollback(transaction: Transaction): Transaction {
        transaction.rollback = true;
        transaction.updatedAt = new Date();

        return transaction;
    }

    private multiplier(action: WalletAction): number {
        if (action === WalletAction.Debit) {
            return -1;
        }

        return 1;
    }
}
