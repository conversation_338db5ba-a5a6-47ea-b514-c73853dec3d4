import { Injectable } from "@nestjs/common";

export enum RaiseType {
    BEFORE = "BEFORE",
    AFTER = "AFTER"
}

export interface CustomError {
    error_code: number;
    http_response_status: string;
    error_msg: string;

    [custom_field: string]: any;
}

export interface ActionErrors {
    [raiseType: string]: CustomError[];
}

export interface CustomerErrors {
    [action: string]: ActionErrors;
}

export interface MerchantErrors {
    [customerId: string]: CustomerErrors;
}

export interface CustomErrorsContainer {
    [merchantCode: string]: MerchantErrors;
}

@Injectable()
export class CustomErrorService {
    private errors: CustomErrorsContainer = {};

    public getMerchantErrors(merchantId: string): MerchantErrors {
        this.errors[merchantId] = this.errors[merchantId] || {};

        return this.errors[merchantId];
    }

    public getCustomerErrors(merchantId: string, customerId: string): CustomerErrors {
        const merchantErrors = this.getMerchantErrors(merchantId);
        merchantErrors[customerId] = merchantErrors[customerId] || {};

        return merchantErrors[customerId];
    }

    public getActionErrors(merchantId: string, customerId: string, action: string): ActionErrors {
        const customerErrors = this.getCustomerErrors(merchantId, customerId);
        customerErrors[action] = customerErrors[action] || {};

        return customerErrors[action];
    }

    public createError(
        merchantId: string,
        customerId: string,
        action: string,
        raiseType: RaiseType,
        data: CustomError | CustomError[]
    ): CustomError[] {
        const actionErrors = this.getActionErrors(merchantId, customerId, action);
        actionErrors[raiseType] = actionErrors[raiseType] || [];
        const errors = Array.isArray(data) ? data : [data];
        actionErrors[raiseType] = actionErrors[raiseType].concat(errors);

        return errors;
    }

    public removeError(merchantId: string, customerId: string, action: string): ActionErrors {
        const customerErrors = this.getCustomerErrors(merchantId, customerId);

        const actionErrors = customerErrors[action];
        customerErrors[action] = {};

        return actionErrors;
    }

    public throwError(merchantId: string, customerId: string, action: string, raiseType: RaiseType) {
        if (!merchantId || !customerId) {
            return;
        }
        // safely return and remove the value from a nested key in an object
        const error = this.shiftCustomError(merchantId, customerId, action, raiseType);

        if (!error) {
            return;
        }

        throw error;
    }

    private shiftCustomError(
        merchantId: string,
        customerId: string,
        action: string,
        raiseType: RaiseType
    ): CustomError {
        const actionErrors = this.getActionErrors(merchantId, customerId, action);
        const errors = actionErrors[raiseType] || [];

        return errors.shift();
    }
}
