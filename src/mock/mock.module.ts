import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { APP_FILTER } from "@nestjs/core";
import { mock } from "@skywind-group/sw-integration-core";
import { CustomerGuard } from "./guards/customer.guard";
import { AuthenticateResponder } from "./responders/authenticate";
import { BalanceResponder } from "./responders/balance";
import { MockController } from "@mock/mock.controller";
import { MockService } from "@mock/mock.service";
import { BasicAuthGuard } from "@mock/guards/auth.guard";
import { RoundResponder } from "@mock/responders/round";
import { MerchantGuard } from "@mock/guards/merchant.guard";
import { LoginGuard } from "@mock/guards/login.guard";
import { ErrorsFilter } from "@mock/./errors.filter";
import { PaymentResponder } from "@mock/responders/payment";
import { FreeBetsPaymentResponder } from "@mock/responders/freeBets.payment";
import { FreeBetsResponder } from "@mock/responders/freeBets";
import { RealityCheckResponder } from "@mock/responders/realityCheck";
import { CreatePromoController } from "@mock/mock.promo.controller";

@Module({
    controllers: [MockController, CreatePromoController],
    providers: [
        CustomerGuard,
        MockService,
        BasicAuthGuard,
        MerchantGuard,
        LoginGuard,
        {
            provide: APP_FILTER,
            useClass: ErrorsFilter
        }
    ],
    imports: [mock.MockModule]
})
export class MockModule implements NestModule {
    public configure(consumer: MiddlewareConsumer): any {
        consumer
            .apply(AuthenticateResponder)
            .forRoutes("v1/mock/API/:regulation/Login")

            .apply(RoundResponder)
            .forRoutes("v1/mock/API/:regulation/GameRound")

            .apply(BalanceResponder)
            .forRoutes("v1/mock/API/:regulation/Balance")

            .apply(PaymentResponder)
            .forRoutes("v1/mock/API/:regulation/Transaction", "v1/mock/API/:regulation/CancelTransaction")

            .apply(FreeBetsPaymentResponder)
            .forRoutes("v1/mock/API/:regulation/FreeSpin/Transaction")

            .apply(FreeBetsResponder)
            .forRoutes("v1/mock/API/:regulation/FreeSpin")

            .apply(RealityCheckResponder)
            .forRoutes("v1/mock/API/:regulation/RealityCheck");
    }
}
