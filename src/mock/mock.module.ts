import { Module } from "@nestjs/common";
import { Imports } from "..";
import { SettingsController } from "./settings.controller";
import { SettingsService } from "./settings.service";
import { MerchantController } from "./merchant.controller";
import { MerchantService } from "./merchant.service";
import { CustomerService } from "./customer.service";
import { Helper } from "./utils/helper";
import { CustomerController } from "./customer.controller";
import { CustomErrorController } from "./customError.controller";
import { CustomErrorService } from "./customError.service";
import { ExtraDataController } from "./extraData.controller";
import { ExtraDataService } from "./extraData.service";
import { TicketController } from "./ticket.controller";
import { TicketService } from "./ticket.service";
import { SessionController } from "./session.controller";
import { SessionService } from "./session.service";
import { TransactionService } from "./transaction.service";
import { TransactionController } from "./transaction.controller";

export interface MockModuleConfig {
    serviceName: string;
    versionFile: string;
}

@Module({
    controllers: [
        SettingsController,
        MerchantController,
        CustomerController,
        CustomErrorController,
        ExtraDataController,
        TicketController,
        SessionController,
        TransactionController
    ],
    providers: [
        SettingsService,
        MerchantService,
        CustomerService,
        CustomErrorService,
        ExtraDataService,
        Helper,
        TicketService,
        SessionService,
        TransactionService
    ],
    exports: [
        SettingsService,
        MerchantService,
        CustomerService,
        CustomErrorService,
        ExtraDataService,
        Helper,
        TicketService,
        SessionService,
        TransactionService
    ]
})
export class MockModule {}
