import { CanActivate, ExecutionContext } from "@nestjs/common";
import { SessionService } from "../session.service";

export abstract class CustomerGuard implements CanActivate {
    protected constructor(protected sessionService: SessionService) {}
    public abstract getCustomerId(req): string;
    public abstract getSessionId(req): string;
    public abstract throwError(context?: ExecutionContext): void;

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const customerId = this.getCustomerId(request);
        const sessionId = this.getSessionId(request);
        const customer = request.merchant.customers[customerId];

        if (!(customer && this.sessionService.validate(sessionId))) {
            this.throwError(context);
            return false;
        }

        request.customer = customer;

        return true;
    }
}
