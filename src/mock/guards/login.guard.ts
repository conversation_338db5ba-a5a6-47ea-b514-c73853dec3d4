import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PlayerSessionExpiredError } from "@errors/sw.errors";

@Injectable()
export class LoginGuard implements CanActivate {
    constructor(private ticketService: mock.TicketService, private merchantService: mock.MerchantService) {}

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const ticketId = request.body.PublicToken;
        const ticket: mock.Ticket = this.ticketService.getById(ticketId);
        if (!ticket) {
            throw new PlayerSessionExpiredError();
        }
        const merchant: mock.Merchant = this.merchantService.getById(ticket.merchantId, true);
        request.merchant = merchant;
        const customer = merchant.customers[ticket.customerId];
        request.customer = customer;
        return true;
    }
}
