import { CanActivate, ExecutionContext } from "@nestjs/common";
import { MerchantService } from "../merchant.service";

export abstract class MerchantGuard implements CanActivate {
    protected constructor(protected merchantService: MerchantService) {}
    public abstract getMerchantCode(req): string;
    public abstract throwError(req?): void;

    public canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const code = this.getMerchantCode(request);
        const merchant = this.merchantService.getById(code);

        if (!merchant) {
            this.throwError(request);
            return false;
        }

        request.merchant = merchant;

        return true;
    }
}
