import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PlayerSessionExpiredError } from "@errors/sw.errors";

@Injectable()
export class MerchantGuard extends mock.MerchantGuard {
    constructor(private readonly sessionService: mock.SessionService, protected merchantService: mock.MerchantService) {
        super(merchantService);
    }

    getMerchantCode(req: PrivateTokenAwareRequest): string {
        const customerSession = this.sessionService.getById(this.getSessionId(req));
        return customerSession?.merchantId;
    }

    throwError(): void {
        throw new PlayerSessionExpiredError();
    }

    getSessionId(req: PrivateTokenAwareRequest): string {
        return req.query.PrivateToken || req.body.PrivateToken;
    }
}

export interface PrivateTokenAwareRequest {
    query?: { PrivateToken: string };
    body?: { PrivateToken: string };
}

@Injectable()
export class MerchantGuardPromo extends mock.MerchantGuard {
    constructor(private readonly sessionService: mock.SessionService, protected merchantService: mock.MerchantService) {
        super(merchantService);
    }

    getMerchantCode(req: any): string {
        return req.params.merchId;
    }

    throwError(): void {
        throw new PlayerSessionExpiredError();
    }
}
