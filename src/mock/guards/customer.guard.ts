import { ExecutionContext, Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PlayerSessionExpiredError } from "@errors/sw.errors";
import { PrivateTokenAwareRequest } from "@mock/guards/merchant.guard";

@Injectable()
export class CustomerGuard extends mock.CustomerGuard {
    constructor(protected sessionService: mock.SessionService) {
        super(sessionService);
    }

    getCustomerId(req: PrivateTokenAwareRequest): string {
        const sessionId = this.getSessionId(req);
        return sessionId.split("-")[1];
    }

    getSessionId(req: PrivateTokenAwareRequest): string {
        return req.query.PrivateToken || req.body.PrivateToken;
    }

    throwError(): void {
        throw new PlayerSessionExpiredError();
    }
}

@Injectable()
export class CustomerGuardPromo extends mock.CustomerGuard {
    constructor(protected sessionService: mock.SessionService) {
        super(sessionService);
    }

    getCustomerId(req: any): string {
        return req.params.custId;
    }

    getSessionId() {
        return "";
    }

    throwError(): void {
        throw new PlayerSessionExpiredError();
    }

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        request.customer = request.merchant.customers[request.params.customerId];

        return !!request.customer;
    }
}
