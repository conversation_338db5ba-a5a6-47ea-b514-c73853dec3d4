import { Settings } from "./settings";
import { Injectable } from "@nestjs/common";

@Injectable()
export class SettingsService {
    public settings = new Settings();

    public clearAll() {
        this.settings = new Settings();
    }

    public setToDefault() {
        this.settings = new Settings();
    }

    public update(settings: Settings) {
        this.settings = settings;
    }

    public loadTestModeEnable(): boolean {
        return !!this.settings.notSaveAnyData;
    }
}
