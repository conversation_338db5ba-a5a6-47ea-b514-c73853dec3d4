export interface Merchants {
    [merchantId: string]: Merchant;
}

export interface Merchant {
    merch_id: string;
    merch_pwd: string;
    customers?: {
        [customerId: string]: Customer;
    };
    isPromoInternal?: boolean;
    multiple_session?: boolean;
}

export interface MerchantInfo {
    merch_id: string;
    merch_pwd: string;
    isPromoInternal: string;
    multiple_session: string;
}

export interface Customer {
    cust_id: string;
    cust_login?: string;
    currency_code?: string;
    language?: string;
    country?: string;
    email?: string;
    test_cust?: boolean;
    first_name?: string;
    last_name?: string;
    date_of_birth?: string;
    status?: string;
    bet_limit?: number;
    balance?: CustomerBalance;
    cust_session_id?: string;
    freeBets?: {
        count: number;
        coin: number;
    };
    jurisdiction?: string;
    extraData?: any;
}

interface CustomerBalance {
    amount: number;
    currency_code: string;
    extra_data?: Record<string, any>;
}
