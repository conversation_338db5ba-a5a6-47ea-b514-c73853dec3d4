import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { OperatorFreeSpinTransactionResponse } from "@entities/operator.entities";

@Injectable()
export class FreeBetsPaymentResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(protected settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: any): Promise<OperatorFreeSpinTransactionResponse> {
        return {
            Balance: { TotalAmount: this.settingsService.settings.amount },
            CurrencyCode: req.CurrencyCode || "EUR",
            AmountInGBP: 0,
            NumberOfSpinsRemaining: 100,
            ErrorDetails: null,
            MessageID: 0,
            UTCTimeStamp: new Date().toISOString()
        };
    }
}
