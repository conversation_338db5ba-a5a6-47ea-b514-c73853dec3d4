import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import * as http from "http";
import { OperatorStartGameRoundResponse } from "@entities/operator.entities";
import { generateNumber } from "@utils/common";

@Injectable()
export class RoundResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(protected settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: any): Promise<OperatorStartGameRoundResponse> {
        return {
            GameRoundID: generateNumber(30).toString(),
            MessageID: 0,
            UTCTimeStamp: new Date().toISOString(),
            ErrorDetails: null
        }
    }
}
