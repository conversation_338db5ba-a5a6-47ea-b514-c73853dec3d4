import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PaymentResponder } from "@mock/responders/payment";
import * as http from "http";
import * as url from "url";

@Injectable()
export class BalanceResponder extends PaymentResponder {
    constructor(protected settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public getBody(req: http.IncomingMessage): Promise<any> {
        return url.parse(req.url, true).query;
    }
}
