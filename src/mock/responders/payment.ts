import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { OperatorRealityCheckDetails, OperatorTransactionResponse } from "@entities/operator.entities";

@Injectable()
export class PaymentResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(protected settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: any): Promise<OperatorTransactionResponse> {
        return {
            Balance: { TotalAmount: this.settingsService.settings.amount },
            CurrencyCode: req.CurrencyCode || "EUR",
            AmountInGBP: 0,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: null,
            MessageID: 0,
            UTCTimeStamp: new Date().toISOString()
        };
    }
}
