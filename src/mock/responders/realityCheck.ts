import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { OperatorRealityCheckPlayerChoiceResponse } from "@entities/operator.entities";

@Injectable()
export class RealityCheckResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(protected settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: any): Promise<OperatorRealityCheckPlayerChoiceResponse> {
        return {
            ErrorDetails: null,
            MessageID: 0,
            UTCTimeStamp: new Date().toISOString()
        };
    }
}
