import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { OperatorGetFreeSpinsResponse } from "@entities/operator.entities";

@Injectable()
export class FreeBetsResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(protected settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: any): Promise<OperatorGetFreeSpinsResponse> {
        return {
            StakeAmountPerLine: 1,
            NumberOfLines: 2,
            NumberOfSpinsRemaining: 100,
            CurrencyCode: req.CurrencyCode || "EUR",
            ErrorDetails: null,
            MessageID: 0,
            UTCTimeStamp: new Date().toISOString()
        };
    }
}
