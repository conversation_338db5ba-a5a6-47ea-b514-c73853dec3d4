import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import * as http from "http";
import { OperatorLoginRequest, OperatorLoginResponse } from "@entities/operator.entities";

@Injectable()
export class AuthenticateResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(
        protected helper: mock.Helper,
        protected ticketService: mock.TicketService,
        protected settingsService: mock.SettingsService
    ) {
        super(settingsService);
    }
    public async getResponse(req: http.IncomingMessage, body: OperatorLoginRequest): Promise<OperatorLoginResponse> {
        const [customerId, currency] = this.ticketService.getDataFromTicket(body.PublicToken);
        return {
            GamingId: customerId,
            PrivateToken: this.helper.generateId(customerId, "fake"),
            CurrencyCode: currency,
            CountryCode: "GB",
            VIPLevel: 0,
            RelaunchUrl: null,
            ErrorDetails: null,
            MessageID: 0,
            UTCTimeStamp: new Date().toISOString()
        };
    }
}
