import { CallH<PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { ExtraDataService } from "./extraData.service";
import * as deepmerge from "deepmerge";

@Injectable()
export class ExtraDataInterceptor implements NestInterceptor {
    constructor(private service: ExtraDataService) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const action = context.getHandler().name;
        const merchantId = request.merchant?.merch_id;
        const customerId = request.customer?.cust_id;

        return next.handle().pipe(
            map((data) => {
                const extraData = this.service.shiftExtraData(merchantId, customerId, action);

                return deepmerge(data, extraData);
            })
        );
    }
}
