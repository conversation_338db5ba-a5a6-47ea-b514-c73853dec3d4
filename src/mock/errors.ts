import { SWError } from "@skywind-group/sw-wallet-adapter-core";

export interface ExtendedErrorInfo extends Error {
    code: string | number;
}

export interface ErrorInfoToLog<T = ExtendedErrorInfo> {
    err: T;
}

export interface BaseErrorToLog {
    toLog?(): ErrorInfoToLog;
}

export class SWBaseError extends SWError implements BaseErrorToLog {
    public readonly error_code: number;
    public readonly error_msg: string;

    constructor(status: number, message: string, code?: number, extraData?: any) {
        super(status, code, message, undefined, extraData);
        this.error_code = code;
        this.error_msg = message;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.error_code,
                message: this.error_msg,
                stack: this.stack
            }
        };
    }
}

export class ValueIsMissing extends SWBaseError {
    constructor(value: string) {
        super(200, `${value} is missing`, -1);
    }
}

export class EntityNotFound extends SWBaseError {
    constructor(entityType: string, entityId: string) {
        super(200, `cannot find ${entityType} ${entityId}`);
    }
}

export class DuplicateTransaction extends SWBaseError {
    constructor(extraData?) {
        super(200, "Transaction already exists", 1, extraData);
    }
}

export class TransactionNotFound extends SWBaseError {
    constructor(extraData: any) {
        super(200, "Bet transaction not found", -7, extraData);
    }
}

export class ValueNotFound extends SWBaseError {
    constructor(value: string) {
        super(400, `${value} not found`, -1);
    }
}

export class MerchantMismatch extends SWBaseError {
    constructor() {
        super(400, "Merchant mismatch", -1);
    }
}

export class PlayerNotFound extends SWBaseError {
    constructor(customerId: string = "") {
        super(200, `Customer ${customerId} not found`, -2);
    }
}

export class GameTokenExpired extends SWBaseError {
    constructor(message) {
        super(200, message || "Customer ticket or session not found", -3);
    }
}

export class PlayerIsSuspended extends SWBaseError {
    constructor() {
        super(200, "Player is suspended", -301);
    }
}

export class BetLimitExceeded extends SWBaseError {
    constructor() {
        super(200, "Bet limit Exceeded", -302);
    }
}

export class InsufficientBalance extends SWBaseError {
    constructor() {
        super(200, "Insufficient balance", -4);
    }
}

export class InsufficientFreebets extends SWBaseError {
    constructor() {
        super(200, "Insufficient freebets", -5);
    }
}

export class InvalidFreebet extends SWBaseError {
    constructor() {
        super(200, "Invalid freebet", -6);
    }
}

export class RoundNotFound extends SWBaseError {
    constructor() {
        super(200, "Round not found", -7);
    }
}

export class MethodWorksOnlyWithJSON extends SWBaseError {
    constructor() {
        super(400, "Method works only with JSON");
    }
}

export class Forbidden extends SWBaseError {
    constructor(message?: string) {
        super(403, message || "Operation forbidden");
    }
}

export class InvalidRequest extends SWBaseError {
    constructor(message?: string) {
        super(400, message || "Invalid request", -1);
    }
}

export class EmailAlreadyExists extends SWBaseError {
    constructor(email: string) {
        super(400, `Email is already used - ${email}`);
    }
}
