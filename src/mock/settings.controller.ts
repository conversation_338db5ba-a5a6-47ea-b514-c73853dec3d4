import { Get, Controller, <PERSON>, Body, BadRequestException } from "@nestjs/common";
import { SettingsService } from "./settings.service";
import { Settings } from "./settings";

@Controller("/v1/settings")
export class SettingsController {
    constructor(private readonly service: SettingsService) {}

    @Get()
    public getSettings() {
        return this.service.settings;
    }

    @Patch()
    public updateSettings(@Body() settings: Settings): Settings {
        const aroundAmount = settings.aroundAmount;
        if (aroundAmount !== undefined) {
            if (aroundAmount !== true && aroundAmount !== false) {
                throw new BadRequestException("aroundAmount is missing");
            }
        }

        const decreaseLoad = settings.decreaseLoad;
        if (decreaseLoad !== undefined) {
            if (decreaseLoad !== true && decreaseLoad !== false) {
                throw new BadRequestException("decreaseLoad is missing");
            }
        }

        const notSaveAnyData = settings.notSaveAnyData;
        if (notSaveAnyData !== undefined) {
            if (notSaveAnyData !== true && notSaveAnyData !== false) {
                throw new BadRequestException("notSaveAnyData is missing");
            }
        }

        const amount = settings.amount;
        if (amount !== undefined) {
            if (typeof amount !== "number" || +amount < 0) {
                throw new BadRequestException("amount is missing");
            }
        }

        this.service.update(settings);
        return settings;
    }
}
