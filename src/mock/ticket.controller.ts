import { TicketService } from "./ticket.service";
import { Controller, Get, Param } from "@nestjs/common";

@Controller("/v1/merchant")
export class TicketController {
    constructor(public readonly service: TicketService) {}

    @Get("/:merch_id/customer/:cust_id/ticket")
    public generateTicket(@Param("merch_id") merchantId: string, @Param("cust_id") customerId: string) {
        const ticket = this.service.generateTicket(merchantId, customerId);

        return ticket.id;
    }
}
