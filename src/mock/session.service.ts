import { Injectable } from "@nestjs/common";
import { Customer, Merchant } from "./merchant";
import { Helper } from "./utils/helper";
import config from "./config";

export interface CustomerSession {
    id?: string;
    ts: number;
    type?: string;
    merchantId?: string;
    customerId?: string;
}

@Injectable()
export class SessionService {
    private sessions: CustomerSession[] = [];

    constructor(private readonly helper: Helper) {}

    public generateSessionId(customerId: string): string {
        return this.helper.generateId(customerId, "session");
    }

    public generate(merchant: Merchant, customer: Customer, type = "customer"): CustomerSession {
        const sessionId = this.generateSessionId(customer.cust_id);
        const session: CustomerSession = {
            id: sessionId,
            ts: Date.now(),
            type,
            merchantId: merchant.merch_id,
            customerId: customer.cust_id
        };

        if (!merchant.multiple_session) {
            this.removeCustomerSession(merchant.merch_id, customer.cust_id);
        }

        this.sessions.push(session);

        customer.cust_session_id = sessionId;

        return session;
    }

    public generateWithId(
        merchant: Merchant,
        customer: Customer,
        sessionId: string,
        type = "customer"
    ): CustomerSession {
        const existingSession = this.sessions.find((session) => session.id === sessionId);
        if (existingSession) {
            return existingSession;
        }
        const session: CustomerSession = {
            id: sessionId,
            ts: Date.now(),
            type,
            merchantId: merchant.merch_id,
            customerId: customer.cust_id
        };

        if (!merchant.multiple_session) {
            this.removeCustomerSession(merchant.merch_id, customer.cust_id);
        }

        this.sessions.push(session);

        customer.cust_session_id = sessionId;

        return session;
    }

    public validate(sessionId: string): boolean {
        const session = this.getById(sessionId);

        if (!session) {
            return false;
        }

        if (Date.now() > session.ts + config.expirationTime.session) {
            this.removeById(sessionId);
            return false;
        }

        this.prolong(sessionId);

        return true;
    }

    public prolong(sessionId: string): void {
        const session = this.getById(sessionId);
        session.ts = Date.now();
    }

    public getCustomerSessions(merchantId: string, customerId: string): CustomerSession[] {
        return this.sessions.filter(
            (session) => session.merchantId === merchantId && session.customerId === customerId
        );
    }

    public removeById(sessionId: string) {
        this.sessions = this.sessions.filter((session) => session.id !== sessionId);
    }

    public removeCustomerSession(merchantId: string, customerId: string) {
        this.sessions = this.sessions.filter(
            (session) => !(session.merchantId === merchantId && session.customerId === customerId)
        );
    }

    public getById(sessionId: string): CustomerSession {
        return this.sessions.find((s) => s.id === sessionId);
    }
}
