import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import {
    MockCreatePromo,
    OperatorAwardPromRequest,
    OperatorAwardPromResponse,
    OperatorCancelTransactionRequest,
    OperatorEndGameRoundRequest,
    OperatorEndGameRoundResponse,
    OperatorFreeSpinTransactionRequest,
    OperatorFreeSpinTransactionResponse,
    OperatorGameChecksumsRequest,
    OperatorGameChecksumsResponse,
    OperatorGetBalanceRequest,
    OperatorGetBalanceResponse,
    OperatorGetFreeSpinsRequest,
    OperatorGetFreeSpinsResponse,
    OperatorGetRealityCheckRequest,
    OperatorGetRealityCheckResponse,
    OperatorLoginRequest,
    OperatorLoginResponse,
    OperatorPlatformChecksumsRequest,
    OperatorPlatformChecksumsResponse,
    OperatorPlayCheckerRequest,
    OperatorPlayCheckerResponse,
    OperatorRealityCheckDetails,
    OperatorRealityCheckPlayerChoiceRequest,
    OperatorRealityCheckPlayerChoiceResponse,
    OperatorRealityCheckStatus,
    OperatorStartGameRoundRequest,
    OperatorStartGameRoundResponse,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType
} from "@entities/operator.entities";
import { generateNumber } from "@utils/common";
import {
    b365InsufficientFunds,
    b365InvalidAmount,
    b365InvalidTransactionType,
    b365NoFreeSpinsRemaining,
    b365PromotionIsClosed,
    b365UnknownFreeSpinToken
} from "@errors/operator.errors";
import { Customer, WalletAction } from "@skywind-group/sw-integration-core/lib/mock";
import { calculation } from "@skywind-group/sw-utils";
import normalizeAmountByPrecision = calculation.normalizeAmountByPrecision;

interface MockB365Extension {
    freeBets?: {
        freeSpinToken?: string;
        numberOfLines?: number;
    };
}

@Injectable()
export class MockService extends mock.MockService {
    private static readonly WIN_PRECISION = 2;

    constructor(
        protected readonly customerService: mock.CustomerService,
        protected readonly sessionService: mock.SessionService,
        protected readonly ticketService: mock.TicketService,
        protected readonly transactionService: mock.TransactionService
    ) {
        super(customerService, sessionService, ticketService, transactionService);
    }

    public login(
        merchant: mock.Merchant,
        customer: mock.Customer,
        loginInfo: OperatorLoginRequest
    ): OperatorLoginResponse {
        const response: OperatorLoginResponse = this.authenticate(merchant, loginInfo);
        response.MessageID = loginInfo.MessageID;
        response.UTCTimeStamp = loginInfo.UTCTimeStamp;
        if (customer.bet_limit) {
            response.WagerLimit = +customer.bet_limit;
        }
        return response;
    }

    public verifyPublicToken(
        playCheckerRequest: OperatorPlayCheckerRequest,
        customer: mock.Customer
    ): OperatorPlayCheckerResponse {
        return {
            GamingId: customer.cust_id,
            ErrorDetails: null,
            MessageID: playCheckerRequest.MessageID,
            UTCTimeStamp: playCheckerRequest.UTCTimeStamp
        };
    }

    public getBalance(
        merchant: mock.Merchant,
        customer: mock.Customer,
        req?: OperatorGetBalanceRequest
    ): OperatorGetBalanceResponse {
        this.sessionService.validate(req.PrivateToken);
        return {
            Balance: { TotalAmount: normalizeAmountByPrecision(MockService.WIN_PRECISION, customer.balance.amount) },
            CurrencyCode: customer.balance.currency_code,
            ErrorDetails: null,
            MessageID: req.MessageID,
            UTCTimeStamp: req.UTCTimeStamp
        } as any;
    }

    public startGameRound(
        customer: mock.Customer,
        request: OperatorStartGameRoundRequest
    ): OperatorStartGameRoundResponse {
        const roundId = generateNumber(10);
        return {
            ErrorDetails: null,
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp,
            GameRoundID: roundId.toString()
        };
    }

    public endGameRound(customer: mock.Customer, request: OperatorEndGameRoundRequest): OperatorEndGameRoundResponse {
        return {
            ErrorDetails: null,
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp
        };
    }

    public payment(
        merchant: mock.Merchant,
        customer: mock.Customer,
        request: OperatorTransactionRequest
    ): OperatorTransactionResponse {
        if (request.Amount <= 0) {
            throw new b365InvalidAmount();
        }
        if (request.TransactionType === OperatorTransactionType.Stake) {
            return this.debit(merchant, customer, request);
        } else if (request.TransactionType === OperatorTransactionType.Return) {
            return this.credit(merchant, customer, request);
        }
        throw new b365InvalidTransactionType();
    }

    public realityCheckPlayerChoice(
        customer: mock.Customer,
        request: OperatorRealityCheckPlayerChoiceRequest
    ): OperatorRealityCheckPlayerChoiceResponse {
        customer.extraData = {
            realityCheckStatus: OperatorRealityCheckStatus.NotDue
        };

        return {
            ErrorDetails: null,
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp
        };
    }

    public realityCheck(
        customer: mock.Customer,
        request: OperatorGetRealityCheckRequest
    ): OperatorGetRealityCheckResponse {
        return {
            RealityCheck: {
                RealityCheckStatus:
                    (customer.extraData && customer.extraData.realityCheckStatus) || OperatorRealityCheckStatus.NotDue,
                Interval: 300,
                TimeSinceLastAlert: 300,
                TimeUntilNextAlert: 0,
                TimeSinceLogin: 300,
                AccountHistoryUrl: "https://www.tut.by/"
            },
            ErrorDetails: null,
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp
        };
    }

    public getFreeBets(
        customer: mock.Customer & MockB365Extension,
        request: OperatorGetFreeSpinsRequest
    ): OperatorGetFreeSpinsResponse {
        const freeBets = customer?.freeBets;
        if (freeBets?.freeSpinToken && freeBets?.freeSpinToken !== request.FreeSpinToken) {
            throw new b365UnknownFreeSpinToken();
        }
        return {
            StakeAmountPerLine: freeBets?.coin,
            NumberOfLines: freeBets?.numberOfLines,
            NumberOfSpinsRemaining: freeBets?.count,
            CurrencyCode: customer?.balance?.currency_code,
            ErrorDetails: null,
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp
        };
    }

    public createPromotion(customer: mock.Customer & MockB365Extension, request: MockCreatePromo) {
        customer.freeBets = {
            count: request.NumberOfFreeSpins,
            coin: request.StakeAmountPerLine,
            freeSpinToken: request.FreeSpinToken,
            numberOfLines: request.NumberOfLines
        };
        return customer.freeBets;
    }

    public freeBetsAward(
        customer: mock.Customer & MockB365Extension,
        request: OperatorAwardPromRequest
    ): OperatorAwardPromResponse {
        if (customer.freeBets !== undefined) {
            customer.freeBets.count += request.FreeSpinCount;
        } else {
            customer.freeBets = {
                count: request.FreeSpinCount,
                coin: 1,
                freeSpinToken: "default-free-spin-token",
                numberOfLines: 40
            };
        }
        return {
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp,
            ErrorDetails: null
        };
    }

    public freeBetsPayment(
        merchant: mock.Merchant,
        customer: mock.Customer,
        request: OperatorFreeSpinTransactionRequest
    ): OperatorFreeSpinTransactionResponse {
        this.validateFreeBets(merchant, customer);
        if (request.TransactionType === OperatorTransactionType.Stake) {
            customer.freeBets.count--;
        } else if (request.TransactionType === OperatorTransactionType.Return) {
            this.credit(merchant, customer, request);
        } else {
            throw new b365InvalidTransactionType();
        }

        return {
            ...this.getBalance(merchant, customer, request as any),
            AmountInGBP: 0,
            NumberOfSpinsRemaining: customer.freeBets.count
        };
    }

    public reportGameChecksums(request: OperatorGameChecksumsRequest): OperatorGameChecksumsResponse {
        return {
            AdmStatusCode: 1,
            AdmErrorDetails: "",
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp,
            ErrorDetails: null
        };
    }

    public reportPlatformChecksums(request: OperatorPlatformChecksumsRequest): OperatorPlatformChecksumsResponse {
        return {
            MessageID: request.MessageID,
            UTCTimeStamp: request.UTCTimeStamp,
            ErrorDetails: null
        };
    }

    protected getAuthResponse(merchant: mock.Merchant, customer: mock.Customer): OperatorLoginResponse {
        const response: OperatorLoginResponse = {
            GamingId: customer.cust_id,
            PrivateToken: customer.cust_session_id,
            CurrencyCode: customer.currency_code,
            CountryCode: customer.country,
            VIPLevel: 0,
            MessageID: undefined,
            UTCTimeStamp: undefined,
            RelaunchUrl: null,
            ErrorDetails: null
        };
        if (customer?.extraData?.allowOffers === false) {
            response.AllowOffers = false;
        }
        return response;
    }

    protected getDebitTransactionIdInRollbackRequest(body: OperatorCancelTransactionRequest): string {
        return body.ExternalTransactionIDToCancel || this.getLastDebitTransaction(body);
    }

    private getLastDebitTransaction(body: OperatorCancelTransactionRequest): string {
        const { merchantId, customerId } = this.sessionService.getById(body.PrivateToken);
        const transactions = this.transactionService.getAll(merchantId, customerId);
        // Get the id of the last transaction for this customer
        return transactions[transactions.length - 1]?.trxId;
    }

    protected getPaymentResponse(
        merchant: mock.Merchant,
        customer: mock.Customer,
        transaction: mock.Transaction,
        body: OperatorTransactionRequest | OperatorFreeSpinTransactionRequest
    ): OperatorTransactionResponse {
        return {
            Balance: { TotalAmount: normalizeAmountByPrecision(MockService.WIN_PRECISION, customer.balance.amount) },
            CurrencyCode: body.CurrencyCode,
            AmountInGBP: 0,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: null,
            UTCTimeStamp: body.UTCTimeStamp,
            MessageID: body.MessageID
        };
    }

    protected getTicketId(loginInfo: OperatorLoginRequest): string {
        return loginInfo.PublicToken;
    }

    protected getTransactionId(body: OperatorTransactionRequest & OperatorCancelTransactionRequest): string {
        return body.ExternalTransactionID || body.ExternalTransactionIDToCancel;
    }

    protected isFreebet(request: OperatorFreeSpinTransactionRequest, action: mock.WalletAction): boolean {
        return action === mock.WalletAction.Debit && request.FreeSpinToken !== undefined;
    }

    protected getAmount(request: OperatorTransactionRequest): number {
        return request.Amount;
    }

    private validateFreeBets(merchant: mock.Merchant, customer: mock.Customer) {
        // MEMO: if use_promos = false, use merchant free-bets, otherwise ours (throws error)
        if (merchant.isPromoInternal) {
            throw new b365PromotionIsClosed();
        }
    }

    protected checkInsufficientBalance(customer: Customer, body: any): void {
        if (this.isFreebet(body, WalletAction.Debit)) {
            if (!customer.freeBets || customer.freeBets.count < 1) {
                throw new b365NoFreeSpinsRemaining();
            }
        } else {
            const amount = this.getAmount(body);
            if (customer.balance.amount < Math.abs(amount)) {
                throw new b365InsufficientFunds();
            }
        }
    }
}
