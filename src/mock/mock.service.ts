import { Customer, Merchant } from "./merchant";
import { CustomerService } from "./customer.service";
import { SessionService } from "./session.service";
import { TicketService } from "./ticket.service";
import { Transaction, TransactionService, WalletAction } from "./transaction.service";
import { InsufficientBalance, InsufficientFreebets, TransactionNotFound } from "./errors";

export abstract class MockService {
    protected constructor(
        protected readonly customerService: CustomerService,
        protected readonly sessionService: SessionService,
        protected readonly ticketService: TicketService,
        protected readonly transactionService: TransactionService
    ) {}

    public abstract getBalance(merchant: Merchant, customer: Customer, body?: any): any;

    // Verify token
    public authenticate(merchant: Merchant, body: any): any {
        const ticketId = this.getTicketId(body);
        const ticket = this.ticketService.validateTicket(ticketId);
        const customer = this.customerService.getMerchantCustomer(merchant.merch_id, ticket.customerId);
        const sessionId = this.getSessionId(body);
        if (sessionId) {
            this.sessionService.generateWithId(merchant, customer, sessionId);
        } else {
            this.sessionService.generate(merchant, customer);
        }

        return this.getAuthResponse(merchant, customer);
    }

    protected getSessionId(body: any): string {
        return body.sessionId;
    }

    protected abstract getTicketId(body: any): string;

    protected abstract getAuthResponse(merchant: Merchant, customer: Customer): any;

    // Payments
    public debit(merchant: Merchant, customer: Customer, body: any): any {
        return this.makePayment(merchant, customer, WalletAction.Debit, body);
    }

    public credit(merchant: Merchant, customer: Customer, body: any): any {
        return this.makePayment(merchant, customer, WalletAction.Credit, body);
    }

    protected makePayment(merchant: Merchant, customer: Customer, action: WalletAction, body: any) {
        const amount = this.getAmount(body);
        const trxId = this.getTransactionId(body);

        let transaction = this.transactionService.getById(trxId, action);
        if (!transaction) {
            if (action === WalletAction.Debit) {
                this.checkInsufficientBalance(customer, body);
            }
            transaction = this.transactionService.create(merchant.merch_id, customer.cust_id, {
                trxId,
                amount,
                action
            });

            if (this.isFreebet(body, action)) {
                customer.freeBets.count--;
            } else {
                customer.balance.amount += transaction.amount;
            }
        }

        return this.getPaymentResponse(merchant, customer, transaction, body);
    }

    protected abstract getAmount(body: any): number;

    protected abstract getTransactionId(body: any): string;

    /**
     * Should return true in case of freebet bet only
     * */
    protected abstract isFreebet(body: any, action: WalletAction): boolean;

    protected abstract getPaymentResponse(
        merchant: Merchant,
        customer: Customer,
        transaction: Transaction,
        body: any
    ): any;

    protected checkInsufficientBalance(customer: Customer, body: any): void {
        if (this.isFreebet(body, WalletAction.Debit)) {
            if (!customer.freeBets || customer.freeBets.count < 1) {
                throw new InsufficientFreebets();
            }
        } else {
            const amount = this.getAmount(body);
            if (customer.balance.amount < Math.abs(amount)) {
                throw new InsufficientBalance();
            }
        }
    }

    public rollback(merchant: Merchant, customer: Customer, body: any): any {
        const trxId = this.getTransactionId(body);
        const debitTrxId = this.getDebitTransactionIdInRollbackRequest(body);
        const originalTransaction = this.transactionService.getById(debitTrxId, WalletAction.Debit);
        if (!originalTransaction) {
            throw new TransactionNotFound({});
        }

        let transaction = this.transactionService.getById(trxId, WalletAction.Rollback);
        if (!transaction) {
            if (!originalTransaction.rollback) {
                this.transactionService.rollback(originalTransaction);
                customer.balance.amount -= originalTransaction.amount;
            }

            transaction = this.transactionService.create(merchant.merch_id, customer.cust_id, {
                amount: Math.abs(originalTransaction.amount),
                trxId,
                action: WalletAction.Rollback
            });
        }

        return this.getPaymentResponse(merchant, customer, transaction, body);
    }

    protected abstract getDebitTransactionIdInRollbackRequest(body: any): string;
}
