import { Body, Controller, Get, HttpCode, Param, Patch, Post } from "@nestjs/common";
import { MerchantService } from "./merchant.service";
import { Customer } from "./merchant";
import { CustomerService } from "./customer.service";
import { Helper } from "./utils/helper";
import { InvalidRequest } from "./errors";

@Controller("/v1/merchant")
export class CustomerController {
    constructor(
        private readonly merchantService: MerchantService,
        private readonly customerService: CustomerService,
        private readonly helper: Helper
    ) {}

    @Get("/:merch_id/customer/:cust_id")
    @HttpCode(201)
    public getCustomer(@Param("merch_id") merch_id: string, @Param("cust_id") cust_id: string) {
        const merchant = this.merchantService.getById(merch_id, true);
        return this.customerService.getMerchantCustomer(merchant.merch_id, cust_id);
    }

    @Post("/:merch_id/customer")
    @HttpCode(201)
    public createCustomer(@Param("merch_id") merch_id: string, @Body() customer: Customer) {
        const merchant = this.merchantService.getById(merch_id, true);
        return this.customerService.createCustomer(merchant, customer);
    }

    @Patch("/:merch_id/customer/:cust_id")
    public updateCustomer(
        @Param("merch_id") merch_id: string,
        @Param("cust_id") cust_id: string,
        @Body() data: Customer
    ) {
        const merchant = this.merchantService.getById(merch_id, true);
        const customer = this.customerService.getMerchantCustomer(merchant.merch_id, cust_id);
        return this.customerService.updateCustomer(merchant, customer, data);
    }

    @Post("/:merch_id/customer/:cust_id/balance/:amount")
    public updateCustomerBalance(
        @Param("merch_id") merch_id: string,
        @Param("cust_id") cust_id: string,
        @Param("amount") amount: string
    ) {
        const customer = this.customerService.getMerchantCustomer(merch_id, cust_id);
        customer.balance.amount = this.helper.sumMajorUnits(customer.balance.amount, +amount);
        return customer;
    }

    @Post("/:merch_id/customer/:cust_id/balance/extra_data")
    public updateCustomerBalanceExtraData(
        @Param("merch_id") merch_id: string,
        @Param("cust_id") cust_id: string,
        @Body() extra_data: Record<string, any>
    ) {
        const customer = this.customerService.getMerchantCustomer(merch_id, cust_id);
        customer.balance.extra_data = extra_data;
        return customer;
    }

    @Post("/:merch_id/customer/:cust_id/freebets/:count/:coin")
    public updateCustomerFreeBets(
        @Param("merch_id") merch_id: string,
        @Param("cust_id") cust_id: string,
        @Param("count") count: string,
        @Param("coin") coin: string
    ) {
        const merchant = this.merchantService.getById(merch_id, true);
        if (merchant.isPromoInternal) {
            throw new InvalidRequest("free bets are managed by Skywind promotion system");
        }
        const customer = this.customerService.getMerchantCustomer(merch_id, cust_id);

        return this.customerService.setCustomerFreebets(customer, +coin, +count);
    }
}
