import { Injectable } from "@nestjs/common";
import { MerchantService } from "./merchant.service";
import { EntityNotFound, InvalidRequest } from "./errors";
import { Customer, Merchant } from "./merchant";
import { SettingsService } from "./settings.service";

export const defaultCustomerId = "test_customer";
export const defaultCustomerCurrency = "USD";

@Injectable()
export class CustomerService {
    constructor(private readonly merchantService: MerchantService, private readonly settingsService: SettingsService) {}

    public getDefaultCustomer(
        custId: string = defaultCustomerId,
        currencyCode: string = defaultCustomerCurrency,
        isTest = true,
        amount: number = this.settingsService.settings.amount
    ): Customer {
        return {
            cust_id: custId,
            cust_login: "",
            currency_code: currencyCode,
            status: "normal",
            bet_limit: null,
            test_cust: isTest,
            country: "CN",
            language: "en",
            balance: {
                amount,
                currency_code: currencyCode
            }
        };
    }

    public getMerchantCustomer(merchantId: string, customerId: string): Customer {
        const merchant = this.merchantService.getById(merchantId, true);

        const customer = merchant.customers[customerId];
        if (!customer) {
            throw new EntityNotFound("customer", customerId);
        }
        return customer;
    }

    public updateCustomer(merchant: Merchant, customer: Customer, dataToUpdate: Customer): Customer {
        const newCustomer: Customer = { ...customer, ...dataToUpdate };

        if (newCustomer.balance) {
            newCustomer.balance = {
                amount: newCustomer.balance.amount,
                currency_code: newCustomer.currency_code,
                extra_data: newCustomer.balance.extra_data
            };
        }
        newCustomer.status = newCustomer.status || "normal";
        newCustomer.bet_limit = newCustomer.bet_limit || null;
        newCustomer.extraData = newCustomer.extraData || null;

        merchant.customers[customer.cust_id] = newCustomer;
        return newCustomer;
    }

    public createCustomer(merchant: Merchant, customerData: Customer): Customer {
        const savedCustomer = merchant.customers[customerData.cust_id];
        if (savedCustomer) {
            return savedCustomer;
        }
        const customer: Customer = {
            ...customerData,
            status: customerData.status || "normal",
            bet_limit: customerData.bet_limit || null
        };

        customer.balance = {
            amount: 0,
            currency_code: customer.currency_code
        };

        merchant.customers[customerData.cust_id] = customer;
        return customer;
    }

    public setCustomerFreebets(customer: Customer, coin: number, count: number): Customer {
        if (coin <= 0) {
            throw new InvalidRequest("free bet coin should be positive");
        }

        if (count < 0) {
            throw new InvalidRequest("free bet count should be positive");
        }

        if (!Number.isInteger(count)) {
            throw new InvalidRequest("free bet count should be integer");
        }

        customer.freeBets = { count, coin };
        return customer;
    }
}
