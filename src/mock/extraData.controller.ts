import { Body, Controller, Get, Param, Post, Delete } from "@nestjs/common";
import { ExtraData, ExtraDataService } from "./extraData.service";

@Controller("/v1/merchant")
export class ExtraDataController {
    constructor(private readonly service: ExtraDataService) {}

    @Get("/:merch_id/extra_data")
    public getMerchantExtraData(@Param("merch_id") merchantId: string) {
        return this.service.getMerchantExtraData(merchantId);
    }

    @Get("/:merch_id/customer/:cust_id/extra_data")
    public getCustomerExtraData(@Param("merch_id") merchantId: string, @Param("cust_id") customerId: string) {
        return this.service.getCustomerExtraData(merchantId, customerId);
    }

    @Get("/:merch_id/customer/:cust_id/extra_data/:action")
    public getActionExtraData(
        @Param("merch_id") merchantId: string,
        @Param("cust_id") customerId: string,
        @Param("action") action: string
    ) {
        return this.service.getActionExtraData(merchantId, customerId, action);
    }

    @Post("/:merch_id/customer/:cust_id/extra_data/:action")
    public createExtraData(
        @Param("merch_id") merchantId: string,
        @Param("cust_id") customerId: string,
        @Param("action") action: string,
        @Body() data: ExtraData
    ) {
        return this.service.createExtraData(merchantId, customerId, action, data);
    }

    @Delete("/:merch_id/customer/:cust_id/extra_data/:action")
    public removeExtraData(
        @Param("merch_id") merchantId: string,
        @Param("cust_id") customerId: string,
        @Param("action") action: string
    ) {
        return this.service.removeExtraData(merchantId, customerId, action);
    }
}
