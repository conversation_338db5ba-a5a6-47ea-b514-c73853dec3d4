import { Controller, Get, Param, Delete, HttpCode, HttpStatus } from "@nestjs/common";
import { SessionService } from "./session.service";

@Controller("/v1/merchant")
export class SessionController {
    constructor(public readonly service: SessionService) {}

    @Get("/:merch_id/customer/:cust_id/sessions")
    public getSessions(@Param("merch_id") merchantId: string, @Param("cust_id") customerId: string) {
        return this.service.getCustomerSessions(merchantId, customerId);
    }

    @HttpCode(HttpStatus.NO_CONTENT)
    @Delete("/:merch_id/customer/:cust_id/sessions/:session_id")
    public removeSession(@Param("session_id") sessionId: string) {
        return this.service.removeById(sessionId);
    }
}
