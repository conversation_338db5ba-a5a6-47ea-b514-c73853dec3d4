import { mock } from "@skywind-group/sw-integration-core";
import { Catch, ForbiddenException, HttpException, HttpStatus } from "@nestjs/common";
import { OperatorError } from "@entities/operator.entities";
import { AmountNegativeError, PlayerSessionExpiredError, ValidationError } from "@errors/sw.errors";
import { b365ErrorCodes, b365ErrorMessages } from "@errors/operator.errors";
import { logging } from "@skywind-group/sw-utils";

@Catch()
export class ErrorsFilter extends mock.ExceptionsFilter {
    protected readonly log = logging.logger("exception-filter");

    public getStatusAndBody(exception: any): [number, any] {
        const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
        let body;

        if (exception instanceof ForbiddenException) {
            body = this.getOperatorErrorBody(
                b365ErrorCodes.AuthenticationFailed,
                b365ErrorMessages.AuthenticationFailed
            );
        } else if (exception instanceof PlayerSessionExpiredError) {
            body = this.getOperatorErrorBody(b365ErrorCodes.InvalidToken, b365ErrorMessages.InvalidToken);
        } else {
            body = this.getOperatorErrorBody(
                exception.ErrorCode || "Unknown code",
                exception.ErrorMessage || exception.message || "Mock system error",
                exception.ExtraData || {}
            );
        }
        this.log.error(exception, `Error: ${exception.ErrorCode || exception.code}`);

        return [status, body];
    }

    private getOperatorErrorBody(code, message, extraData = {}): { ErrorDetails: OperatorError[]; [key: string]: any } {
        return {
            ...extraData,
            ErrorDetails: [
                {
                    ErrorCode: code,
                    ErrorMessage: message
                }
            ]
        };
    }
}
