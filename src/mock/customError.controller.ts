import { Body, Controller, Get, HttpCode, Param, Patch, Post, Delete } from "@nestjs/common";
import { CustomError, CustomErrorService, RaiseType } from "./customError.service";

@Controller("/v1/merchant")
export class CustomErrorController {
    constructor(private readonly customErrorService: CustomErrorService) {}

    @Get("/:merch_id/error")
    public getMerchantErrors(@Param("merch_id") merchantId: string) {
        return this.customErrorService.getMerchantErrors(merchantId);
    }

    @Get("/:merch_id/customer/:cust_id/error")
    public getCustomerErrors(@Param("merch_id") merchantId: string, @Param("cust_id") customerId: string) {
        return this.customErrorService.getCustomerErrors(merchantId, customerId);
    }

    @Get("/:merch_id/customer/:cust_id/error/:action")
    public getActionErrors(
        @Param("merch_id") merchantId: string,
        @Param("cust_id") customerId: string,
        @Param("action") action: string
    ) {
        return this.customErrorService.getActionErrors(merchantId, customerId, action);
    }

    @Delete("/:merch_id/customer/:cust_id/error/:action")
    public removeActonErrors(
        @Param("merch_id") merchantId: string,
        @Param("cust_id") customerId: string,
        @Param("action") action: string
    ) {
        return this.customErrorService.removeError(merchantId, customerId, action);
    }

    @Post("/:merch_id/customer/:cust_id/error/:action/raiseType/:raiseType")
    public createError(
        @Param("merch_id") merchantId: string,
        @Param("cust_id") customerId: string,
        @Param("action") action: string,
        @Param("raiseType") raiseType: RaiseType,
        @Body() data: CustomError | CustomError[]
    ) {
        return this.customErrorService.createError(merchantId, customerId, action, raiseType, data);
    }
}
