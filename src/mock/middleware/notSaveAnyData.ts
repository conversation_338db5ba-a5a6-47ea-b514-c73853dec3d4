import { NestMiddleware } from "@nestjs/common";
import { SettingsService } from "../settings.service";
import * as http from "http";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";
import { ContentType } from "../../interfaces/contentType.enum";
import config from "../config";

export const MOCK_RESPONSE_HEADER = "x-sw-mock-response";

export abstract class NotSaveAnyDataMiddleware implements NestMiddleware {
    protected constructor(protected settingsService: SettingsService, protected parser?: XmlService) {}

    public abstract async getResponse(req: http.IncomingMessage, body: any): Promise<any>;

    public async getBody(req: http.IncomingMessage): Promise<any> {
        return new Promise((resolve) => {
            let body = "";
            req.on("data", (chunk) => (body += chunk));
            req.on("end", () => resolve(body ? this.parseBody(body) : {}));
        });
    }

    public async use(req: http.IncomingMessage, res: http.ServerResponse, next: () => void): Promise<any> {
        if (this.settingsService.loadTestModeEnable()) {
            // https://github.com/nestjs/nest/issues/1279
            res.setHeader(MOCK_RESPONSE_HEADER, "true");
            res.setHeader("Content-Type", this.settingsService.settings.contentType);
            let responseData: any;
            try {
                const body = await this.getBody(req);
                responseData = await this.getResponse(req, body);

                if (config.mockLatency && Number.isFinite(config.mockLatency)) {
                    const spreading = Number.isFinite(config.mockLatencySpreading) ? config.mockLatencySpreading : 0;
                    const sleepTime = Math.round(config.mockLatency + Math.random() * spreading);
                    await this.emulateLatency(sleepTime);
                }
            } catch (error) {
                responseData = {
                    code: error.code,
                    message: error.message,
                }
            }
            return res.end(this.stringifyBody(responseData));
        } else {
            return next();
        }
    }

    private async emulateLatency(ms: number) : Promise<number> {
        return new Promise(resolve => setTimeout(resolve, ms, ms));
    }

    private parseBody(body: string): any {
        switch (this.settingsService.settings.contentType) {
            case ContentType.ApplicationXml:
            case ContentType.TextXml:
                return this.parser.convertToObject(body);
            default:
                return JSON.parse(body);
        }
    }

    private stringifyBody(body: any): string {
        switch (this.settingsService.settings.contentType) {
            case ContentType.ApplicationXml:
            case ContentType.TextXml:
                return this.parser.convertToXML(body);
            default:
                return JSON.stringify(body);
        }
    }
}
