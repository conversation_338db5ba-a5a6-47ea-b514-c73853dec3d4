import { Injectable } from "@nestjs/common";
import config from "./config";
import { GameTokenExpired } from "./errors";
import { Helper } from "./utils/helper";
import { defaultCustomerCurrency, defaultCustomerId } from "./customer.service";

export interface Ticket {
    merchantId: string;
    customerId: string;
    creationTime: number;
    id: string;
}

@Injectable()
export class TicketService {
    public tickets: Ticket[] = [];

    constructor(private readonly helper: Helper) {}
    public generateTicket(merchantId: string, customerId: string): Ticket {
        const now = Date.now();
        const ticketId = this.helper.generateId(customerId, "ticket");
        const ticket: Ticket = {
            id: ticketId,
            merchantId,
            customerId,
            creationTime: now
        };

        this.tickets.push(ticket);

        return ticket;
    }

    public validateTicket(ticketId: string): Ticket {
        const ticket = this.getById(ticketId);
        if (!ticket || this.isTicketExpired(ticket)) {
            this.removeTicket(ticketId);
            throw new GameTokenExpired("Ticket is expired");
        }

        return this.removeTicket(ticketId);
    }

    public removeTicket(ticketId): Ticket {
        const ticket = this.getById(ticketId);
        this.tickets = this.tickets.filter((ticket) => ticket.id !== ticketId);

        return ticket;
    }

    private isTicketExpired(ticket: Ticket) {
        return config.expirationTime.ticket && Date.now() > ticket.creationTime + config.expirationTime.ticket;
    }

    public getDataFromTicket(ticketId: string): [string, string, string] {
        let [customerId, currency, hash, jurisdiction] = ticketId.split("__");
        customerId = customerId ? customerId.trim() : defaultCustomerId;
        currency = currency ? currency.trim() : defaultCustomerCurrency;
        jurisdiction = jurisdiction && jurisdiction.trim();

        return [customerId, currency, jurisdiction];
    }

    public getById(ticketId: string): Ticket {
        return this.tickets.find((t) => t.id === ticketId);
    }
}
