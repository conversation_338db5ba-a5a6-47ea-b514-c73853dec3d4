import { logging } from "@skywind-group/sw-utils";
import { Injectable } from "@nestjs/common";
import { SettingsService } from "../settings.service";
import config from "../config";

const rn = require("random-number");

const log = logging.logger("api");

@Injectable()
export class Helper {
    constructor(private readonly settingsService: SettingsService) {}
    public parseBoolean(str) {
        if (typeof str === "string") {
            str = str.toLowerCase().trim();
        }

        switch (str) {
            case "true":
            case "1":
                return true;
            case "false":
            case "0":
            case null:
                return false;
            default:
                return Boolean(str);
        }
    }

    public sumMajorUnits(amount1: number, amount2: number): number {
        if (this.settingsService.settings.aroundAmount) {
            const aggregator =
                Math.round(config.currencyUnitMultiplier * amount1) +
                Math.round(config.currencyUnitMultiplier * amount2);
            return aggregator / config.currencyUnitMultiplier;
        } else {
            return amount1 + amount2;
        }
    }

    public generateId(customerId: string, type: string): string {
        return `${type.toUpperCase()}-${customerId}-${Date.now()}-${Math.round(Math.random() * 1000000)}`;
    }
}
