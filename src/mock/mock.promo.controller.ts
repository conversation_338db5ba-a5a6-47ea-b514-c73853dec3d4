import { Body, Controller, Post, UseGuards } from "@nestjs/common";
import { MockService } from "@mock/mock.service";
import { MerchantGuardPromo } from "@mock/guards/merchant.guard";
import { CustomerGuardPromo } from "@mock/guards/customer.guard";
import { mock } from "@skywind-group/sw-integration-core";
import { MockCreatePromo } from "@entities/operator.entities";

@Controller("v1")
export class CreatePromoController {
    constructor(private readonly service: MockService) {}

    @Post("/merchant/:merchId/customer/:customerId/create-prom")
    @UseGuards(MerchantGuardPromo, CustomerGuardPromo)
    public createPromo(@mock.CustomerParam() customer: mock.Customer, @Body() body: MockCreatePromo): any {
        return this.service.createPromotion(customer, body);
    }
}
