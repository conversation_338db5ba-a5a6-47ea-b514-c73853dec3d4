import { Injectable } from "@nestjs/common";

export interface ExtraData {
    [custom_field: string]: any;
}

export interface CustomerExtraData {
    [action: string]: ExtraData[];
}

export interface MerchantExtraData {
    [customerId: string]: CustomerExtraData;
}

export interface ExtraDataContainer {
    [merchantCode: string]: MerchantExtraData;
}

@Injectable()
export class ExtraDataService {
    private readonly errors: ExtraDataContainer = {};

    public getMerchantExtraData(merchantId: string): MerchantExtraData {
        this.errors[merchantId] = this.errors[merchantId] || {};

        return this.errors[merchantId];
    }

    public getCustomerExtraData(merchantId: string, customerId: string): CustomerExtraData {
        const merchantExtraData = this.getMerchantExtraData(merchantId);
        merchantExtraData[customerId] = merchantExtraData[customerId] || {};

        return merchantExtraData[customerId];
    }

    public getActionExtraData(merchantId: string, customerId: string, action: string): ExtraData[] {
        const customerExtraData = this.getCustomerExtraData(merchantId, customerId);
        customerExtraData[action] = customerExtraData[action] || [];

        return customerExtraData[action];
    }

    public createExtraData(merchantId: string, customerId: string, action: string, data: ExtraData): ExtraData {
        const actionExtraData = this.getActionExtraData(merchantId, customerId, action);
        actionExtraData.push(data);

        return data;
    }

    public removeExtraData(merchantId: string, customerId: string, action: string): ExtraData {
        const actionExtraData = this.getCustomerExtraData(merchantId, customerId);

        const extraData = actionExtraData[action];
        actionExtraData[action] = [];

        return extraData;
    }

    public shiftExtraData(merchantId: string, customerId: string, action: string): ExtraData {
        const actionExtraData = this.getActionExtraData(merchantId, customerId, action);

        return actionExtraData.shift() || {};
    }
}
