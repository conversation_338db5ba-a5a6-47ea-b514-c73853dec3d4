import { mock } from "@skywind-group/sw-integration-core";
import { Injectable } from "@nestjs/common";
import { OperatorTransactionRequest } from "@entities/operator.entities";

const complexActions = ["transaction", "freeBetsTransaction"];

@Injectable()
export class MockErrorInterceptor extends mock.CustomErrorInterceptor {
    getHandlerName(request: any, context: any): string {
        const originalAction = super.getHandlerName(request, context);
        return complexActions.includes(originalAction)
            ? this.getInnerAction(request.body, originalAction)
            : originalAction;
    }

    getInnerAction(request: OperatorTransactionRequest, prefix: string): string {
        return `${prefix}:${request.TransactionType.toLowerCase()}`;
    }
}
