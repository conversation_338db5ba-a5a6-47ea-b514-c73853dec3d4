import { Body, Controller, Get, Post, Put, Query, UseGuards, UseInterceptors } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { MockService } from "./mock.service";
import {
    OperatorCancelTransactionRequest,
    OperatorCancelTransactionResponse,
    OperatorEndGameRoundRequest,
    OperatorEndGameRoundResponse,
    OperatorGetBalanceRequest,
    OperatorGetBalanceResponse,
    OperatorLoginRequest,
    OperatorLoginResponse,
    OperatorPlayCheckerRequest,
    OperatorPlayCheckerResponse,
    OperatorStartGameRoundRequest,
    OperatorStartGameRoundResponse,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorRealityCheckPlayerChoiceRequest,
    OperatorRealityCheckPlayerChoiceResponse,
    OperatorFreeSpinTransactionRequest,
    OperatorFreeSpinTransactionResponse,
    OperatorGetFreeSpinsRequest,
    OperatorGetFreeSpinsResponse,
    OperatorGetRealityCheckRequest,
    OperatorGetRealityCheckResponse,
    OperatorGameChecksumsRequest,
    OperatorGameChecksumsResponse,
    OperatorPlatformChecksumsRequest,
    OperatorPlatformChecksumsResponse,
    OperatorAwardPromRequest,
    OperatorAwardPromResponse
} from "@entities/operator.entities";
import { BasicAuthGuard } from "@mock/guards/auth.guard";
import { CustomerGuard } from "@mock/guards/customer.guard";
import { Customer, CustomerParam } from "@skywind-group/sw-integration-core/lib/mock";
import { MerchantGuard } from "@mock/guards/merchant.guard";
import { LoginGuard } from "@mock/guards/login.guard";
import { MockErrorInterceptor } from "@mock/mock.interceptor";

@Controller("v1/mock/API/:regulation")
@UseInterceptors(MockErrorInterceptor, mock.ExtraDataInterceptor)
@UseGuards(BasicAuthGuard)
export class MockController {
    constructor(private readonly service: MockService) {}

    @Post("Login")
    @UseGuards(LoginGuard)
    public login(
        @mock.MerchantParam() merchant: mock.Merchant,
        @CustomerParam() customer: Customer,
        @Body() loginRequest: OperatorLoginRequest
    ): OperatorLoginResponse {
        return this.service.login(merchant, customer, loginRequest);
    }

    @Post("PlayChecker")
    @UseGuards(LoginGuard)
    public verifyPublicToken(
        @CustomerParam() customer: Customer,
        @Body() playCheckerRequest: OperatorPlayCheckerRequest
    ): OperatorPlayCheckerResponse {
        return this.service.verifyPublicToken(playCheckerRequest, customer);
    }

    @Get("Balance")
    @UseGuards(MerchantGuard, CustomerGuard)
    public getBalance(
        @CustomerParam() customer: Customer,
        @Query() request: OperatorGetBalanceRequest
    ): OperatorGetBalanceResponse {
        return this.service.getBalance(undefined, customer, request);
    }

    @Post("GameRound")
    @UseGuards(MerchantGuard, CustomerGuard)
    public startGameRound(
        @CustomerParam() customer: Customer,
        @Body() request: OperatorStartGameRoundRequest
    ): OperatorStartGameRoundResponse {
        return this.service.startGameRound(customer, request);
    }

    @Put("GameRound")
    @UseGuards(MerchantGuard, CustomerGuard)
    public endGameRound(
        @CustomerParam() customer: Customer,
        @Body() request: OperatorEndGameRoundRequest
    ): OperatorEndGameRoundResponse {
        return this.service.endGameRound(customer, request);
    }

    @Post("Transaction")
    @UseGuards(MerchantGuard, CustomerGuard)
    public transaction(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: OperatorTransactionRequest
    ): OperatorTransactionResponse {
        return this.service.payment(merchant, customer, body);
    }

    @Post(["CancelTransaction", "CancelGameRound"])
    @UseGuards(MerchantGuard, CustomerGuard)
    public cancelTransaction(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: OperatorCancelTransactionRequest | OperatorEndGameRoundRequest
    ): OperatorCancelTransactionResponse | OperatorEndGameRoundResponse {
        return this.service.rollback(merchant, customer, body);
    }

    @Post("RealityCheck")
    @UseGuards(MerchantGuard, CustomerGuard)
    public realityCheckPlayerChoice(
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: OperatorRealityCheckPlayerChoiceRequest
    ): OperatorRealityCheckPlayerChoiceResponse {
        return this.service.realityCheckPlayerChoice(customer, body);
    }

    @Get("RealityCheck")
    @UseGuards(MerchantGuard, CustomerGuard)
    public realityCheck(
        @mock.CustomerParam() customer: mock.Customer,
        @Query() request: OperatorGetRealityCheckRequest
    ): OperatorGetRealityCheckResponse {
        return this.service.realityCheck(customer, request);
    }

    @Get("FreeSpin")
    @UseGuards(MerchantGuard, CustomerGuard)
    public getFreeBets(
        @mock.CustomerParam() customer: mock.Customer,
        @Query() request: OperatorGetFreeSpinsRequest
    ): OperatorGetFreeSpinsResponse {
        return this.service.getFreeBets(customer, request);
    }

    @Post("FreeSpin/Transaction")
    @UseGuards(MerchantGuard, CustomerGuard)
    public freeBetsTransaction(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: OperatorFreeSpinTransactionRequest
    ): OperatorFreeSpinTransactionResponse {
        return this.service.freeBetsPayment(merchant, customer, body);
    }

    @Post("/FreeSpin/Award")
    @UseGuards(MerchantGuard, CustomerGuard)
    public freeBetsAward(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: OperatorAwardPromRequest
    ): OperatorAwardPromResponse {
        return this.service.freeBetsAward(customer, body);
    }

    @Post("ReportGame/Checksum")
    public reportGameChecksums(@Body() body: OperatorGameChecksumsRequest): OperatorGameChecksumsResponse {
        return this.service.reportGameChecksums(body);
    }

    @Post("ReportPlatform/Checksum")
    public reportPlatformChecksums(@Body() body: OperatorPlatformChecksumsRequest): OperatorPlatformChecksumsResponse {
        return this.service.reportPlatformChecksums(body);
    }
}
