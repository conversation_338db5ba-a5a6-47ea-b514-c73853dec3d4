"use strict";

const config = {
    environment: process.env.NODE_ENV || "development",

    logLevel: process.env.LOG_LEVEL || "info",

    logParams: {
        secureKeys: ["merch_pwd"],
        allowedHeaders: process.env.ALLOWED_HEADERS_TO_LOG || ""
    },

    currencyUnitMultiplier: 100,

    expirationTime: {
        ticket: +process.env.TICKET_EXPIRATION_MS || 5 * 60 * 1000, // 5 min,
        session: +process.env.SESSION_EXPIRATION_MS || 5 * 60 * 1000 // 5 min
    },

    mockLatency: +process.env.IPM_MOCK_LATENCY || 0,
    mockLatencySpreading: +process.env.IPM_MOCK_LATENCY_SPREADING || (+process.env.IPM_MOCK_LATENCY || 0) / 10,
    mockLatencyPathMask: process.env.IPM_MOCK_LATENCY_PATH_MASK || "/api/*",

    defaultSettings: {
        decreaseLoad: process.env.DECREASE_LOAD === "true", // false
        notSaveAnyData: process.env.NOT_SAVE_ANY_DATA === "true", // false
        amount: +process.env.MOCK_SETTINGS_CUSTOMER_AMOUNT || 1000000,
        contentType: process.env.MOCK_CONTENT_TYPE || "application/json"
    },

    serverName: "ipm",

    bodyParserJsonLimit: +process.env.BODY_PARSER_JSON_LIMIT || 5242880,
    bodyParserUrlLimit: +process.env.BODY_PARSER_URL_LIMIT || 5242880,
    compressionThreshold: +process.env.COMPRESSION_THRESHOLD || 1024,

    queryLogging: process.env.POSTGRES_QUERY_LOGGING === "true",

    specialFeatures: {
        phantomCompanyTournamentTicket: process.env.PHANTOM_COMPANY_TOURNAMENT_TICKET === "true"
    },

    auth: {
        username: process.env.BASE_AUTH_USERNAME || "skywind",
        password: process.env.BASE_AUTH_PASSWORD || "91NTfJbsPGK8N0oX"
    },

    operator: {
        secretKey: process.env.OPERATOR_SECRET_KEY || "5FbzFNDHYRghixCwd8NTNJn9Tq4cl1Ue",
        cryptoAlgorythm: process.env.OPERATOR_CRYPTO_ALG || "sha256"
    }
};

export default config;
