import { Injectable } from "@nestjs/common";
import { Customer, Merchant, MerchantInfo, Merchants } from "./merchant";
import { Helper } from "./utils/helper";
import { EntityNotFound, InvalidRequest } from "./errors";

@Injectable()
export class MerchantService {
    private merchants: Merchants = {};

    constructor(private readonly helper: Helper) {}

    public getAll(): Merchants {
        return this.merchants;
    }

    public setAll(newMerchants: Merchants): void {
        this.merchants = newMerchants;
    }

    public getById(merchantId: string = "", raiseErrorIfNotExists: boolean = false): Merchant {
        const merchant = this.merchants[merchantId];

        if (!merchant && raiseErrorIfNotExists) {
            throw new EntityNotFound("Entity", merchantId);
        }

        return merchant;
    }

    public setById(merchantId: string, merchantData: Merchant) {
        this.merchants[merchantId] = merchantData;
    }

    public getCustomerById(merchantId: string, customerId: string): Customer {
        const merchant: Merchant = this.merchants[merchantId];
        if (merchant) {
            return merchant.customers && merchant.customers[customerId];
        }
    }

    public getMerchantWithPassword(merch_id: string, merch_pwd: string) {
        const merchant = this.getById(merch_id);

        if (merchant.merch_pwd !== merch_pwd) {
            throw new InvalidRequest("Invalid password");
        }

        return merchant;
    }

    public updateMerchant(merchantId: string, data: MerchantInfo): Merchant {
        if (typeof data.merch_pwd !== "undefined") {
            this.getById(merchantId).merch_pwd = data.merch_pwd;
        }

        if (typeof data.isPromoInternal !== "undefined") {
            this.getById(merchantId).isPromoInternal = this.helper.parseBoolean(data.isPromoInternal);
        }

        if (typeof data.multiple_session !== "undefined") {
            this.getById(merchantId).multiple_session = this.helper.parseBoolean(data.multiple_session);
        }

        return this.getById(merchantId);
    }

    public createMerchant(merchantId: string, data: MerchantInfo): Merchant {
        let isPromoInternal: boolean = false;
        if (typeof data.isPromoInternal !== "undefined") {
            isPromoInternal = this.helper.parseBoolean(data.isPromoInternal);
        }

        let multipleSession: boolean = false;
        if (typeof data.multiple_session !== "undefined") {
            multipleSession = this.helper.parseBoolean(data.multiple_session);
        }

        if (!this.getById(merchantId)) {
            this.setById(merchantId, {
                merch_id: merchantId,
                merch_pwd: data.merch_pwd,
                customers: {},
                isPromoInternal: !!isPromoInternal,
                multiple_session: multipleSession
            });
        }
        return this.getById(merchantId);
    }
}
