import { Body, Controller, Post, UseFilters } from "@nestjs/common";
import { RegulatoryActionRequest } from "../interfaces/payment.service";
import { PaymentFacade } from "./payment.facade";
import { ErrorFilter } from "../utils/error.filter";

@UseFilters(ErrorFilter)
@Controller("performRegulatoryAction")
export class RegulationController {
    constructor(private readonly paymentFacade: PaymentFacade) {}

    @Post("/")
    public async performRegulatoryAction(@Body() req: RegulatoryActionRequest): Promise<any> {
        return this.paymentFacade.performRegulatoryAction(req);
    }
}
