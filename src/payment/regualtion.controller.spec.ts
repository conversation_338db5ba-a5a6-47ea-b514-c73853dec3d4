import { Test, TestingModule } from "@nestjs/testing";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { PaymentFacade } from "./payment.facade";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { RegulationController } from "./regulation.controller";

@suite()
class RegulationControllerSpec {
    private controller: RegulationController;
    private facadeStub: SinonStubbedInstance<PaymentFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(PaymentFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [RegulationController],
            providers: [
                {
                    provide: PaymentFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<RegulationController>(RegulationController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testPerformRegulatoryAction() {
        const response: any = {
            data: "action data"
        };
        const req = {};
        this.facadeStub.performRegulatoryAction.resolves(response);
        const result = await this.controller.performRegulatoryAction(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.performRegulatoryAction.lastCall.args).deep.eq([req]);
    }
}
