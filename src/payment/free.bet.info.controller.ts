import { Body, Controller, Post, UseFilters } from "@nestjs/common";
import { ErrorFilter } from "../utils/error.filter";
import { PaymentFacade } from "./payment.facade";
import { GetFreeBetInfoRequest } from "..";
import { FreeBetInfo } from "@skywind-group/sw-wallet-adapter-core";

@UseFilters(ErrorFilter)
@Controller("get-freebet")
export class FreeBetInfoController {
    constructor(private readonly paymentFacade: PaymentFacade) {}

    @Post("/")
    public async getFreeBetInfo(@Body() req: GetFreeBetInfoRequest): Promise<FreeBetInfo> {
        return this.paymentFacade.getFreeBetInfo(req);
    }
}
