import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import { stub } from "sinon";
import { expect, use } from "chai";
import { ModuleRef } from "@nestjs/core";
import { getHandlerName } from "../utils/handler";
import { HttpGateway } from "../interfaces/http";
import { HttpPaymentServiceAdapter } from "./httpPayment.adapter";
import { CheckProviderService } from "../utils/checkExistsSupport";
import { OperationForbidden } from "../errors";
import "chai-as-promised";

use(require("chai-as-promised"));

@suite()
class HttpPaymentServiceAdapterSpec {
    public async after() {
        sinon.restore();
    }

    @test
    public async testCommitPayment() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const commitPayment = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("commitPayment")).returns(true);
        moduleRef.get.withArgs(getHandlerName("commitPayment")).returns(commitPayment);

        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.commitPayment(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitPayment);
    }

    @test
    public async testBetPayment() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const commitBetPayment = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("commitBetPayment")).returns(true);
        moduleRef.get.withArgs(getHandlerName("commitBetPayment")).returns(commitBetPayment);

        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.commitBetPayment(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitBetPayment);
    }

    @test
    public async testCommitFreBetPayment() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const commitFreeBet = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("commitFreeBetPayment")).returns(true);
        moduleRef.get.withArgs(getHandlerName("commitFreeBetPayment")).returns(commitFreeBet);

        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment", request: { freeBetCoin: 1 } };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.commitFreeBetPayment(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitFreeBet);
    }

    @test
    public async testWinPayment() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const commitWinPayment = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("commitWinPayment")).returns(true);
        moduleRef.get.withArgs(getHandlerName("commitWinPayment")).returns(commitWinPayment);
        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.commitWinPayment(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitWinPayment);
    }

    @test
    public async testGetBalance() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const getBalances = {
            build: () => null,
            parse: () => null
        };

        checkProviderStub.exists.withArgs(getHandlerName("getBalances")).returns(true);
        moduleRef.get.withArgs(getHandlerName("getBalances")).returns(getBalances);
        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.getBalances(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(getBalances);
    }

    @test
    public async tetJackpotWin() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const commitJackpotWin = {
            build: () => null,
            parse: () => null
        };

        checkProviderStub.exists.withArgs(getHandlerName("commitJackpotWinPayment")).returns(true);
        moduleRef.get.withArgs(getHandlerName("commitJackpotWinPayment")).returns(commitJackpotWin);
        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.commitJackpotWinPayment(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitJackpotWin);
    }

    @test
    public async testBonusPayment() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const testBonusPayment = {
            build: () => null,
            parse: () => null
        };

        checkProviderStub.exists.withArgs(getHandlerName("commitBonusPayment")).returns(true);
        moduleRef.get.withArgs(getHandlerName("commitBonusPayment")).returns(testBonusPayment);

        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.commitBonusPayment(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(testBonusPayment);
    }

    @test
    public async testTransferIn() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const transferIn = {
            build: () => null,
            parse: () => null
        };

        checkProviderStub.exists.withArgs(getHandlerName("transferIn")).returns(true);
        moduleRef.get.withArgs(getHandlerName("transferIn")).returns(transferIn);
        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.transferIn(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(transferIn);
    }

    @test
    public async testTransferOut() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const transferOut = {
            build: () => null,
            parse: () => null
        };

        checkProviderStub.exists.withArgs(getHandlerName("transferOut")).returns(true);
        moduleRef.get.withArgs(getHandlerName("transferOut")).returns(transferOut);
        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.transferOut(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(transferOut);
    }

    @test
    public async testRefundBet() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const refundBetPayment = {
            build: () => null,
            parse: () => null
        };

        checkProviderStub.exists.withArgs(getHandlerName("refundBetPayment")).returns(true);
        moduleRef.get.withArgs(getHandlerName("refundBetPayment")).returns(refundBetPayment);
        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "payment" };
        const res = { main: 1000 };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.refundBetPayment(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(refundBetPayment);
    }

    @test
    public async testForbidden() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);

        const refundBetPayment = {
            build: () => null,
            parse: () => null
        };

        checkProviderStub.exists.withArgs(getHandlerName("refundBetPayment")).returns(false);
        moduleRef.get.withArgs(getHandlerName("refundBetPayment")).returns(refundBetPayment);
        const serviceAdapter = new HttpPaymentServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        await expect(serviceAdapter.refundBetPayment({} as any)).to.be.rejectedWith(OperationForbidden);
    }
}
