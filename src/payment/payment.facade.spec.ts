import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import { expect, use } from "chai";
import "chai-as-promised";
import { OperationForbidden } from "../errors";
import { PaymentFacade } from "./payment.facade";
import { HttpPaymentServiceAdapter } from "./httpPayment.adapter";
import {
    BrandFinalizationType,
    ConnectionError,
    PAYMENT_TYPE,
    RequireRefundBetError
} from "@skywind-group/sw-wallet-adapter-core";

use(require("chai-as-promised"));

@suite()
class PaymentFacadeSpec {
    public async after() {
        sinon.restore();
    }

    @test
    public async commitPayment() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub);

        const req = { operation: "payment", request: {} };
        const res = { url: "http://localhost" };
        paymentStub.commitPayment.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("commitPayment").returns(true);
        const result = await facade.commitPayment(req as any);
        expect(result).eq(res);
        expect(paymentStub.commitPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitSplitPaymentWithOfflinePayments() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub,
            paymentStub,
            paymentStub,
            paymentStub,
            paymentStub,
            paymentStub,
            paymentStub,
            paymentStub);

        const req = { operation: "payment", request: { finalizationType: BrandFinalizationType.OFFLINE_PAYMENTS } };
        const res = { url: "http://localhost" };
        paymentStub.finalizeWithOfflinePayments.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("finalizeWithOfflinePayments").returns(true);
        const result = await facade.commitSplitPayment(PAYMENT_TYPE.WIN, req as any);
        expect(result).eq(res);
        expect(paymentStub.finalizeWithOfflinePayments.lastCall.args[0]).eq(req);
    }

    @test
    public async commitBetPayment() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub);

        const req = { operation: "payment", request: {} };
        const res = { url: "http://localhost" };
        paymentStub.commitBetPayment.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("commitBetPayment").returns(true);
        const result = await facade.commitSplitPayment(PAYMENT_TYPE.BET, req as any);
        expect(result).eq(res);
        expect(paymentStub.commitBetPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitFreeBetPayment() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, undefined, undefined, undefined, undefined, paymentStub);

        const req = { operation: "payment", request: { freeBetCoin: 1 } };
        const res = { url: "http://localhost" };
        paymentStub.commitFreeBetPayment.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("commitFreeBetPayment").returns(true);
        const result = await facade.commitSplitPayment(PAYMENT_TYPE.BET, req as any);
        expect(result).eq(res);
        expect(paymentStub.commitFreeBetPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitFreeBetWinPayment() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, undefined, undefined, undefined, undefined, paymentStub);

        const req = { operation: "payment", request: { freeBetCoin: 1 } };
        const res = { url: "http://localhost" };
        paymentStub.commitFreeBetWinPayment.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("commitFreeBetWinPayment").returns(true);
        const result = await facade.commitSplitPayment(PAYMENT_TYPE.WIN, req as any);
        expect(result).eq(res);
        expect(paymentStub.commitFreeBetWinPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitWinPayment() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub);

        const req = { operation: "payment", request: {} };
        const res = { url: "http://localhost" };
        paymentStub.commitWinPayment.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("commitWinPayment").returns(true);
        const result = await facade.commitSplitPayment(PAYMENT_TYPE.WIN, req as any);
        expect(result).eq(res);
        expect(paymentStub.commitWinPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitBetRequireRefundTrue() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, paymentStub, paymentStub, paymentStub);

        const req = { operation: "payment", request: {} };
        const res = { url: "http://localhost" };
        paymentStub.commitBetPayment.withArgs(req as any).throws(new ConnectionError(""));
        paymentStub.isImplemented.withArgs("commitBetPayment").returns(true);
        paymentStub.isImplemented.withArgs("refundBetPayment").returns(true);
        await expect(facade.commitSplitPayment(PAYMENT_TYPE.BET, req as any)).to.be.rejectedWith(RequireRefundBetError);
        expect(paymentStub.commitBetPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitBetRequireRefundFalse() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, paymentStub, paymentStub, paymentStub);

        const req = { operation: "payment", request: {} };
        const res = { url: "http://localhost" };
        paymentStub.commitBetPayment.withArgs(req as any).throws(new ConnectionError(""));
        paymentStub.isImplemented.withArgs("commitBetPayment").returns(true);
        paymentStub.isImplemented.withArgs("refundBetPayment").returns(false);
        await expect(facade.commitSplitPayment(PAYMENT_TYPE.BET, req as any)).to.be.rejectedWith(ConnectionError);
        expect(paymentStub.commitBetPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitJackpotPayment() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, paymentStub);

        const req = { operation: "payment", request: { isJPWin: true } };
        const res = { url: "http://localhost" };
        paymentStub.commitJackpotWinPayment.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("commitJackpotWinPayment").returns(true);
        const result = await facade.commitPayment(req as any);
        expect(result).eq(res);
        expect(paymentStub.commitJackpotWinPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async commitBonusPayment() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, paymentStub, paymentStub, paymentStub, paymentStub);

        const req = { operation: "payment", request: {} };
        const res = { url: "http://localhost" };
        paymentStub.commitBonusPayment.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("commitBonusPayment").returns(true);
        const result = await facade.commitBonusPayment(req as any);
        expect(result).eq(res);
        expect(paymentStub.commitBonusPayment.lastCall.args[0]).eq(req);
    }

    @test
    public async transferIn() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, paymentStub, paymentStub, paymentStub, paymentStub);

        const req = { request: { operation: "transfer-in" } };
        const res = { url: "http://localhost" };
        paymentStub.transferIn.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("transferIn").returns(true);
        const result = await facade.transfer(req as any);
        expect(result).eq(res);
        expect(paymentStub.transferIn.lastCall.args[0]).eq(req);
    }

    @test
    public async transferOut() {
        const paymentStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(paymentStub, paymentStub, paymentStub, paymentStub, paymentStub);

        const req = { request: { operation: "transfer-out" } };
        const res = { url: "http://localhost" };
        paymentStub.transferOut.withArgs(req as any).resolves(res as any);
        paymentStub.isImplemented.withArgs("transferOut").returns(true);
        const result = await facade.transfer(req as any);
        expect(result).eq(res);
        expect(paymentStub.transferOut.lastCall.args[0]).eq(req);
    }

    @test
    public async forbidden_1() {
        const startGameStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(startGameStub, startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        startGameStub.refundBetPayment.withArgs(req as any).resolves({} as any);
        await expect(facade.refundBetPayment(req as any)).to.be.rejectedWith(OperationForbidden);
    }

    @test
    public async forbidden_2() {
        const startGameStub = sinon.createStubInstance(HttpPaymentServiceAdapter);

        const facade = new PaymentFacade(startGameStub, startGameStub, startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        startGameStub.refundBetPayment.withArgs(req as any).resolves({} as any);
        await expect(facade.refundBetPayment(req as any)).to.be.rejectedWith(OperationForbidden);
    }
}
