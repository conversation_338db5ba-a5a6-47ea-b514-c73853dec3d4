import { DynamicModule, Module, Provider } from "@nestjs/common";
import { PaymentController } from "./payment.controller";
import { Names } from "../names";
import { getPaymentHandlers, PaymentModuleConfig, PaymentService } from "../interfaces/payment.service";
import { PaymentFacade } from "./payment.facade";
import { BalanceController } from "./balance.controller";
import { HttpGateway } from "../interfaces/http";
import { getHandlerName } from "../utils/handler";
import { Imports, ServiceProvider } from "../interfaces/types";
import { safeInject } from "../utils/saveInject";
import { ErrorFilter } from "../utils/error.filter";
import { CheckProviderService } from "../utils/checkExistsSupport";
import { HttpPaymentServiceAdapter } from "./httpPayment.adapter";
import { BrokenGameController } from "./brokengame.controller";
import { RegulationController } from "./regulation.controller";
import { FreeBetInfoController } from "./free.bet.info.controller";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";
import { XmlHttpGateway } from "..";

/**
 * Base module to handle all payment requests
 * The module is dynamic.
 * The developer responsibility is to specify service provider to handle payment requests
 */
@Module({
    controllers: [
        PaymentController,
        BalanceController,
        BrokenGameController,
        RegulationController,
        FreeBetInfoController
    ]
})
export class PaymentModule {
    /**
     * Simple method to create a method with full payment service support
     *
     * @param paymentServiceProvider the provider of the full PaymentService interface
     * @param imports additional imports, to this module
     */
    public static register(paymentServiceProvider: ServiceProvider<PaymentService>, imports?: Imports): DynamicModule {
        return {
            module: PaymentModule,
            imports,
            providers: [
                ...safeInject(
                    paymentServiceProvider,
                    Names.PaymentSupportService,
                    Names.JackpotPaymentSupportService,
                    Names.BonusPaymentSupportService,
                    Names.RefundBetSupportService,
                    Names.TransferSupportService
                ),
                ErrorFilter,
                CheckProviderService,
                PaymentFacade,
                XmlService
            ]
        };
    }

    /**
     * The advanced method to config payments:
     * - ability to specify http gateway configuration
     * - split functionality for different payment method types: bonus/split/jackpot/transfer/etc.
     * - auto inject the @HttpPaymentHandler
     *
     * @param config
     * @param imports - additional imports
     */
    public static registerWithConfig(config: PaymentModuleConfig, imports?: Imports): DynamicModule {
        const providers: Provider[] = [];
        const exports = [];

        PaymentModule.registerOrUseDefault(config.payment, Names.PaymentSupportService, providers, config);
        PaymentModule.registerOrUseDefault(
            config.jackpotPayment,
            Names.JackpotPaymentSupportService,
            providers,
            config
        );
        PaymentModule.registerOrUseDefault(config.bonusPayment, Names.BonusPaymentSupportService, providers, config);
        PaymentModule.registerOrUseDefault(config.refund, Names.RefundBetSupportService, providers, config);
        PaymentModule.registerOrUseDefault(config.transfer, Names.TransferSupportService, providers, config);
        PaymentModule.registerOrUseDefault(config.freeBetPayment, Names.FreeBetPaymentSupport, providers, config);
        PaymentModule.registerOrUseDefault(config.brokenGame, Names.BrokenGameSupportService, providers, config);
        PaymentModule.registerOrUseDefault(config.regulation, Names.RegulationSupportService, providers, config);
        PaymentModule.registerOrUseDefault(config.freeBetInfo, Names.FreeBetInfoService, providers, config);
        PaymentModule.registerOrUseDefault(config.offlineBonusPayment, Names.OfflineBonusPaymentService, providers, config);

        if (config.http) {
            providers.push(HttpGateway);
            exports.push(HttpGateway);
            providers.push(XmlHttpGateway);
            exports.push(XmlHttpGateway);
            providers.push(
                {
                    provide: Names.HttpGatewayConfig,
                    useValue: config.http.gatewayConfig
                },
                {
                    provide: Names.XmlService,
                    useValue: new XmlService()
                }
            );
            if (config.http.handlers) {
                for (const handler of Object.keys(config.http.handlers)) {
                    providers.push({
                        provide: getHandlerName(handler),
                        ...config.http.handlers[handler]
                    });
                }
            } else {
                for (const provider of getPaymentHandlers()) {
                    const keys: string[] = Reflect.getMetadata("http_handler", provider);
                    keys.forEach((key) => providers.push({ provide: getHandlerName(key), useClass: provider }));
                }
            }
        }

        return {
            module: PaymentModule,
            imports,
            providers: [...providers, ErrorFilter, CheckProviderService, PaymentFacade, XmlService],
            exports
        };
    }

    private static registerOrUseDefault(
        provider: ServiceProvider<any>,
        serviceName: Names,
        providers: Provider[],
        config: PaymentModuleConfig
    ) {
        if (provider) {
            providers.push(...safeInject(provider, serviceName));
        } else if (config.http) {
            providers.push({
                provide: serviceName,
                useClass: HttpPaymentServiceAdapter
            } as Provider);
        }
    }
}
