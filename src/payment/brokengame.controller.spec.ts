import { Test, TestingModule } from "@nestjs/testing";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { PaymentFacade } from "./payment.facade";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BrokenGameController } from "./brokengame.controller";

@suite()
class BalanceControllerSpec {
    private controller: BrokenGameController;
    private facadeStub: SinonStubbedInstance<PaymentFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(PaymentFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [BrokenGameController],
            providers: [
                {
                    provide: PaymentFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<BrokenGameController>(BrokenGameController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testFinalizeGame() {
        const response: Balance = {
            main: 10000
        };
        const req = {};
        this.facadeStub.finalizeGame.resolves(response);
        const result = await this.controller.finalizeGame(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.finalizeGame.lastCall.args).deep.eq([req]);
    }
}
