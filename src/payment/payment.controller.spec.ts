import { Test, TestingModule } from "@nestjs/testing";
import { PaymentController } from "./payment.controller";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { PaymentFacade } from "./payment.facade";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { Balance, OfflineBonusInfo, PAYMENT_TYPE } from "@skywind-group/sw-wallet-adapter-core";

@suite()
class PaymentControllerSpec {
    private controller: PaymentController;
    private facadeStub: SinonStubbedInstance<PaymentFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(PaymentFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [PaymentController],
            providers: [
                {
                    provide: PaymentFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<PaymentController>(PaymentController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testCommitPayment() {
        const response: Balance = {
            main: 10000
        };
        const req = {
            operation: "payment"
        };
        this.facadeStub.commitPayment.resolves(response);
        const result = await this.controller.commitPayment(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.commitPayment.lastCall.args).deep.eq([req]);
    }

    @test
    public async testCommitSpitPayment() {
        const response: Balance = {
            main: 20000
        };
        const req = {
            operation: "bet"
        };
        this.facadeStub.commitSplitPayment.resolves(response);
        const result = await this.controller.commitSplitPayment(PAYMENT_TYPE.BET, req as any);
        expect(result).eq(response);
        expect(this.facadeStub.commitSplitPayment.lastCall.args).deep.eq([PAYMENT_TYPE.BET, req]);
    }

    @test
    public async testCommitBonusPayment() {
        const response: Balance = {
            main: 30000
        };
        const req = {
            operation: "bonus-payment"
        };
        this.facadeStub.commitBonusPayment.resolves(response);
        const result = await this.controller.commitBonusPayment(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.commitBonusPayment.lastCall.args).deep.eq([req]);
    }

    @test
    public async testTransfer() {
        const response: Balance = {
            main: 40000
        };
        const req = {
            operation: "transfer-in"
        };
        this.facadeStub.transfer.resolves(response);
        const result = await this.controller.transfer(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.transfer.lastCall.args).deep.eq([req]);
    }

    @test
    public async testRefund() {
        const response: Balance = {
            main: 50000
        };
        const req = {
            operation: "refund-bet"
        };
        this.facadeStub.refundBetPayment.resolves(response);
        const result = await this.controller.refundBetPayment(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.refundBetPayment.lastCall.args).deep.eq([req]);
    }

    @test
    public async testOfflineBonusPayment() {
        const response: OfflineBonusInfo = {
            balance: { main: 1000 },
            externalTrxId: "666"

        };
        const req: any = {};
        this.facadeStub.commitOfflineBonusPayment.resolves(response);
        const result = await this.controller.commitOfflineBonusPayment(req);
        expect(result).eq(response);
        expect(this.facadeStub.commitOfflineBonusPayment.lastCall.args).deep.eq([req]);
    }
}
