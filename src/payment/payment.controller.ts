import { Body, Controller, Param, Post, UseFilters } from "@nestjs/common";
import {
    CommitBonusPaymentRequest, CommitOfflineBonusPaymentRequest,
    CommitPaymentRequest,
    RefundRequest,
    TransferRequest
} from "../interfaces/payment.service";
import {
    Balance,
    OfflineBonusInfo,
    PAYMENT_TYPE
} from "@skywind-group/sw-wallet-adapter-core";
import { PaymentFacade } from "./payment.facade";
import { ErrorFilter } from "../utils/error.filter";

@UseFilters(ErrorFilter)
@Controller("payments")
export class PaymentController {
    constructor(private readonly paymentFacade: PaymentFacade) {}

    @Post("/")
    public async commitPayment(@Body() req: CommitPaymentRequest): Promise<Balance> {
        return this.paymentFacade.commitPayment(req);
    }

    @Post("/bonus")
    public async commitBonusPayment(@Body() req: CommitBonusPaymentRequest): Promise<Balance> {
        return this.paymentFacade.commitBonusPayment(req);
    }

    @Post("/transfer")
    public async transfer(@Body() req: TransferRequest): Promise<Balance> {
        return this.paymentFacade.transfer(req);
    }

    @Post("/:paymentType")
    public async commitSplitPayment(
        @Param("paymentType") type: PAYMENT_TYPE,
        @Body() req: CommitPaymentRequest
    ): Promise<Balance> {
        return this.paymentFacade.commitSplitPayment(type, req);
    }

    @Post("/bet/refund")
    public async refundBetPayment(@Body() req: RefundRequest): Promise<Balance> {
        return await this.paymentFacade.refundBetPayment(req);
    }

    @Post("bonus/offline")
    public commitOfflineBonusPayment(@Body() req: CommitOfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return this.paymentFacade.commitOfflineBonusPayment(req);
    }

}
