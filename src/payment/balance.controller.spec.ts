import { Test, TestingModule } from "@nestjs/testing";
import { PaymentController } from "./payment.controller";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { PaymentFacade } from "./payment.facade";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { Balance, Balances, PAYMENT_TYPE } from "@skywind-group/sw-wallet-adapter-core";
import { BalanceController } from "./balance.controller";

@suite()
class BalanceControllerSpec {
    private controller: BalanceController;
    private facadeStub: SinonStubbedInstance<PaymentFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(PaymentFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [BalanceController],
            providers: [
                {
                    provide: PaymentFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<BalanceController>(BalanceController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testGetBalances() {
        const response: Balances = {
            USD: {
                main: 10000
            }
        };
        const req = {};
        this.facadeStub.getBalances.resolves(response);
        const result = await this.controller.getBalances(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.getBalances.lastCall.args).deep.eq([req]);
    }
}
