import { Body, Controller, Post, UseFilters } from "@nestjs/common";
import { BalanceRequest } from "../interfaces/payment.service";
import { Balances } from "@skywind-group/sw-wallet-adapter-core";
import { PaymentFacade } from "./payment.facade";
import { ErrorFilter } from "../utils/error.filter";

@UseFilters(ErrorFilter)
@Controller("balances")
export class BalanceController {
    constructor(private readonly paymentFacade: PaymentFacade) {}

    @Post("/")
    public async getBalances(@Body() req: BalanceRequest): Promise<Balances> {
        return this.paymentFacade.getBalances(req);
    }
}
