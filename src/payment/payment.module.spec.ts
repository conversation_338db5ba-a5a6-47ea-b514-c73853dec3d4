import { Test, TestingModule } from "@nestjs/testing";
import { suite, test } from "mocha-typescript";
import { PaymentModule } from "./payment.module";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import {
    BalanceRequest,
    BrokenGameRequest,
    CommitBonusPaymentRequest, CommitOfflineBonusPaymentRequest,
    CommitPaymentRequest,
    GetFreeBetInfoRequest,
    HttpPaymentHandler,
    PaymentService,
    RefundRequest,
    SplitPaymentSupport,
    TransferRequest
} from "../interfaces/payment.service";
import { PaymentController } from "./payment.controller";
import { assert, expect } from "chai";
import { BalanceController } from "./balance.controller";
import { PaymentFacade } from "./payment.facade";
import { Names } from "../names";
import { HttpGateway, HttpHandler, HTTPOperatorRequest } from "../interfaces/http";
import { forwardRef, Injectable, Module } from "@nestjs/common";
import { getHandlerName } from "../utils/handler";
import {
    Balance,
    Balances,
    FreeBetInfo,
    MerchantGameTokenData,
    OfflineBonusInfo
} from "@skywind-group/sw-wallet-adapter-core";
import { HttpPaymentServiceAdapter } from "./httpPayment.adapter";
import request = require("superagent");

@suite()
class PaymentModuleSpec {
    public async after() {
        sinon.restore();
    }

    @test
    public async testRegisterWithService() {
        const payment: SinonStubbedInstance<PaymentService> = sinon.createStubInstance(HttpPaymentServiceAdapter);
        const module: TestingModule = await Test.createTestingModule({
            imports: [PaymentModule.register({ useValue: payment })]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
    }

    @test
    public async testRegisterWithSubInterfaces() {
        const payment: SinonStubbedInstance<PaymentService> = sinon.createStubInstance(HttpPaymentServiceAdapter);
        const module: TestingModule = await Test.createTestingModule({
            imports: [
                PaymentModule.registerWithConfig({
                    payment: { useValue: payment },
                    transfer: { useValue: payment },
                    jackpotPayment: { useValue: payment },
                    refund: { useValue: payment },
                    bonusPayment: { useValue: payment }
                })
            ]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get(Names.PaymentSupportService)).is.not.undefined;
        expect(module.get(Names.JackpotPaymentSupportService)).is.not.undefined;
        expect(module.get(Names.BonusPaymentSupportService)).is.not.undefined;
        expect(module.get(Names.RefundBetSupportService)).is.not.undefined;
        expect(module.get(Names.TransferSupportService)).is.not.undefined;
    }

    @test
    public async testRegisterWithSubInterfacesPartial() {
        const payment: SinonStubbedInstance<PaymentService> = sinon.createStubInstance(HttpPaymentServiceAdapter);
        const module: TestingModule = await Test.createTestingModule({
            imports: [
                PaymentModule.registerWithConfig({
                    payment: { useValue: payment }
                })
            ]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get(Names.PaymentSupportService)).is.not.undefined;
        assert.throws(() => module.get(Names.JackpotPaymentSupportService));
        assert.throws(() => module.get(Names.BonusPaymentSupportService));
        assert.throws(() => module.get(Names.RefundBetSupportService));
        assert.throws(() => module.get(Names.TransferSupportService));
    }

    @test
    public async testRegisterWithHttpConfig() {
        const payment: SinonStubbedInstance<PaymentService> = sinon.createStubInstance(HttpPaymentServiceAdapter);

        class CommitPaymentHandler implements HttpHandler {
            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                PaymentModule.registerWithConfig({
                    http: {
                        gatewayConfig: {
                            operatorUrl: "http://localhost:8080",
                            keepAlive: {
                                freeSocketKeepAliveTimeout: 2000,
                                maxFreeSockets: 10,
                                socketActiveTTL: 2000
                            }
                        },
                        handlers: {
                            commitPayment: { useClass: CommitPaymentHandler }
                        }
                    }
                })
            ]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("commitPayment"))).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("commitWinPayment")));
        assert.throws(() => module.get(getHandlerName("commitBetPayment")));
    }

    @test
    public async testRegisterWithHttpConfigAnAutoInjecting() {
        @Injectable()
        class TestProvider {
        }

        @HttpPaymentHandler("commitPayment")
        class CommitPaymentHandler implements HttpHandler {
            constructor(private test: TestProvider) {
            }

            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        @Module({
            providers: [CommitPaymentHandler, TestProvider],
            exports: [CommitPaymentHandler, TestProvider]
        })
        class Services {
            constructor() {
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                Services,
                PaymentModule.registerWithConfig(
                    {
                        http: {
                            gatewayConfig: {
                                operatorUrl: "http://localhost:8080",
                                keepAlive: {
                                    freeSocketKeepAliveTimeout: 2000,
                                    maxFreeSockets: 10,
                                    socketActiveTTL: 2000
                                }
                            }
                        }
                    },
                    [Services]
                )
            ]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("commitPayment"))).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("commitWinPayment")));
        assert.throws(() => module.get(getHandlerName("commitBetPayment")));
    }

    @test
    public async testRegisterWithMixedConfig() {
        @Injectable()
        class TestProvider {
        }

        @Injectable()
        class PaymentServiceSupport implements SplitPaymentSupport {
            constructor(public readonly httpGateway: HttpGateway) {
            }

            public async commitWinPayment(req: CommitPaymentRequest): Promise<Balance> {
                return undefined;
            }

            public async commitBetPayment(req: CommitBonusPaymentRequest): Promise<Balance> {
                return undefined;
            }

            public async getBalances(req: BalanceRequest): Promise<Balances> {
                return undefined;
            }
        }

        @HttpPaymentHandler("commitJackpotWinPayment")
        class CommitJackpotPaymentHandler implements HttpHandler {
            constructor(private test: TestProvider) {
            }

            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        @Module({
            providers: [CommitJackpotPaymentHandler, PaymentServiceSupport, TestProvider],
            exports: [CommitJackpotPaymentHandler, PaymentServiceSupport, TestProvider],
            imports: [forwardRef(() => paymentModule)]
        })
        class Services {
            constructor() {
            }
        }

        const paymentModule = PaymentModule.registerWithConfig(
            {
                http: {
                    gatewayConfig: {
                        operatorUrl: "http://localhost:8080",
                        keepAlive: {
                            freeSocketKeepAliveTimeout: 2000,
                            maxFreeSockets: 10,
                            socketActiveTTL: 2000
                        }
                    }
                },
                payment: { useClass: PaymentServiceSupport }
            },
            [Services]
        );
        const module: TestingModule = await Test.createTestingModule({
            imports: [Services, paymentModule]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("commitJackpotWinPayment"))).is.not.undefined;
        expect(module.get(Names.PaymentSupportService)).is.not.undefined;
        expect(module.get<PaymentServiceSupport>(Names.PaymentSupportService).httpGateway).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("commitWinPayment")));
        assert.throws(() => module.get(getHandlerName("commitBetPayment")));
    }

    @test
    public async testRegisterWithType() {
        class PaymentServiceMock implements PaymentService {
            public commitBetPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitFreeBetPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitFreeBetWinPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitBonusPayment(req: CommitBonusPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitJackpotWinPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitWinPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public getBalances(req: BalanceRequest): Promise<Balances> {
                return Promise.resolve(undefined);
            }

            public refundBetPayment(req: RefundRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public finalizeGame(req: BrokenGameRequest): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public transferIn(req: TransferRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public transferOut(req: TransferRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public getFreeBetInfo(req: GetFreeBetInfoRequest<MerchantGameTokenData>): Promise<FreeBetInfo> {
                return Promise.resolve(undefined);
            }

            public commitOfflineBonusPayment(req: CommitOfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
                return Promise.resolve(undefined);
            }
        }

        const payment: SinonStubbedInstance<PaymentService> = sinon.createStubInstance(HttpPaymentServiceAdapter);
        let module1 = PaymentModule.register(PaymentServiceMock);
        const module: TestingModule = await Test.createTestingModule({
            imports: [module1]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
    }

    @test
    public async testRegisterWithSubInterfacesAndTypes() {
        class PaymentSupportMock implements PaymentService {
            public commitBetPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitFreeBetPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitFreeBetWinPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitBonusPayment(req: CommitBonusPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitJackpotWinPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public commitWinPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public getBalances(req: BalanceRequest): Promise<Balances> {
                return Promise.resolve(undefined);
            }

            public finalizeGame(req: BrokenGameRequest): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public refundBetPayment(req: RefundRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public transferIn(req: TransferRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public transferOut(req: TransferRequest<MerchantGameTokenData>): Promise<Balance> {
                return Promise.resolve(undefined);
            }

            public getFreeBetInfo(req: GetFreeBetInfoRequest<MerchantGameTokenData>): Promise<FreeBetInfo> {
                return Promise.resolve(undefined);
            }

            public commitOfflineBonusPayment(req: CommitOfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
                return Promise.resolve(undefined);
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                PaymentModule.registerWithConfig({
                    payment: PaymentSupportMock,
                    transfer: PaymentSupportMock,
                    jackpotPayment: PaymentSupportMock,
                    refund: PaymentSupportMock,
                    bonusPayment: PaymentSupportMock
                })
            ]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get(Names.PaymentSupportService)).is.not.undefined;
        expect(module.get(Names.JackpotPaymentSupportService)).is.not.undefined;
        expect(module.get(Names.BonusPaymentSupportService)).is.not.undefined;
        expect(module.get(Names.RefundBetSupportService)).is.not.undefined;
        expect(module.get(Names.TransferSupportService)).is.not.undefined;
    }

    public async testRegisterWithFreeBet() {
        @Injectable()
        class TestProvider {}

        @Injectable()
        class PaymentServiceSupport implements SplitPaymentSupport {
            constructor(public readonly httpGateway: HttpGateway) {}

            public async commitWinPayment(req: CommitPaymentRequest): Promise<Balance> {
                return undefined;
            }

            public async commitBetPayment(req: CommitBonusPaymentRequest): Promise<Balance> {
                return undefined;
            }

            public async getBalances(req: BalanceRequest): Promise<Balances> {
                return undefined;
            }
        }

        @HttpPaymentHandler("commitFreeBetPayment")
        class CommitFreeBetHandler implements HttpHandler {
            constructor(private test: TestProvider) {}

            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        @Module({
            providers: [CommitFreeBetHandler, PaymentServiceSupport, TestProvider],
            exports: [CommitFreeBetHandler, PaymentServiceSupport, TestProvider],
            imports: [forwardRef(() => paymentModule)]
        })
        class Services {
            constructor() {}
        }

        const paymentModule = PaymentModule.registerWithConfig(
            {
                http: {
                    gatewayConfig: {
                        operatorUrl: "http://localhost:8080",
                        keepAlive: {
                            freeSocketKeepAliveTimeout: 2000,
                            maxFreeSockets: 10,
                            socketActiveTTL: 2000
                        }
                    }
                },
                payment: { useClass: PaymentServiceSupport }
            },
            [Services]
        );
        const module: TestingModule = await Test.createTestingModule({
            imports: [Services, paymentModule]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("commitFreeBetPayment"))).is.not.undefined;
        expect(module.get(Names.PaymentSupportService)).is.not.undefined;
        expect(module.get<PaymentServiceSupport>(Names.PaymentSupportService).httpGateway).is.not.undefined;
        expect(module.get<PaymentServiceSupport>(Names.FreeBetPaymentSupport).httpGateway).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("commitWinPayment")));
        assert.throws(() => module.get(getHandlerName("commitBetPayment")));
    }

    @test
    public async testRegisterWithSeveralHandler() {
        @Injectable()
        class TestProvider {}

        @HttpPaymentHandler("commitWinPayment")
        @HttpPaymentHandler("commitJackpotWinPayment")
        class CommitPaymentHandler implements HttpHandler {
            constructor(private test: TestProvider) {}

            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        @Module({
            providers: [CommitPaymentHandler, TestProvider],
            exports: [CommitPaymentHandler, TestProvider]
        })
        class Services {
            constructor() {}
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                Services,
                PaymentModule.registerWithConfig(
                    {
                        http: {
                            gatewayConfig: {
                                operatorUrl: "http://localhost:8080",
                                keepAlive: {
                                    freeSocketKeepAliveTimeout: 2000,
                                    maxFreeSockets: 10,
                                    socketActiveTTL: 2000
                                }
                            }
                        }
                    },
                    [Services]
                )
            ]
        }).compile();

        expect(module.get<PaymentController>(PaymentController)).is.not.undefined;
        expect(module.get<BalanceController>(BalanceController)).is.not.undefined;
        expect(module.get<PaymentFacade>(PaymentFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("commitWinPayment"))).is.not.undefined;
        expect(module.get(getHandlerName("commitJackpotWinPayment"))).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("commitFreeBetPayment")));
        assert.throws(() => module.get(getHandlerName("commitBetPayment")));
    }
}
