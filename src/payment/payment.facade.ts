import { Inject, Injectable, Optional, Param } from "@nestjs/common";
import {
    BalanceRequest,
    BonusPaymentSupport,
    BrokenGameRequest,
    BrokenGameSupport,
    CommitBonusPaymentRequest, CommitOfflineBonusPaymentRequest,
    CommitPaymentRequest,
    FreeBetInfoSupport,
    FreeBetPaymentSupport,
    GetFreeBetInfoRequest,
    JackpotPaymentSupport, OfflineBonusPaymentSupport,
    PaymentSupport,
    RefundBetSupport,
    RefundRequest,
    RegulationSupport,
    RegulatoryActionRequest,
    SplitPaymentSupport,
    TransferPaymentSupport,
    TransferRequest
} from "../interfaces/payment.service";
import { OperationForbidden } from "../errors";
import { Names } from "../names";
import {
    Balance,
    Balances,
    BrandFinalizationType,
    ConnectionError,
    FreeBetInfo,
    MerchantGameTokenData, OfflineBonusInfo,
    PAYMENT_TYPE,
    RequireRefundBetError
} from "@skywind-group/sw-wallet-adapter-core";
import { BaseFacade } from "../utils/base.facade";

@Injectable()
export class PaymentFacade extends BaseFacade {
    constructor(
        @Inject(Names.PaymentSupportService)
        private readonly paymentSupport: PaymentSupport | SplitPaymentSupport,
        @Optional()
        @Inject(Names.JackpotPaymentSupportService)
        private readonly jackpotPaymentSupport?: JackpotPaymentSupport,
        @Optional()
        @Inject(Names.BonusPaymentSupportService)
        private readonly bonusPaymentSupport?: BonusPaymentSupport,
        @Optional()
        @Inject(Names.RefundBetSupportService)
        private readonly refundBetSupport?: RefundBetSupport,
        @Optional()
        @Inject(Names.TransferSupportService)
        private readonly transferSupport?: TransferPaymentSupport,
        @Optional()
        @Inject(Names.FreeBetPaymentSupport)
        private readonly freeBetSupport?: FreeBetPaymentSupport,
        @Optional()
        @Inject(Names.BrokenGameSupportService)
        private readonly brokenGameSupport?: BrokenGameSupport,
        @Optional()
        @Inject(Names.RegulationSupportService)
        private readonly regulationSupport?: RegulationSupport,
        @Optional()
        @Inject(Names.FreeBetInfoService)
        private readonly freeBetInfoSupport?: FreeBetInfoSupport,
        @Optional()
        @Inject(Names.OfflineBonusPaymentService)
        private readonly offlineBonusPaymentSupport?: OfflineBonusPaymentSupport
    ) {
        super();
    }

    public async getBalances(req: BalanceRequest): Promise<Balances> {
        return this.paymentSupport.getBalances(req);
    }

    private get splitPayment(): SplitPaymentSupport {
        return this.paymentSupport as SplitPaymentSupport;
    }

    public async commitPayment(req: CommitPaymentRequest): Promise<Balance> {
        if (req.request.isJPWin) {
            return this.checkExists(this.jackpotPaymentSupport, "commitJackpotWinPayment")(req);
        } else if (req.request.finalizationType === BrandFinalizationType.OFFLINE_PAYMENTS) {
            return this.checkExists(this.brokenGameSupport, "finalizeWithOfflinePayments")(req);
        } else {
            return this.checkExists(this.paymentSupport as PaymentSupport, "commitPayment")(req);
        }
    }

    public async commitBonusPayment(req: CommitBonusPaymentRequest): Promise<Balance> {
        return this.checkExists(this.bonusPaymentSupport, "commitBonusPayment")(req);
    }

    public async transfer(req: TransferRequest): Promise<Balance> {
        if (req.request.operation === "transfer-in") {
            return this.checkExists(this.transferSupport, "transferIn")(req);
        } else if (req.request.operation === "transfer-out") {
            return this.checkExists(this.transferSupport, "transferOut")(req);
        } else {
            throw new OperationForbidden(`Invalid transfer type ${req.request.operation}`);
        }
    }

    public async commitSplitPayment(
        @Param("paymentType") type: PAYMENT_TYPE,
        req: CommitPaymentRequest
    ): Promise<Balance> {
        switch (type) {
            case PAYMENT_TYPE.BET:
                return this.commitBetPayment(req);
            case PAYMENT_TYPE.WIN:
                return this.commitWinPayment(req);
            default:
                throw new OperationForbidden(`Invalid payment type ${type}`);
        }
    }

    private async commitWinPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
        if (!this.isFreeBet(req)) {
            return req.request.finalizationType === BrandFinalizationType.OFFLINE_PAYMENTS ?
                   await this.checkExists(this.brokenGameSupport, "finalizeWithOfflinePayments")(req) :
                   await this.checkExists(this.splitPayment, "commitWinPayment")(req);
        } else {
            return await this.checkExists(this.freeBetSupport, "commitFreeBetWinPayment")(req);
        }
    }

    private async commitBetPayment(req: CommitPaymentRequest<MerchantGameTokenData>): Promise<Balance> {
        try {
            if (!this.isFreeBet(req)) {
                return await this.checkExists(this.splitPayment, "commitBetPayment")(req);
            } else {
                return await this.checkExists(this.freeBetSupport, "commitFreeBetPayment")(req);
            }
        } catch (error) {
            if (error instanceof ConnectionError && this.exists(this.refundBetSupport, "refundBetPayment")) {
                throw new RequireRefundBetError(error.message);
            } else {
                throw error;
            }
        }
    }

    public async refundBetPayment(req: RefundRequest): Promise<Balance> {
        return await this.checkExists(this.refundBetSupport, "refundBetPayment")(req);
    }

    public async finalizeGame(req: BrokenGameRequest): Promise<Balance> {
        return await this.checkExists(this.brokenGameSupport, "finalizeGame")(req);
    }

    public async performRegulatoryAction(req: RegulatoryActionRequest): Promise<any> {
        return await this.checkExists(this.regulationSupport, "performRegulatoryAction")(req);
    }

    public async getFreeBetInfo(req: GetFreeBetInfoRequest): Promise<FreeBetInfo> {
        return await this.checkExists(this.freeBetInfoSupport, "getFreeBetInfo")(req);
    }

    public async commitOfflineBonusPayment(req: CommitOfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return await this.checkExists(this.offlineBonusPaymentSupport, "commitOfflineBonusPayment")(req);
    }

    private isFreeBet(req: CommitPaymentRequest): boolean {
        return !!req.request.freeBetCoin || req.request.freeBetMode;
    }
}
