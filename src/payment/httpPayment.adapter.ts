import {
    Balance,
    Balances,
    FreeBetInfo,
    MerchantGameTokenData,
    OfflineBonusInfo
} from "@skywind-group/sw-wallet-adapter-core";
import {
    BalanceRequest,
    BrokenGameRequest,
    CommitBonusPaymentRequest,
    CommitPaymentRequest, GetFreeBetInfoRequest,
    PaymentService,
    RefundRequest, RegulatoryActionRequest,
    TransferRequest,
    CommitOfflineBonusPaymentRequest
} from "../interfaces/payment.service";
import { Injectable } from "@nestjs/common";
import { HttpBaseAdapter } from "../utils/httpBase.adaper";
import { ModuleRef } from "@nestjs/core";

@Injectable()
export class HttpPaymentServiceAdapter<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends HttpBaseAdapter<PaymentService>
    implements PaymentService<AUTH> {
    constructor(moduleRef: ModuleRef) {
        super(moduleRef);
    }

    public async commitBetPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("commitBetPayment"));
    }

    public async commitFreeBetPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("commitFreeBetPayment"));
    }

    public async commitBonusPayment(req: CommitBonusPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("commitBonusPayment"));
    }

    public async commitJackpotWinPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("commitJackpotWinPayment"));
    }

    public async commitPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("commitPayment"));
    }

    public async commitWinPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("commitWinPayment"));
    }

    public async commitFreeBetWinPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("commitFreeBetWinPayment"));
    }

    public async getBalances(req: BalanceRequest): Promise<Balances> {
        return this.httpGateway.request(req, this.getHandle("getBalances"));
    }

    public async refundBetPayment(req: RefundRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("refundBetPayment"));
    }

    public async finalizeGame(req: BrokenGameRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("finalizeGame"));
    }

    public async finalizeWithOfflinePayments(req: CommitPaymentRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("finalizeWithOfflinePayments"));
    }

    public async performRegulatoryAction(req: RegulatoryActionRequest<AUTH>): Promise<any> {
        return this.httpGateway.request(req, this.getHandle("performRegulatoryAction"));
    }

    public async transferIn(req: TransferRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("transferIn"));
    }

    public async transferOut(req: TransferRequest<AUTH>): Promise<Balance> {
        return this.httpGateway.request(req, this.getHandle("transferOut"));
    }

    public getFreeBetInfo(req: GetFreeBetInfoRequest<MerchantGameTokenData>): Promise<FreeBetInfo> {
        return this.httpGateway.request(req, this.getHandle("getFreeBetInfo"));
    }

    public async commitOfflineBonusPayment(req: CommitOfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return this.httpGateway.request(req, this.getHandle("commitOfflineBonusPayment"));
    }
}
