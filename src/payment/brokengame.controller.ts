import { Body, Controller, Param, Post, UseFilters } from "@nestjs/common";
import { BrokenGameRequest } from "../interfaces/payment.service";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { PaymentFacade } from "./payment.facade";
import { ErrorFilter } from "../utils/error.filter";

@UseFilters(ErrorFilter)
@Controller("finalizeGame")
export class BrokenGameController {
    constructor(private readonly paymentFacade: PaymentFacade) {}

    @Post("/")
    public async finalizeGame(@Body() req: BrokenGameRequest): Promise<Balance> {
        return this.paymentFacade.finalizeGame(req);
    }
}
