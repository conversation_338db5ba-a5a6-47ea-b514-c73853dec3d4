import { measures } from "../measures/measures";
import config from "../config";
import { createKafkaOutput } from "./kafkaOutputStream";
import measureProvider = measures.measureProvider;
import type { OutputConfig, GrayLogOutputConfig, KafkaOutputConfig } from "./definitions";
import { ContextField } from "../measures/provider";
import { deepCloneWithoutCircular } from "./deepCloneWithoutCircular";

const bole = require("bole");

type LogLevel = "info" | "error" | "warn" | "debug";
export namespace logging {

    let rootName: string = "";

    export function logger(name: string = ""): Logger {
        return new Logger(bole(rootName ? `${rootName}:${name}` : name));
    }

    export class Logger {
        constructor(private readonly boleLogger) {
        }

        public debug(...args: any[]): Logger {
            return this.log("debug", args);
        }

        public error(...args: any[]): Logger {
            return this.log("error", args);
        }

        public info(...args: any[]): Logger {
            return this.log("info", args);
        }

        public warn(...args: any[]): Logger {
            return this.log("warn", args);
        }

        private log(level: LogLevel, args: any[]) {
            const traceID = measureProvider.getTraceID();
            let newArgs = deepCloneWithoutCircular(args);
            if (traceID) {
                if (newArgs.length > 0 && typeof newArgs[0] === "object") {
                    const first = newArgs[0] instanceof Error ? { err: errAsObject(newArgs[0] as any) } : newArgs[0];
                    newArgs[0] = this.setTransferableFields({
                        ...first,
                        [this.getFieldNameForLogger(ContextField.TRACE_ID)]: traceID,
                    });
                } else {
                    newArgs = [this.setTransferableFields({ [this.getFieldNameForLogger(ContextField.TRACE_ID)]: traceID })].concat(newArgs);
                }
            }
            this.boleLogger[level](...newArgs);
            return this;
        }

        private setTransferableFields(holder: any): any {
            if (holder) {
                const ctx = measureProvider.getContext(true);
                for (const field of Object.keys(ctx)) {
                    const value = ctx[field];
                    if (value !== undefined) {
                        holder[this.getFieldNameForLogger(field)] = ctx[field];
                    }
                }
                if (config.serviceName) {
                    holder[this.getFieldNameForLogger(ContextField.SERVICE)] = config.serviceName;
                    holder[this.getFieldNameForLogger(ContextField.TRANSACTION)] = measureProvider.getTransaction();
                }
            }

            return holder;
        }

        private getFieldNameForLogger(field: string) {
            return `sw-${field}`;
        }
    }

    export function errAsObject(error: Error & { code: number }): object {
        const err = {
            name: error?.constructor?.name || error.name,
            message: error.message,
            code: error.code,
            stack: stackToString(error),
        };

        for (const key of Object.keys(error)) {
            const prop = error[key];
            const type = typeof prop;
            if (!err[key] && type === "number" || type === "string") {
                err[key] = prop;
            }
        }
        return err;
    }

    // eslint-disable-next-line no-inner-declarations
    function stackToString(e): string {
        let s = e.stack;
        let cause;

        if (typeof e.cause === "function") {
            cause = e.cause();
            if (cause) {
                s += "\nCaused by: " + stackToString(cause);
            }
        }

        return s || "";
    }

    export function setRootLogger(name: string): void {
        rootName = name;
    }

    export function getRootLogger(): string {
        return rootName;
    }

    export function setUpOutput(outputConfigs: OutputConfig | OutputConfig[]) {
        const configs: OutputConfig[] = !Array.isArray(outputConfigs) ? [outputConfigs] : outputConfigs;
        const outputs = [];

        for (const outputConfig of configs) {
            const isGrayLog = (cfg: OutputConfig): cfg is GrayLogOutputConfig => cfg.type === "graylog";
            const isKafka = (cfg: OutputConfig): cfg is KafkaOutputConfig => cfg.type === "kafka";
            let outputStream: unknown;
            if (isGrayLog(outputConfig)) {
                const grayLogConfig = { ...config.logging.graylog, ...outputConfig };
                const gelfStream = require("@skywind-group/gelf-stream");
                let filter;
                if (outputConfig.filterFacility) {
                    filter = (chunk: { name: string }) => (chunk.name || "").includes(outputConfig.filterFacility);
                }
                outputStream = gelfStream.forBole(
                    grayLogConfig.host,
                    grayLogConfig.port,
                    { addFlattened: false, filter },
                );
            } else if (isKafka(outputConfig)) {
                const kafkaConfig = { ...config.logging.kafka, ...outputConfig };
                outputStream = createKafkaOutput(kafkaConfig);
            } else {
                const boleConsole = require("bole-console");
                outputStream = boleConsole({ timestamp: true });
            }
            outputs.push({ level: outputConfig.logLevel, stream: outputStream });
        }

        bole.output(outputs);

        process.on("SIGTERM", function () {
            for (const output of outputs) {
                output.stream.end();
            }
            process.exit(0);
        });
    }
}
