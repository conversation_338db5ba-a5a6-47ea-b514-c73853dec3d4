import { logging } from "./logging/logging";
import { readFile } from "fs";
import { measures } from "./measures/measures";
import { createPool, type Factory, Pool } from "generic-pool";
import {
    Redis,
    Cluster,
    type SentinelAddress,
    type NodeRole,
    type RedisValue,
    type ClusterNode,
} from "ioredis";
import { createHash } from "crypto";

export namespace redis {
    import measure = measures.measure;

    export type RedisClient = Redis | Cluster;

    export class RedisProc {
        private readonly files: string[];
        private procHandle: string;

        /**
         *
         * @param logger
         * @param files
         */
        constructor(private readonly logger: logging.Logger,
                    ...files: string[]) {
            this.files = files;
        }

        public get handle(): string {
            return this.procHandle;
        }

        public async init(client: RedisClient): Promise<void> {
            this.procHandle = await this.getHandle(client);
        }

        public async exec<T>(redis: RedisClient, keys?: string[], execArgs?: string[]): Promise<T> {
            if (!this.procHandle) {
                this.procHandle = await this.getHandle(redis);
            }
            try {
                return await this.doExec<T>(redis, keys, execArgs);
            } catch (err) {
                // Checking for empty scripts in redis ( caused on read-only db after it restarting )
                if (this.isRedisEvalNoScriptError(err)) {
                    this.logger.error(err, "Script not found, reload script");
                    this.procHandle = await this.getHandle(redis);
                    return await this.doExec<T>(redis, keys, execArgs);
                }
                return Promise.reject(err);
            }
        }

        public async doExec<T>(redis: RedisClient, keys?: string[], execArgs?: any[]): Promise<T> {
            const params: [sha1: string, numkeys: string, ...args: RedisValue[]] = [
                this.procHandle,
                keys ? keys.length.toString() : "0",
            ];

            if (keys) {
                params.push(...keys);
            }
            if (execArgs) {
                params.push(...execArgs);
            }
            return redis.evalsha(...params) as Promise<T>;
        }

        private async getHandle(redis: RedisClient): Promise<string> {
            const data = await this.readFiles();

            let handle = this.generateSha1(data);
            const exists = await this.isExists(redis, handle);
            if (!exists) {
                handle = await this.createHandle(redis, data);
            }

            return handle;
        }

        private async readFiles(): Promise<string> {
            let result: string = "";
            for (const f of this.files) {
                const data = await this.readFile(f);
                result = result.concat("\n").concat(data);
            }
            return result;
        }

        private readFile(file: string): Promise<string> {
            return new Promise<string>((resolve, reject) => {
                readFile(file, undefined, (err, data) => {
                    if (err) {
                        return reject(err);
                    }
                    return resolve(data.toString());
                });
            });
        }

        private async isExists(client: RedisClient, handle: string): Promise<boolean> {
            const result = await client.script("EXISTS", handle);
            return result && result[0] === 1;
        }

        private async createHandle(client: RedisClient, data: string): Promise<string> {
            return client.script("LOAD", data) as Promise<string>;
        }

        private isRedisEvalNoScriptError(error: any): boolean {
            return error.message.startsWith("NOSCRIPT ");
        }

        private generateSha1(data: string): string {
            return createHash("sha1").update(data).digest("hex");
        }
    }

    export interface RedisPoolConfig {
        host: string;
        port: number;
        password: string;
        sentinels?: Partial<SentinelAddress>[];
        sentinelUsername?: string;
        sentinelPassword?: string;
        clusterNodes?: ClusterNode[];
        clusterSlotsRefreshInterval?: number;
        clusterScaleReads?: NodeRole;
        clusterEnableOfflineQueue?: boolean;
        clusterEnableReadyCheck?: boolean;
        clusterName?: string;
        connectionTimeout?: number;
        minConnections: number;
        maxConnections: number;
        maxIdleTime: number;
        replicationFactor?: number;
        replicationTimeout?: number;
        maxRetriesPerRequest: number;
        showFriendlyErrorStack?: boolean;
        enableOfflineQueue?: boolean;
        failoverDetector?: boolean;
    }

    export class RedisPool<CLIENT extends RedisClient = RedisClient> {

        constructor(private readonly config: RedisPoolConfig,
                    private readonly pool: Pool<CLIENT>) {
        }

        @measure({ name: "RedisPool.get", isAsync: true })
        public async get(): Promise<CLIENT> {
            return this.pool.acquire();
        }

        public async release(client: CLIENT): Promise<void> {
            await this.pool.release(client);
        }

        public async usingDb<T>(callback: (client: CLIENT) => Promise<T>): Promise<T> {
            const client = await this.get();
            try {
                return await callback(client);
            } finally {
                await this.release(client);
            }
        }

        public async usingDbWithReplicate<T>(callback: (client: CLIENT) => Promise<T>): Promise<T> {
            const client = await this.get();
            try {
                const result = await callback(client);
                // eslint-disable-next-line node/no-sync
                await this.waitForSync(client);
                return result;
            } finally {
                await this.release(client);
            }
        }

        public async waitForSync(client: CLIENT): Promise<void> {
            const replicationFactor: number = this.config.replicationFactor;
            const timeout: number = this.config.replicationTimeout;
            if (replicationFactor > 0) {
                const wait: Promise<number> = client.wait(replicationFactor, timeout);
                const result = await wait;
                if (+result < replicationFactor) {
                    return Promise.reject(new Error("Cannot replicate"));
                }
            }
        }

        public async shutdown(): Promise<void> {
            await this.pool.drain();
            await this.pool.clear();
        }
    }

    export interface RedisConnectionFactory<T extends RedisClient> extends Factory<T> {
        createClient(): T;
    }

    class RedisClientFactory implements RedisConnectionFactory<RedisClient> {
        constructor(private readonly host: string,
                    private readonly port: number,
                    private readonly password: string,
                    private readonly connectionTimeout: number,
                    private readonly maxRetriesPerRequest: number,
                    private readonly showFriendlyErrorStack: boolean,
                    private readonly enableOfflineQueue: boolean,
        ) {
        }

        public async create(): Promise<RedisClient> {
            return this.createClient();
        }

        public createClient(): RedisClient {
            return new Redis({
                host: this.host,
                port: this.port,
                password: this.password,
                connectTimeout: this.connectionTimeout,
                maxRetriesPerRequest: this.maxRetriesPerRequest,
                showFriendlyErrorStack: this.showFriendlyErrorStack,
                enableOfflineQueue: this.enableOfflineQueue,
            });
        }

        public async destroy(client: RedisClient): Promise<void> {
            await client.quit();
        }
    }

    class SentinelRedisClientFactory implements RedisConnectionFactory<RedisClient> {
        constructor(private readonly clusterName: string,
                    private readonly sentinels: Partial<SentinelAddress>[],
                    private readonly sentinelUsername: string,
                    private readonly sentinelPassword: string,
                    private readonly password: string,
                    private readonly connectionTimeout: number,
                    private readonly maxRetriesPerRequest: number,
                    private readonly showFriendlyErrorStack: boolean,
                    private readonly enableOfflineQueue: boolean,
                    private readonly failoverDetector: boolean,
        ) {
        }

        public async create(): Promise<RedisClient> {
            return this.createClient();
        }

        public createClient(): RedisClient {
            return new Redis({
                name: this.clusterName,
                password: this.password,
                sentinelUsername: this.sentinelUsername,
                sentinelPassword: this.sentinelPassword,
                sentinels: this.sentinels,
                connectTimeout: this.connectionTimeout,
                autoResubscribe: true,
                maxRetriesPerRequest: this.maxRetriesPerRequest,
                showFriendlyErrorStack: this.showFriendlyErrorStack,
                enableOfflineQueue: this.enableOfflineQueue,
                failoverDetector: this.failoverDetector,
            });
        }

        public async destroy(client: RedisClient): Promise<void> {
            await client.quit();
        }
    }

    class ClusterRedisClientFactory implements RedisConnectionFactory<Cluster> {
        constructor(private readonly clusterNodes: ClusterNode[],
                    private readonly password: string,
                    private readonly connectionTimeout: number,
                    private readonly maxRetriesPerRequest: number,
                    private readonly scaleReads: NodeRole,
                    private readonly slotsRefreshInterval: number,
                    private readonly enableOfflineQueue: boolean,
                    private readonly enableReadyCheck: boolean,
                    private readonly showFriendlyErrorStack: boolean) {
        }

        public async create(): Promise<Cluster> {
            return this.createClient();
        }

        public createClient(): Cluster {
            return new Cluster(this.clusterNodes, {
                redisOptions: {
                    password: this.password,
                    connectTimeout: this.connectionTimeout,
                    autoResubscribe: true,
                    maxRetriesPerRequest: this.maxRetriesPerRequest,
                },
                slotsRefreshInterval: this.slotsRefreshInterval,
                enableOfflineQueue: this.enableOfflineQueue,
                enableReadyCheck: this.enableReadyCheck,
                scaleReads: this.scaleReads,
                showFriendlyErrorStack: this.showFriendlyErrorStack,
            });
        }

        public async destroy(client: Cluster): Promise<void> {
            await client.quit();
        }
    }

    export function createRedisPool(config: RedisPoolConfig) {
        return new RedisPool(
            config,
            createPool(
                getRedisFactory(config),
                {
                    max: config.maxConnections,
                    min: config.minConnections,
                    idleTimeoutMillis: config.maxIdleTime,
                },
            ),
        );
    }

    export function getRedisFactory(config: RedisPoolConfig)
        : SentinelRedisClientFactory | RedisClientFactory | ClusterRedisClientFactory {
        if (config.sentinels) {
            return new SentinelRedisClientFactory(
                config.clusterName,
                config.sentinels,
                config.sentinelUsername,
                config.sentinelPassword,
                config.password,
                config.connectionTimeout,
                config.maxRetriesPerRequest,
                config.showFriendlyErrorStack,
                config.enableOfflineQueue,
                config.failoverDetector,
            );
        }
        if (config.clusterNodes) {
            return new ClusterRedisClientFactory(
                config.clusterNodes,
                config.password,
                config.connectionTimeout,
                config.maxRetriesPerRequest,
                config.clusterScaleReads,
                config.clusterSlotsRefreshInterval,
                config.clusterEnableOfflineQueue,
                config.clusterEnableReadyCheck,
                config.showFriendlyErrorStack,
            );
        }

        return new RedisClientFactory(
            config.host,
            config.port,
            config.password,
            config.connectionTimeout,
            config.maxRetriesPerRequest,
            config.showFriendlyErrorStack,
            config.enableOfflineQueue,
        );
    }

    export function handleRedisEvents(redis: RedisClient, logger: logging.Logger): void {
        redis.on("connect", () => {
            logger.debug("Connected to Redis");
        });

        redis.on("error", (err) => {
            logger.error(err, "Redis connection error");
        });

        redis.on("sentinelError", (err) => {
            logger.error(err, "Sentinel error");
        });
    }
}
