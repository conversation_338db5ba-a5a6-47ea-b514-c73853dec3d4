import { Modu<PERSON> } from "@nestjs/common";
import { InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { HttpGateway, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { ScheduleModule } from "@nestjs/schedule";
import { CriticalFilesService } from "@operator/services/criticalFiles.service";
import { Names } from "@names";
import { ReportGameChecksumHttpHandler } from "@operator/handlers/reportGameChecksum.http.handler";
import { ReportPlatformChecksumHttpHandler } from "@operator/handlers/reportPlatformChecksum.http.handler";

@Module({
    providers: [
        CriticalFilesService,
        ReportPlatformChecksumHttpHandler,
        ReportGameChecksumHttpHandler,
        HttpGateway,
        {
            provide: Names.InternalAPIService,
            useValue: new InternalAPIService(config.swInternalMapiUrl)
        },
        {
            provide: CoreNames.HttpGatewayConfig,
            useValue: config.http
        }
    ],
    imports: [ScheduleModule.forRoot()],
    exports: [CriticalFilesService]
})
export class CriticalFilesModule {}
