import { Modu<PERSON> } from "@nestjs/common";
import { HistoryService } from "@operator/services/history.service";
import { HistoryController } from "@operator/controllers/history.controller";
import { InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { HttpGateway, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { PlayCheckerHttpHandler } from "@operator/handlers/playChecker.http.handler";
import { Names } from "@names";
import { TypeOrmModule } from "@nestjs/typeorm";
import { RoundRepository } from "@wallet/round/round.repository";
import { DbModule } from "@db/db.module";

@Module({
    imports: [TypeOrmModule.forFeature([RoundRepository]), DbModule],
    controllers: [HistoryController],
    providers: [
        HttpGateway,
        PlayCheckerHttpHandler,
        HistoryService,
        {
            provide: Names.InternalAPIService,
            useValue: new InternalAPIService(config.swInternalMapiUrl)
        },
        {
            provide: CoreNames.HttpGatewayConfig,
            useValue: config.http
        }
    ],
    exports: [HistoryService]
})
export class HistoryModule {}
