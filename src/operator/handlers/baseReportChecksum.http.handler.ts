import { BaseHttpHandler } from "@utils/baseHttp.handler";
import { Inject } from "@nestjs/common";
import { Names } from "@names";
import { InternalAPIService, MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import { MerchantInternalError } from "@errors/sw.errors";
import { MerchantInfoCache } from "@operator/services/merchantInfo.cache";

export abstract class BaseReportChecksumHttpHandler extends BaseHttpHandler {
    protected readonly cache: MerchantInfoCache;

    constructor(@Inject(Names.InternalAPIService) private readonly apiService: InternalAPIService) {
        super();
        this.cache = MerchantInfoCache.getInstance();
    }

    protected async getMerchantInfo(merchantCode: string, merchantType: string): Promise<MerchantInfo> {
        const cacheKey = `${merchantCode}__${merchantType}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get<MerchantInfo>(cacheKey);
        }
        const merchantEntityInfo = await this.apiService.getMerchantEntityInfo(merchantCode, merchantType);
        if (!merchantEntityInfo) {
            throw new MerchantInternalError("Merchant not found");
        }
        this.cache.set<MerchantInfo>(cacheKey, merchantEntityInfo.merchant);
        return merchantEntityInfo.merchant;
    }
}
