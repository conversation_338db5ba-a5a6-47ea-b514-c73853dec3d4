import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import {
    FileChecksum,
    OperatorPlatformChecksumsRequest,
    OperatorPlatformChecksumsResponse
} from "@entities/operator.entities";
import * as request from "superagent";
import { buildMerchantCode } from "@utils/common";
import { ModulesListWithHashes } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { BaseReportChecksumHttpHandler } from "@operator/handlers/baseReportChecksum.http.handler";

export class ReportPlatformChecksumHttpHandler extends BaseReportChecksumHttpHandler
    implements HttpHandler<ModulesListWithHashes> {
    public async build(
        modulesListWithHashes: ModulesListWithHashes
    ): Promise<HTTPOperatorRequest<OperatorPlatformChecksumsRequest>> {
        const checksums: FileChecksum[] = [];
        for (const module of modulesListWithHashes.modules) {
            for (const criticalFile of module.list) {
                for (const [path, checksum] of Object.entries(criticalFile)) {
                    checksums.push({
                        FileName: path,
                        Checksum: checksum
                    });
                }
            }
        }
        const operatorCriticalFilesReportRequest: OperatorPlatformChecksumsRequest = {
            ...this.buildBaseRequest(),
            Checksums: checksums
        };
        return this.buildHttpRequest({
            path: "ReportPlatform/Checksum",
            method: "post",
            payload: operatorCriticalFilesReportRequest,
            jurisdiction: config.defaultJurisdiction,
            merchantInfo: await this.getMerchantInfo(
                buildMerchantCode(config.defaultJurisdiction),
                config.merchantType
            ),
            retryAvailable: true
        });
    }

    public parse(response: request.Response): Promise<any> {
        return this.parseHttpResponse<OperatorPlatformChecksumsResponse>(response);
    }
}
