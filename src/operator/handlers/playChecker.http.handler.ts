import { BaseHttpHandler } from "@utils/baseHttp.handler";
import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import {
    OperatorHistoryRequest,
    OperatorPlayCheckerRequest,
    OperatorPlayCheckerResponse
} from "@entities/operator.entities";
import * as request from "superagent";
import { Inject } from "@nestjs/common";
import { Names } from "@names";
import { InternalAPIService, MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import { MerchantInternalError } from "@errors/sw.errors";

export class PlayCheckerHttpHandler extends BaseHttpHandler implements HttpHandler<OperatorHistoryRequest> {
    constructor(@Inject(Names.InternalAPIService) private readonly apiService: InternalAPIService) {
        super();
    }

    public async build(req: OperatorHistoryRequest): Promise<HTTPOperatorRequest<OperatorPlayCheckerRequest>> {
        const playCheckerRequest: OperatorPlayCheckerRequest = {
            ...this.buildBaseRequest(),
            PublicToken: req.publicToken
        };
        return this.buildHttpRequest({
            path: "PlayChecker",
            method: "post",
            payload: playCheckerRequest,
            jurisdiction: req.jurisdiction,
            merchantInfo: await this.getMerchantInfo(req.merchantCode, req.merchantType),
            retryAvailable: true
        });
    }

    public parse(response: request.Response): Promise<OperatorPlayCheckerResponse> {
        return this.parseHttpResponse<OperatorPlayCheckerResponse>(response);
    }

    private async getMerchantInfo(merchantCode: string, merchantType: string): Promise<MerchantInfo> {
        const merchantEntityInfo = await this.apiService.getMerchantEntityInfo(merchantCode, merchantType);
        if (!merchantEntityInfo) {
            throw new MerchantInternalError("Merchant not found");
        }
        return merchantEntityInfo.merchant;
    }
}
