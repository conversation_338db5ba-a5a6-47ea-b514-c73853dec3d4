import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { FileChecksum, OperatorGameChecksumsRequest, OperatorGameChecksumsResponse } from "@entities/operator.entities";
import * as request from "superagent";
import { buildMerchantCode } from "@utils/common";
import { GameWithHashList } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { BaseReportChecksumHttpHandler } from "@operator/handlers/baseReportChecksum.http.handler";

export class ReportGameChecksumHttpHandler extends BaseReportChecksumHttpHandler
    implements HttpHandler<GameWithHashList> {
    public async build(game: GameWithHashList): Promise<HTTPOperatorRequest<OperatorGameChecksumsRequest>> {
        const checksums: FileChecksum[] = [];
        for (const criticalFile of game.list) {
            for (const [path, checksum] of Object.entries(criticalFile)) {
                checksums.push({
                    FileName: path,
                    Checksum: checksum
                });
            }
        }
        const operatorCriticalFilesReportRequest: OperatorGameChecksumsRequest = {
            ...this.buildBaseRequest(),
            ProviderGameReference: game.code,
            Checksums: checksums
        };
        return this.buildHttpRequest({
            path: "ReportGame/Checksum",
            method: "post",
            payload: operatorCriticalFilesReportRequest,
            jurisdiction: config.defaultJurisdiction,
            merchantInfo: await this.getMerchantInfo(
                buildMerchantCode(config.defaultJurisdiction),
                config.merchantType
            ),
            retryAvailable: true
        });
    }

    public parse(response: request.Response): Promise<any> {
        return this.parseHttpResponse<OperatorGameChecksumsResponse>(response);
    }
}
