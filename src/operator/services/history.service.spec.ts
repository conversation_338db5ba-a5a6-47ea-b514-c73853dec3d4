import "module-alias/register";
import { expect, should, use } from "chai";
import { suite, test } from "mocha-typescript";
import * as chaiAsPromised from "chai-as-promised";
import { HttpGateway, HttpGatewayConfig } from "@skywind-group/sw-integration-core";
import { InternalAPIService, MerchantInfo, RoundImageResponse } from "@skywind-group/sw-wallet-adapter-core";
import { mapping, testing } from "@skywind-group/sw-utils";
import DBModel = mapping.DBModel;
import * as request from "superagent";
import { PlayCheckerHttpHandler } from "@operator/handlers/playChecker.http.handler";
import { HistoryService } from "@operator/services/history.service";
import { Test, TestingModule } from "@nestjs/testing";
import { OperatorHistoryRequest, OperatorPlayCheckerResponse } from "@entities/operator.entities";
import * as sinon from "sinon";
import { Names } from "@names";
import config from "@config";
import { Names as CoreNames } from "@skywind-group/sw-integration-core/lib/names";
import status200 = testing.status200;
import { RoundNotFoundError } from "@errors/sw.errors";
import { RoundDto } from "@wallet/round/round.dto";
import { roundRepository } from "@utils/test/stubs";

should();
use(chaiAsPromised);

const httpGatewayConfig = {
    operatorUrl: "http://operator/API",
    keepAlive: {
        socketActiveTTL: 60000,
        maxFreeSockets: 100,
        freeSocketKeepAliveTimeout: 12000
    }
} as HttpGatewayConfig;

const merchantInfo: MerchantInfo = {
    brandId: 1111,
    type: config.merchantType,
    code: config.merchantCode,
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
};

@suite()
class HistoryServiceSpec {
    private requestMock: testing.RequestMock = testing.requestMock(request);
    private findRoundStub: sinon.SinonStub;
    private playCheckerHttpHandler: PlayCheckerHttpHandler;
    private historyService: HistoryService;
    private moduleRef: TestingModule;

    public async before() {
        this.moduleRef = await Test.createTestingModule({
            providers: [
                HttpGateway,
                PlayCheckerHttpHandler,
                HistoryService,
                {
                    provide: Names.InternalAPIService,
                    useValue: new InternalAPIService(config.swInternalMapiUrl)
                },
                {
                    provide: CoreNames.HttpGatewayConfig,
                    useValue: httpGatewayConfig
                },
                {
                    provide: Names.RoundRepository,
                    useValue: roundRepository
                }
            ]
        }).compile();
        this.findRoundStub = sinon.stub(roundRepository, "findOne");
        this.playCheckerHttpHandler = this.moduleRef.get(PlayCheckerHttpHandler);
        this.historyService = this.moduleRef.get(HistoryService);
    }

    public after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    public afterAll() {
        return this.moduleRef.close();
    }

    @test("Getting the round history image successfully")
    public async getRoundHistoryImage() {
        this.requestMock.post(
            "http://operator/API/IGP/PlayChecker",
            status200({
                GamingId: "player",
                ErrorDetails: null
            } as OperatorPlayCheckerResponse)
        );
        this.requestMock.post(
            config.swInternalMapiUrl + "/v1/merchants/history/image",
            status200({
                imageUrl: "https://www.round-image.com",
                ttl: config.roundImageHistoryTTL
            } as RoundImageResponse)
        );
        const historyRequest: OperatorHistoryRequest = {
            publicToken: "some_token",
            roundId: 123456,
            jurisdiction: "IT",
            language: "IT"
        };
        const merchantCode = `${merchantInfo.code}__${historyRequest.jurisdiction}`;
        this.requestMock.get(
            config.swInternalMapiUrl + `/v1/merchantentities/${merchantInfo.type}/${merchantCode}`,
            status200({ merchant: merchantInfo })
        );
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        const actualResponse = await this.historyService.getGameHistoryImageUrl(historyRequest);
        return expect(actualResponse).to.equal("https://www.round-image.com");
    }

    @test("Getting the round history image fails with RoundNotFoundError")
    public async getRoundHistoryImageFailsWithMerchantNotFound() {
        this.requestMock.post(
            "http://operator/API/IGP/PlayChecker",
            status200({
                GamingId: "player",
                ErrorDetails: null
            } as OperatorPlayCheckerResponse)
        );
        this.requestMock.post(
            config.swInternalMapiUrl + "/v1/merchants/history/image",
            status200({
                imageUrl: "https://www.round-image.com",
                ttl: config.roundImageHistoryTTL
            } as RoundImageResponse)
        );
        const historyRequest: OperatorHistoryRequest = {
            publicToken: "some_token",
            roundId: 123456,
            jurisdiction: "IT",
            language: "IT"
        };
        const merchantCode = `${merchantInfo.code}__${historyRequest.jurisdiction}`;
        this.requestMock.get(
            config.swInternalMapiUrl + `/v1/merchantentities/${merchantInfo.type}/${merchantCode}`,
            status200({ merchant: merchantInfo })
        );
        this.findRoundStub.returns(Promise.reject(new RoundNotFoundError()));
        const promise = this.historyService.getGameHistoryImageUrl(historyRequest);
        return promise.should.eventually.be.rejectedWith(RoundNotFoundError);
    }
}
