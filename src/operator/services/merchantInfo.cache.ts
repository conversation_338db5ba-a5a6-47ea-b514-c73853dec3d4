import * as NodeCache from "node-cache";
import config from "@config";

export class MerchantInfoCache {
    private cache: NodeCache;
    private static instance: MerchantInfoCache;

    private constructor() {
        this.cache = new NodeCache(config.criticalFiles.cache);
    }

    public static getInstance(): MerchantInfoCache {
        if (!this.instance) {
            this.instance = new MerchantInfoCache();
        }
        return this.instance;
    }

    public set<T>(key: string, value: T): boolean {
        return this.cache.set<T>(key, value);
    }

    public get<T>(key: string): T {
        return this.cache.get<T>(key);
    }

    public has(key: string): boolean {
        return this.cache.has(key);
    }
}
