import { Injectable, Inject } from "@nestjs/common";
import { HistoryQueryParams, InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { Names } from "@names";
import { OperatorHistoryRequest, OperatorPlayCheckerResponse } from "@entities/operator.entities";
import { RoundNotFoundError } from "@errors/sw.errors";
import { buildMerchantCode } from "@utils/common";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import { PlayCheckerHttpHandler } from "@operator/handlers/playChecker.http.handler";
import { RoundRepository } from "@wallet/round/round.repository";
import { RoundEntity } from "@wallet/round/round.entity";

@Injectable()
export class HistoryService {
    constructor(
        @Inject(Names.InternalAPIService) private apiService: InternalAPIService,
        private readonly roundRepository: RoundRepository,
        private readonly httpGateway: HttpGateway,
        private readonly playCheckerHttpHandler: PlayCheckerHttpHandler
    ) {}

    public async getGameHistoryImageUrl(request: OperatorHistoryRequest): Promise<string> {
        const merchantCode = buildMerchantCode(request.jurisdiction, request.merchantCode);
        const merchantType = request.merchantType || config.merchantType;
        await this.verifyPublicToken({ ...request, merchantCode, merchantType });
        const roundId = request.swRoundId || (await this.getSWRoundId(request.roundId));
        const params: HistoryQueryParams = {
            ttl: config.roundImageHistoryTTL,
            language: request.language
        };
        const historyVisualisation = await this.apiService.getRoundImage(merchantCode, merchantType, roundId, params);
        return historyVisualisation.imageUrl;
    }

    private async verifyPublicToken(request: OperatorHistoryRequest): Promise<OperatorPlayCheckerResponse> {
        // If the status code is not 200, 201 or 203, this call will throw an error
        return this.httpGateway.request<OperatorHistoryRequest, OperatorPlayCheckerResponse>(
            request,
            this.playCheckerHttpHandler
        );
    }

    private async getSWRoundId(operatorRoundId: number): Promise<string> {
        const roundEntity: RoundEntity = await this.roundRepository.findOne({
            where: { operatorRoundId }
        });
        if (!roundEntity) {
            throw new RoundNotFoundError();
        }
        return roundEntity.swRoundId;
    }
}
