import { Injectable } from "@nestjs/common";
import {
    GamesListWithHashes,
    InternalAPIService,
    ModulesListWithHashes,
    SWPlatformCriticalFilesRequest
} from "@skywind-group/sw-wallet-adapter-core";
import { logging, measures } from "@skywind-group/sw-utils";
import config from "../../config";
import { Cron } from "@nestjs/schedule";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import { ReportGameChecksumHttpHandler } from "../handlers/reportGameChecksum.http.handler";
import { Names } from "@names";
import { buildMerchantCode } from "@utils/common";
import { SWGameCriticalFilesRequest } from "@skywind-group/sw-wallet-adapter-core/src/skywind/definitions/criticalFiles";
import { ReportPlatformChecksumHttpHandler } from "@operator/handlers/reportPlatformChecksum.http.handler";
import measureProvider = measures.measureProvider;

export interface ICriticalFilesService {
    reportCriticalFiles(): Promise<void>;
}

@Injectable()
export class CriticalFilesService implements ICriticalFilesService {
    private log: logging.Logger;

    constructor(
        private readonly reportPlatformChecksumHttpHandler: ReportPlatformChecksumHttpHandler,
        private readonly reportGameChecksumHttpHandler: ReportGameChecksumHttpHandler,
        private readonly apiService: InternalAPIService,
        private readonly httpGateway: HttpGateway
    ) {
        this.log = logging.logger("adapter-b365-critical-files-job");
    }

    @Cron(config.criticalFiles.cronPattern, { name: Names.CriticalFilesJob })
    public async reportCriticalFiles(): Promise<void> {
        return measureProvider.runInTransaction(Names.CriticalFilesJob, async () => {
            await this.reportPlatformCriticalFiles();
            await this.reportGameCriticalFiles();
        });
    }

    private async reportGameCriticalFiles(): Promise<void> {
        const gameCriticalFiles = await this.fetchGameCriticalFiles();
        const gamesReportedSuccessfully: string[] = [];
        for (const gameWithHashList of gameCriticalFiles.games) {
            try {
                await this.httpGateway.request(gameWithHashList, this.reportGameChecksumHttpHandler);
                gamesReportedSuccessfully.push(gameWithHashList.code);
            } catch (err) {
                this.log.error(err, `Failed to report game ${gameWithHashList.code} to the operator`);
            }
        }
        const message =
            gamesReportedSuccessfully.length === 0
                ? "No game critical files were reported to the operator"
                : gamesReportedSuccessfully.length === gameCriticalFiles.games.length
                ? "All game critical files were reported successfully to the operator"
                : "Part of the game critical files were reported successfully to the operator";
        const gamesNotReportedSuccessfully = gameCriticalFiles.games
            .map((game) => game.code)
            .filter((gameCode) => !gamesReportedSuccessfully.includes(gameCode));
        this.log.info({ reported: gamesReportedSuccessfully, notReported: gamesNotReportedSuccessfully }, message);
    }

    public async reportPlatformCriticalFiles(): Promise<void> {
        const platformCriticalFiles = await this.fetchPlatformCriticalFiles();
        await this.httpGateway.request(platformCriticalFiles, this.reportPlatformChecksumHttpHandler);
        this.log.info(
            platformCriticalFiles.modules.map((module) => module.name),
            "Platform critical files were reported to the operator"
        );
    }

    private async fetchGameCriticalFiles(): Promise<GamesListWithHashes> {
        try {
            const gameCodes = await this.fetchGameCodes(
                buildMerchantCode(config.defaultJurisdiction),
                config.merchantType
            );
            const gameCriticalFilesRequest: SWGameCriticalFilesRequest = {
                regulation: config.criticalFiles.regulation,
                games: gameCodes,
                includeVersions: false
            };
            const merchantCode = buildMerchantCode(config.defaultJurisdiction);
            const gameCriticalFilesInfo = await this.apiService.getGameCriticalFilesInfo(
                merchantCode,
                config.merchantType,
                gameCriticalFilesRequest
            );
            if (gameCriticalFilesInfo.games.length < gameCodes.length) {
                const criticalFilesGameCodes = gameCriticalFilesInfo.games.map((game) => game.code);
                const gamesWithoutCriticalFiles = gameCodes.filter(
                    (gameCode) => !criticalFilesGameCodes.includes(gameCode)
                );
                this.log.warn({ games: gamesWithoutCriticalFiles }, "Game critical files not found");
            }
            return gameCriticalFilesInfo;
        } catch (error) {
            this.log.error(error, "Unable to retrieve game critical files");
            throw error;
        }
    }

    private async fetchPlatformCriticalFiles(): Promise<ModulesListWithHashes> {
        try {
            const platformCriticalFilesRequest: SWPlatformCriticalFilesRequest = {
                regulation: config.criticalFiles.regulation
            };
            const merchantCode = buildMerchantCode(config.defaultJurisdiction);
            return this.apiService.getPlatformCriticalFilesInfo(
                merchantCode,
                config.merchantType,
                platformCriticalFilesRequest
            );
        } catch (error) {
            this.log.error(error, "Unable to retrieve platform critical files");
            throw error;
        }
    }

    private async fetchGameCodes(merchantCode: string, merchantType: string): Promise<string[]> {
        const games = await this.apiService.getGames(merchantCode, merchantType, { shortInfo: true });
        return games.map((game) => game.code);
    }
}
