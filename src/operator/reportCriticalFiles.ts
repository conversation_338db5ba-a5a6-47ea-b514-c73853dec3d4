import { logging } from "@skywind-group/sw-utils";
import { CriticalFilesService } from "./services/criticalFiles.service";
import config from "../config";
import { InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import { ReportGameChecksumHttpHandler } from "@operator/handlers/reportGameChecksum.http.handler";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import { ReportPlatformChecksumHttpHandler } from "@operator/handlers/reportPlatformChecksum.http.handler";

const log = logging.logger("adapter-b365-critical-files-job");

// This script was made as a contingency plan in case the service
// is restarted at the same time the cron was supposed to run
// With this script you can report the critical files manually at any time
const reportCriticalFiles = async () => {
    const httpGateway = new HttpGateway(config.http);
    const internalMapiService = new InternalAPIService(config.swInternalMapiUrl);
    const reportGameChecksumHttpHandler = new ReportGameChecksumHttpHandler(internalMapiService);
    const reportPlatformChecksumHttpHandler = new ReportPlatformChecksumHttpHandler(internalMapiService);
    const criticalFilesService = new CriticalFilesService(
        reportPlatformChecksumHttpHandler,
        reportGameChecksumHttpHandler,
        internalMapiService,
        httpGateway
    );
    return criticalFilesService.reportCriticalFiles();
};

reportCriticalFiles().catch((err) => log.error(err, "Failed to report critical files (manually triggered)"));
