import { Controller, Get, HttpStatus, Query, Redirect, UseFilters } from "@nestjs/common";
import { HistoryService } from "@operator/services/history.service";
import { OperatorHistoryRequest } from "@entities/operator.entities";
import { BaseErrorsFilter } from "@launcher/errors.filter";
import { ValidationPipe } from "@utils/validator.pipe";

@Controller("v1/history")
@UseFilters(BaseErrorsFilter)
export class HistoryController {
    constructor(private readonly historyService: HistoryService) {}

    @Get("round-details")
    @Redirect(undefined, HttpStatus.FOUND)
    public async getVisualGameHistory(@Query(new ValidationPipe()) queryParams: OperatorHistoryRequest) {
        return { url: await this.historyService.getGameHistoryImageUrl(queryParams) };
    }
}
