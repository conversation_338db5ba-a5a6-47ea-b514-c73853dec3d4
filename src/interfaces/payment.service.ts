import {
    Balance,
    Balances,
    FinalizeGameRequest,
    FreeBetInfo,
    FreeBetInfoRequest,
    MERCHANT_REGULATION,
    MerchantGameTokenData,
    MerchantTransferRequest,
    OfflineBonusInfo,
    OfflineBonusPaymentRequest,
    PaymentRequest,
    PlayerRegulatoryActionRequest,
    RefundBetRequest
} from "@skywind-group/sw-wallet-adapter-core";
import { BaseRequest, ServiceProvider } from "./types";
import { HttpHandlersConfig } from "./http";
import { InjectableOptions } from "@nestjs/common";
import { SCOPE_OPTIONS_METADATA } from "@nestjs/common/constants";

/**
 * Base interface of payment request that include game token data
 */
export interface BasePaymentRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData> extends BaseRequest {
    gameTokenData: AUTH;
}

/**
 *   Base interface that includes support of all payment functions
 */
export interface PaymentService<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends SplitPaymentSupport<AUTH>,
        PaymentSupport<AUTH>,
        FreeBetPaymentSupport<AUTH>,
        JackpotPaymentSupport<AUTH>,
        TransferPaymentSupport<AUTH>,
        BonusPaymentSupport<AUTH>,
        RefundBetSupport<AUTH>,
        BalanceSupport<AUTH>,
        BrokenGameSupport<AUTH>,
        RegulationSupport<AUTH>,
        FreeBetInfoSupport,
        OfflineBonusPaymentSupport{}


/**
 * Commit payment request, used for the commit payment, split payment, jackpot payment
 */
export interface CommitPaymentRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BasePaymentRequest<AUTH> {
    request: PaymentRequest;
    regulation?: MERCHANT_REGULATION;
}

/**
 * Request to make bonus payment
 */
export interface CommitBonusPaymentRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BasePaymentRequest<AUTH> {
    request: PaymentRequest;
    regulation?: MERCHANT_REGULATION;
}

/**
 * Request to make transfer-in/out internal wallet
 */
export interface TransferRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BasePaymentRequest<AUTH> {
    request: MerchantTransferRequest;
    regulation?: MERCHANT_REGULATION;
}

/**
 * Refund beet request
 */
export interface RefundRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BasePaymentRequest<AUTH> {
    request: RefundBetRequest;
    regulation?: MERCHANT_REGULATION;
}

/**
 * Request to finalize game
 */
export interface BrokenGameRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BasePaymentRequest<AUTH> {
    request: FinalizeGameRequest;
    regulation?: MERCHANT_REGULATION;
}

/**
 * Request to perform regulatory action
 */
export interface RegulatoryActionRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BasePaymentRequest<AUTH> {
    request: PlayerRegulatoryActionRequest;
    regulation?: MERCHANT_REGULATION;
}

/**
 * Offline bonus payment request
 */
export interface CommitOfflineBonusPaymentRequest extends BaseRequest{
    request: OfflineBonusPaymentRequest;
    regulation?: MERCHANT_REGULATION;
}

/**
 * Balance request
 */
export type BalanceRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData> = BasePaymentRequest<AUTH>;

export interface GetFreeBetInfoRequest<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BaseRequest {

    gameToken: AUTH;

    freeBetRequest: FreeBetInfoRequest;
}

/**
 * Support of split payment: separate bet request and separate win request
 */
export interface SplitPaymentSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BalanceSupport<AUTH> {
    commitWinPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance>;

    commitBetPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance>;
}

/**
 * Free bet payment support
 */
export interface FreeBetPaymentSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    commitFreeBetPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance>;

    commitFreeBetWinPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance>;
}

/**
 * Support of bonus payment
 */
export interface BonusPaymentSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    commitBonusPayment(req: CommitBonusPaymentRequest<AUTH>): Promise<Balance>;
}

/**
 *  Payment request when bet/win is in one request
 */
export interface PaymentSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData>
    extends BalanceSupport<AUTH> {
    commitPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance>;
}

/*
 *  Jackpot win payment support
 */
export interface JackpotPaymentSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    commitJackpotWinPayment(req: CommitPaymentRequest<AUTH>): Promise<Balance>;
}

/**
 *  Transfer-in/out internal wallet payment support
 */
export interface TransferPaymentSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    transferIn(req: TransferRequest<AUTH>): Promise<Balance>;

    transferOut(req: TransferRequest<AUTH>): Promise<Balance>;
}

/**
 * Refund bet support
 */
export interface RefundBetSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    refundBetPayment(req: RefundRequest<AUTH>): Promise<Balance>;
}

/**
 * Get balance support
 */
export interface BalanceSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    getBalances(req: BalanceRequest): Promise<Balances>;
}

/**
 * Broken game support
 */
export interface BrokenGameSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    finalizeWithOfflinePayments?(req: CommitPaymentRequest<AUTH>): Promise<Balance>;

    finalizeGame(req: BrokenGameRequest<AUTH>): Promise<Balance>;
}

/**
 * Regulation support
 */
export interface RegulationSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    performRegulatoryAction?(req: RegulatoryActionRequest<AUTH>): Promise<any>;
}

/**
 * Free bet info support
 */
export interface FreeBetInfoSupport<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    getFreeBetInfo(req: GetFreeBetInfoRequest<AUTH>): Promise<FreeBetInfo>;
}

/**
 * Offline bonus payment support
 */
export interface OfflineBonusPaymentSupport {
    commitOfflineBonusPayment?(req: CommitOfflineBonusPaymentRequest): Promise<OfflineBonusInfo>;
}

/**
 * Payment module configuration
 *
 * The configuration allows to specify optional payment methods, i.e. bonus/transfer/jackpot/etc.
 */
export interface PaymentModuleConfig<AUTH extends MerchantGameTokenData = MerchantGameTokenData> {
    /**
     * Http configuration (optional)
     * If this param is specified, the HttpGateway service is available for injection
     */
    http?: HttpHandlersConfig<PaymentService>;
    /**
     * Payment support (split and/or one payment request).
     * This should the provider of the service to inject through nest
     */
    payment?: ServiceProvider<PaymentSupport<AUTH> | SplitPaymentSupport<AUTH>>;
    /**
     * Refund support
     * This should the provider of the service to inject through nest
     */
    refund?: ServiceProvider<RefundBetSupport<AUTH>>;
    /**
     * Transfer support
     * This should the provider of the service to inject through nest**
     */
    transfer?: ServiceProvider<TransferPaymentSupport<AUTH>>;
    /**
     * Jackpot win payment support
     * This should the provider of the service to inject through nest**
     */
    jackpotPayment?: ServiceProvider<JackpotPaymentSupport<AUTH>>;
    /**
     * Bonus payment support
     * This should the provider of the service to inject through nest**
     */
    bonusPayment?: ServiceProvider<BonusPaymentSupport<AUTH>>;

    /**
     * Free payment support
     * This should the provider of the service to inject through nest
     */
    freeBetPayment?: ServiceProvider<FreeBetPaymentSupport<AUTH>>;

    /**
     * Broken game support
     * This should the provider of the service to inject through nest
     */
    brokenGame?: ServiceProvider<BrokenGameSupport<AUTH>>;

    /**
     * Regulation support
     * This should the provider of the service to inject through nest
     */
    regulation?: ServiceProvider<RegulationSupport<AUTH>>;

    freeBetInfo?: ServiceProvider<FreeBetInfoSupport<AUTH>>;

    offlineBonusPayment?: ServiceProvider<OfflineBonusPaymentSupport>;
}

/**
 * This decorator is for http handler of Payment interface.
 * If the PaymentModule is created with http configuration, the service will be injected to the specified
 * method of payment module.
 *
 * @param key the method of the Payment interface
 * @param options injection options
 */
export function HttpPaymentHandler<K extends keyof PaymentService>(
    key: K,
    options?: InjectableOptions
): ClassDecorator {
    return (target) => {
        Reflect.defineMetadata(SCOPE_OPTIONS_METADATA, options, target);
        let handlers: string[] = Reflect.getMetadata("http_handler", target);
        if (!handlers) handlers = [];
        handlers.push(key);
        Reflect.defineMetadata("http_handler", handlers, target);
        paymentHandlers.push(target);
    };
}

const paymentHandlers = [];

export function getPaymentHandlers(): any[] {
    return paymentHandlers;
}
