import { Inject, Injectable } from "@nestjs/common";
import * as http from "superagent";
import { HttpGateway, HttpGatewayConfig, HTTPOperatorRequest, HttpRequestOptions } from "./http";
import { Names } from "../names";
import { getSecuredObjectData, getSecuredXmlData, XmlService } from "@skywind-group/sw-wallet-adapter-core";
import { SuperAgentRequest } from "superagent";
import { ContentType } from "./contentType.enum";
import { RequestXmlBodyLog } from "./requestXmlBodyLog.interface";
import { ResponseXmlBodyLog } from "./responseXmlBodyLog.interface";

/**
 * Http utility to
 *  - build xml http request
 *  - send xml http request to operator
 *  - parse the xml response form operator
 */
@Injectable()
export class XmlHttpGateway extends HttpGateway {
    public constructor(
        @Inject(Names.HttpGatewayConfig) protected config: HttpGatewayConfig,
        @Inject(Names.XmlService) private xmlService: XmlService
    ) {
        super(config);
    }

    protected prepareRequest(
        req: SuperAgentRequest,
        httpReq: HTTPOperatorRequest,
        options: Partial<HttpRequestOptions>
    ): void {
        if (httpReq.contentType === ContentType.ApplicationXml) {
            req.buffer().type("xml");
        }
        super.prepareRequest(req, httpReq, options);
    }

    protected getSecuredData(data: any): any {
        return getSecuredXmlData(data, this.config.logSecureKeys);
    }

    protected prepareRequestBodyLog(data: any): RequestXmlBodyLog {
        return {
            xmlData: data,
            data: this.xmlService.convertToObject(data)
        };
    }

    protected prepareResponseBodyLog(response: http.Response): ResponseXmlBodyLog {
        const xmlData = response.text || response.body;
        return {
            xmlData: xmlData,
            data: this.xmlService.convertToObject(xmlData)
        };
    }

    protected prepareHttpRequestLogData(httpReq: HTTPOperatorRequest): RequestXmlBodyLog {
        return {
            xmlData: this.getSecuredData(httpReq.payload),
            data: getSecuredObjectData(this.xmlService.convertToObject(httpReq.payload), this.config.logSecureKeys)
        };
    }
}
