import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core/src/skywind/definitions/common";
import {
    GameLogoutRequest,
    GameLogoutResponse,
    LoginTerminalRequest,
    LoginTerminalResponse,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantStartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { BaseRequest, ServiceProvider } from "./types";
import { InjectableOptions } from "@nestjs/common";
import { HttpHandlersConfig } from "./http";
import { SCOPE_OPTIONS_METADATA } from "@nestjs/common/constants";

/**
 * Create game url request
 */
export interface CreateGameUrlRequest<REQ extends MerchantGameInitRequest = MerchantGameInitRequest>
    extends BaseRequest {
    gameCode: string;
    providerCode: string;
    providerGameCode: string;
    initRequest: REQ;
}

/**
 * Create game token request
 */
export interface CreateGameTokenRequest<SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData>
    extends BaseRequest {
    gameCode: string;
    startGameToken: SGT;
    currency: string;
    transferEnabled: boolean;
}

/**
 * Login player to terminal request
 */
export interface LoginTerminalPlayerRequest<LTR extends LoginTerminalRequest = LoginTerminalRequest>
    extends BaseRequest {
    initRequest: LTR;
}

/**
 * Keep request
 */
export interface KeepAliveRequest<GT extends MerchantGameTokenData = MerchantGameTokenData> {
    merchantInfo: MerchantInfo;
    gameToken: GT;
}

/**
 *  The start game service to :
 *   - create game url/ create game token
 *   - support keep alive
 *   - support login player to terminal
 */
export interface StartGameService<
    SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData,
    GT extends MerchantGameTokenData = MerchantGameTokenData,
    GIR extends MerchantGameInitRequest = MerchantGameInitRequest
>
    extends CreateGameTokenSupport<SGT, GT>,
        CreateGameUrlSupport<GIR>,
        KeepAliveSupport<GT>,
        LoginTerminalPlayerSupport<SGT>,
        LogoutGameSupport {}

/**
 *  Create game url/ create game token
 */
export interface CreateGameTokenSupport<
    SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData,
    GT extends MerchantGameTokenData = MerchantGameTokenData
> {
    createGameTokenData(req: CreateGameTokenRequest<SGT>): Promise<MerchantGameTokenInfo<GT>>;
}

/**
 *  Create game url/ create game token
 */
export interface CreateGameUrlSupport<GIR extends MerchantGameInitRequest = MerchantGameInitRequest> {
    createGameUrl(req: CreateGameUrlRequest<GIR>): Promise<MerchantGameURLInfo>;
}

/**
 *  Keep alive support
 */
export interface KeepAliveSupport<GT extends MerchantGameTokenData = MerchantGameTokenData> {
    keepAlive(req: KeepAliveRequest<GT>): Promise<void>;
}

/**
 *  Login player to terminal support
 */
export interface LoginTerminalPlayerSupport<SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData> {
    loginTerminalPlayer(req: LoginTerminalPlayerRequest): Promise<LoginTerminalResponse<SGT>>;
}

/**
 *  Logout game support
 */
export interface LogoutGameSupport {
    logoutGame(req: GameLogoutRequest): Promise<GameLogoutResponse>;
}

/**
 *  Start game module version
 */
export interface StartGameModuleConfig {
    /**
     * Optional http configuration.
     *
     * If it's specified we can use HttpGateway through injection mechanism
     */
    http?: HttpHandlersConfig<StartGameService>;
    /**
     *  Create game url
     */
    createGameURL?: ServiceProvider<CreateGameUrlSupport>;

    /**
     *  Create create game token functionality
     */
    createGameToken?: ServiceProvider<CreateGameTokenSupport>;
    /**
     * Optional keep alive support
     */
    keepAlive?: ServiceProvider<KeepAliveSupport>;
    /**
     * Login player to terminal support
     */
    loginTerminal?: ServiceProvider<LoginTerminalPlayerSupport>;
    /**
     * Logout game support
     */
    logoutGame?: ServiceProvider<LogoutGameSupport>;
}

/**
 * This decorator is for http handler of StartGame interface.
 * If the StartGameModule is created with http configuration, the service will be injected to the specified
 * method of start game module.
 *
 * @param key the method of the StartGameService interface
 * @param options injection options
 */
export function HttpStartGameHandler<K extends keyof StartGameService>(
    key: K,
    options?: InjectableOptions
): ClassDecorator {
    return (target) => {
        Reflect.defineMetadata(SCOPE_OPTIONS_METADATA, options, target);
        Reflect.defineMetadata("http_handler", key, target);
        startHandlers.push(target);
    };
}

const startHandlers = [];

export function getStartGameHandlers(): any[] {
    return startHandlers;
}
