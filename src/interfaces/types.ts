import { ClassProvider, DynamicModule, ExistingProvider, FactoryProvider, ValueProvider } from "@nestjs/common";
import { Type } from "@nestjs/common/interfaces/type.interface";
import { ForwardReference } from "@nestjs/common/interfaces/modules/forward-reference.interface";
import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";

/**
 * Utility type to describe provider for partial methods support
 */
export type ServiceProvider<T> =
    | Type<any>
    | Omit<ValueProvider<T>, "provide">
    | Omit<ClassProvider<T>, "provide">
    | Omit<ExistingProvider<T>, "provide">
    | Omit<FactoryProvider<T>, "provide">;

/**
 * impots type shortcut
 */
export type Imports = Array<Type<any> | DynamicModule | Promise<DynamicModule> | ForwardReference>;

/**
 * Base integration request that contains merchant information
 */
export interface BaseRequest {
    merchantInfo: MerchantInfo;
}
