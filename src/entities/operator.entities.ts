import { IsDefined, IsNotEmpty, IsOptional } from "class-validator";

export interface OperatorGameTokenData {
    channelId: number;
    jurisdiction: string;
    privateToken?: string; // Not required only for fun mode
    freeSpinToken?: string;
    siteUrl?: string;
    disablePlayerPhantomFeatures?: boolean; // It is AllowOffers from operator, which further used in GameProvider and GameServer
}

export class OperatorHistoryRequest {
    @IsDefined()
    @IsNotEmpty()
    publicToken: string;

    @IsDefined()
    @IsNotEmpty()
    jurisdiction: string;

    @IsOptional()
    @IsNotEmpty()
    language: string;

    @IsOptional()
    @IsNotEmpty()
    roundId?: number;

    @IsOptional()
    @IsNotEmpty()
    swRoundId?: string;

    @IsOptional()
    @IsNotEmpty()
    merchantCode?: string;

    @IsOptional()
    @IsNotEmpty()
    merchantType?: string;
}

export interface OperatorGameLaunchData {
    publicToken: string;
    channelId: number;
    freeSpinToken?: string;
    freePlayToken?: string; // IT regulation only
    jurisdiction: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface OperatorResponse {}
export interface OperatorErrorResponse {
    ErrorDetails: OperatorError[];
}
export interface OperatorError {
    ErrorCode: string;
    ErrorMessage: string;
}

export interface OperatorHeaders {
    authorization: string; // Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==
    apiVersion: string; // api-version: 3
}

export interface OperatorMetaDataRequest {
    MessageID: number;
    UTCTimeStamp: string;
}

export interface OperatorMetaDataResponse extends OperatorMetaDataRequest {
    ErrorDetails: OperatorError[];
}

export interface OperatorLoginRequest extends OperatorMetaDataRequest {
    PublicToken: string;
}

export type OperatorPlayCheckerRequest = OperatorLoginRequest;

export interface OperatorPlayCheckerResponse extends OperatorMetaDataResponse {
    GamingId: string;
}

export interface OperatorLoginResponse extends OperatorMetaDataResponse {
    GamingId: string;
    PrivateToken: string;
    CurrencyCode: string;
    CountryCode: string;
    VIPLevel: number;
    RelaunchUrl: string;
    AllowOffers?: boolean;
    WagerLimit?: number;
}

export interface OperatorGetBalanceRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    GamingId: string;
}

export interface OperatorGetBalanceResponse extends OperatorMetaDataResponse {
    Balance: OperatorBalance;
    CurrencyCode: string;
}

export interface OperatorStartGameRoundRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    GameCode: string;
    ChannelID: number;
    StartTimeUTC: string;
    ExternalGameRoundID: string; // 4-50 symbols
}
export interface OperatorStartGameRoundResponse extends OperatorMetaDataResponse {
    GameRoundID: string;
}

export interface OperatorEndGameRoundRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    GameRoundID: string;
    EndTimeUTC: string;
}

export type OperatorEndGameRoundResponse = OperatorMetaDataResponse;

export interface OperatorTransactionRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    TransactionType: OperatorTransactionType;
    ActionType: OperatorActionType;
    Amount: number;
    CurrencyCode: string;
    ExternalTransactionID: string; // 4-50 symbols
    JackpotDetails?: OperatorJackpotDetails[];
    GameRoundID: string;
}

export interface OperatorAwardPromRequest extends OperatorMetaDataRequest {
    ExternalTransactionID: string;
    GameCode: string;
    FreeSpinCount: number;
    PrivateToken: string;
}

export type OperatorAwardPromResponse = OperatorErrorResponse & OperatorMetaDataRequest;

export interface OperatorTransactionResponse extends OperatorMetaDataResponse {
    Balance: OperatorBalance;
    CurrencyCode: string;
    AmountInGBP: number;
    RealityCheck: OperatorRealityCheckDetails;
}

export interface OperatorCancelTransactionRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    ExternalTransactionIDToCancel: string;
    GameRoundID: string;
}

export interface OperatorCancelTransactionResponse extends OperatorMetaDataResponse {
    Balance: OperatorBalance;
    CurrencyCode: string;
    AmountInGBP: number;
}

export interface OperatorGetFreeSpinsRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    GameCode: string;
    FreeSpinToken: string;
}
export interface MockCreatePromo {
    StakeAmountPerLine: number;
    NumberOfLines: number;
    NumberOfFreeSpins: number;
    FreeSpinToken: string;
}

export interface OperatorGetFreeSpinsResponse extends OperatorMetaDataResponse {
    StakeAmountPerLine: number; // used as coin
    CurrencyCode: string;
    NumberOfLines: number;
    NumberOfSpinsRemaining: number;
}

export interface OperatorFreeSpinTransactionRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    TransactionType: OperatorTransactionType;
    Amount: number;
    CurrencyCode: string;
    ExternalTransactionID: string; // 4-50 symbols
    JackpotDetails?: OperatorJackpotDetails[];
    GameRoundID: string;
    FreeSpinToken: string;
}

export interface OperatorFreeSpinTransactionResponse extends OperatorMetaDataResponse {
    Balance: OperatorBalance;
    CurrencyCode: string;
    AmountInGBP: number;
    NumberOfSpinsRemaining: number;
}

export interface OperatorGetFreeSpinsSummaryRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    FreeSpinToken: string;
}

export interface OperatorGetFreeSpinsSummaryResponse extends OperatorMetaDataResponse {
    TotalSpinsUsed: number;
    TotalReturns: number; // Decimal number
    CurrencyCode: string;
    GamePlayCompleted: boolean; // ?
}

export interface OperatorGetRealityCheckRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
}

export interface OperatorGetRealityCheckResponse extends OperatorMetaDataResponse {
    RealityCheck: OperatorRealityCheckDetails;
}

export interface OperatorRealityCheckPlayerChoiceRequest extends OperatorMetaDataRequest {
    PrivateToken: string;
    Choice: OperatorRealityCheckPlayerChoice;
}

export type OperatorRealityCheckPlayerChoiceResponse = OperatorMetaDataResponse;

export interface OperatorBalance {
    TotalAmount: number; // Decimal number
}

export interface OperatorBalanceResponse {
    Balance: OperatorBalance;
}

export interface OperatorJackpotDetails {
    Amount: number; // Decimal number, Stake: max 7 decimal, Return: max 2 decimal
    Code: string; // JP Id
    NetworkId: string; // Can be null, if it is not supported by multiple providers
}

export interface OperatorRealityCheckDetails {
    RealityCheckStatus: OperatorRealityCheckStatus;
    Interval: number;
    TimeSinceLastAlert: number;
    TimeUntilNextAlert: number;
    TimeSinceLogin: number;
    AccountHistoryUrl: string; // ?
}

export enum OperatorTransactionType {
    Stake = "Stake",
    Return = "Return"
}

export enum OperatorActionType {
    Standard = "Standard",
    PartnerFreeSpin = "PartnerFreeSpin",
    TransferFunds = "TransferFunds"
}

export enum OperatorRealityCheckStatus {
    NotDue = "NotDue",
    Due = "Due",
    Disabled = "Disabled"
}

export enum OperatorRealityCheckPlayerChoice {
    Continue = "Continue",
    Logout = "Logout"
}

export enum OperatorPlatformType {
    mobile = "mobile",
    desktop = "desktop"
}

// New values should be included by the class-validator decorator @IsIn from IntegrationGameLaunchRequest
export enum OperatorJurisdiction {
    UK = "UK",
    IT = "IT",
    IGP = "IGP",
    COM = "COM",
    NL = "NL",
    MT = "MT",
    DE = "DE",
    BR = "BR",
    MX = "MX",
    GR = "GR",
    ES = "ES"
}

export enum TransactionPostfix {
    BET = "_bet",
    WIN = "_win",
    JACKPOT = "_jp",
    BONUS = "_bonus",
    AWARD_PROMO = "_award_promo"
}

export interface FileChecksum {
    FileName: string;
    Checksum: string; // sha1 checksum
}

export interface OperatorGameChecksumsRequest extends OperatorMetaDataRequest {
    ProviderGameReference: string; // game code
    Checksums: FileChecksum[];
}

export interface OperatorGameChecksumsResponse extends OperatorMetaDataResponse {
    AdmStatusCode: number;
    AdmErrorDetails: string;
}

export interface OperatorPlatformChecksumsRequest extends OperatorMetaDataRequest {
    Checksums: FileChecksum[];
}

export type OperatorPlatformChecksumsResponse = OperatorMetaDataResponse;
