import {
    Balance,
    MerchantGameInitRequest,
    MerchantGameURLInfo,
    MerchantInfo
} from "@skywind-group/sw-wallet-adapter-core";
import { OperatorGameLaunchData } from "@entities/operator.entities";
import { IntegrationInitRequest, IntegrationStartGameTokenData } from "@entities/integration.entities";

export interface SWGameInitRequest extends OperatorGameLaunchData, MerchantGameInitRequest {}

export interface SWGameLaunchResponse {
    // TODO: make it a common interface for all integrations
    url: string;
    token: string;
}

export interface SWUrlInfoRequest extends IntegrationInitRequest {
    gameCode: string;
    providerCode: string;
    providerGameCode: string;

    initRequest: IntegrationInitRequest;
    merchantInfo: MerchantInfo;
}

export interface SWMerchantGameHistoryResponse {
    imageUrl: string;
    ttl?: number;
}

export interface SWUrlInfo extends MerchantGameURLInfo {
    tokenData: IntegrationStartGameTokenData;
    urlParams: SWParams;
}

export interface SWParams {
    // TODO: update fields
    language: string;
    playmode: string;
    lobby: string;
    cashier: string;
    history_url?: string;
    history2_url?: string;
    platform?: string;
    lobbyUrl?: string;

    loggedInTime?: number;
    RegulationLimits?: string;
    RegulationSelfExclusion?: string;
    realityCheckTimeout?: string;
    RegulationType?: string;
    RegulationSelfTest?: string;

    // Italian regulation
    rg_link_gioco?: string;
    rg_link_adm_half?: string;
}

export interface GameInfo {
    // TODO: make it a common interface for all integrations
    gameCode: string;
    providerCode: string;
    providerGameCode: string;
}

export interface JackpotHistory {
    // TODO: make it a common interface for all integrations
    totalJpContribution?: number;
    totalJpWin?: number;
}

export interface RoundHistory extends JackpotHistory {
    // TODO: make it a common interface for all integrations
    roundId: string;
    gameId?: string;
    brandId: number;
    playerCode: string;
    device?: string;
    gameCode: string;
    currency: string;
    isTest: boolean;
    bet: number;
    win: number;
    revenue?: number;
    totalEvents?: number;
    balanceBefore?: number;
    balanceAfter?: number;
    broken?: boolean;
    firstTs?: string;
    ts?: string;
    finished?: boolean;
    internalRoundId?: string;
    credit?: number;
    debit?: number;
    ctrl?: number;
    spins?: SpinHistory[];
}

export interface SpinHistory {
    // TODO: make it a common interface for all integrations
    spinNumber: number;
    type: string;
    currency: string;
    bet: number;
    win: number;
    balanceBefore: number;
    balanceAfter: number;
    endOfRound: boolean;
    ts: string;
    test: boolean;
    isPayment: boolean;
    trxId?: string;
    totalJpContribution?: number;
    totalJpWin?: number;
}

export enum PROMO_STATUS {
    ACTIVE = "active"
}

export enum PROMO_OWNER {
    OPERATOR = "operator"
}

export enum PROMO_TYPE {
    FREEBET = "freebet"
}

export enum SW_PLAY_MODE {
    REAL = "real",
    FUN = "fun"
}

export enum SW_GAME_TYPE {
    SLOT = "slot",
    ACTION = "action",
    TABLE = "table",
    EXTERNAL = "external"
}

export enum SW_PLATFORM {
    WEB = "web",
    MOBILE = "mobile"
}

export interface SWUserLoginResponse {
    key: string;
    username: string;
    accessToken: string;
}

export const DEFAULT_LANGUAGE = "en";

export enum SWJurisdiction {
    GB2 = "GB2",
    GI = "GI",
    MT = "MT",
    IT = "IT",
    NL = "NL",
    DE = "DE",
    BR = "BR",
    MX = "MX",
    GR = "GR",
    ES = "ES"
}

export const UNKNOWN_BALANCE: Balance = { main: 0 };
