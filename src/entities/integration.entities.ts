import {
    FinalizeGameRequest,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantStartGameTokenData,
    MerchantTransferRequest,
    PaymentRequest,
    PlayMode
} from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorActionType,
    OperatorGameLaunchData,
    OperatorGameTokenData,
    OperatorJurisdiction,
    OperatorPlatformType
} from "@entities/operator.entities";
import {
    BrokenGameRequest,
    CommitBonusPaymentRequest,
    GetFreeBetInfoRequest,
    KeepAliveRequest,
    RegulatoryActionRequest,
    TransferRequest
} from "@skywind-group/sw-integration-core";
import { IsDefined, IsIn, IsNotEmpty, IsOptional } from "class-validator";

export class IntegrationGameLaunchRequest {
    @IsDefined()
    @IsNotEmpty()
    GameCode: string;

    @IsOptional()
    PublicToken?: string;

    @IsDefined()
    @IsNotEmpty()
    ChannelID: string;

    @IsOptional()
    FreeSpinToken?: string;

    @IsOptional()
    freeSpinToken?: string; // sent with camelCase for Italy instead of PascalCase

    @IsIn([PlayMode.REAL, PlayMode.FUN])
    playmode: string;

    @IsDefined()
    @IsNotEmpty()
    language: string;

    @IsOptional()
    @IsIn([
        OperatorJurisdiction.UK,
        OperatorJurisdiction.IT,
        OperatorJurisdiction.COM,
        OperatorJurisdiction.NL,
        OperatorJurisdiction.MT,
        OperatorJurisdiction.DE,
        OperatorJurisdiction.BR,
        OperatorJurisdiction.MX,
        OperatorJurisdiction.GR,
        OperatorJurisdiction.ES
    ])
    jurisdiction: OperatorJurisdiction;

    @IsOptional()
    @IsNotEmpty()
    historyUrl?: string;

    @IsOptional()
    @IsNotEmpty()
    lobbyUrl?: string;

    @IsOptional()
    @IsNotEmpty()
    cashierUrl?: string;

    @IsIn([OperatorPlatformType.desktop, OperatorPlatformType.mobile])
    platform: OperatorPlatformType; // values: desktop, mobile

    @IsOptional()
    @IsNotEmpty()
    ADM?: string; // Used for the italian jurisdiction

    @IsOptional()
    @IsNotEmpty()
    giocoresponsabile?: string; // Used for the italian jurisdiction

    @IsOptional()
    merchantCode?: string;

    @IsOptional()
    @IsNotEmpty()
    // optional param for testing - we need to be able to pick right merchant type with this param
    merchantType?: string;
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData, OperatorGameTokenData {
    relaunchUrl?: string; // Not required only for fun mode
    loginFailed?: boolean;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData, OperatorGameTokenData {}

export interface IntegrationInitRequest extends MerchantGameInitRequest, OperatorGameLaunchData {
    previousStartTokenData?: IntegrationStartGameTokenData;
    channelId: number;
    history_url?: string;
    platform?: string;
    ADM?: string;
    giocoresponsabile?: string;
}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
    actionType?: OperatorActionType;
}

export interface IntegrationTransferRequest extends TransferRequest<IntegrationGameTokenData> {
    request: MerchantTransferRequest;
    operatorRoundId?: string;
}

export interface IntegrationBrokenGameRequest extends BrokenGameRequest<IntegrationGameTokenData> {
    request: FinalizeGameRequest & PaymentRequest;
    operatorRoundId?: string;
}

export type IntegrationRegulatoryActionRequest = RegulatoryActionRequest<IntegrationGameTokenData>;

export type IntegrationKeepAliveRequest = KeepAliveRequest<IntegrationGameTokenData>;

export type IntegrationGetBalanceRequest = IntegrationPaymentRequest | IntegrationKeepAliveRequest;

export type IntegrationRealityCheckRequest = IntegrationPaymentRequest | IntegrationKeepAliveRequest;

export type IntegrationGetFreeBetInfoRequest = GetFreeBetInfoRequest<IntegrationGameTokenData>;

export interface IntegrationGameTokenDataHolder {
    gameToken?: IntegrationGameTokenData;
    gameTokenData?: IntegrationGameTokenData;
}
