import axios, { AxiosInstance } from 'axios';
import { config } from '../config';
import { RoundDetailsRequest, RoundDetailsResponse } from '../types/api';
import { ApiLogger } from '../utils/logger';

export class RoundDetailsService {
  private client: AxiosInstance;

  constructor() {
    if (!config.game.roundDetailsUrl) {
      throw new Error('Round details URL not configured');
    }

    this.client = axios.create({
      baseURL: config.game.roundDetailsUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async getRoundDetails(request: RoundDetailsRequest): Promise<RoundDetailsResponse> {
    try {
      this.validateRoundDetailsRequest(request);
      
      const params = new URLSearchParams({
        customer: request.customer,
        roundId: request.roundId,
        gameId: request.gameId,
        lang: request.lang,
      });

      ApiLogger.logRequest('getRoundDetails', request);
      
      const response = await this.client.get<RoundDetailsResponse>(`/?${params.toString()}`);
      
      ApiLogger.logResponse('getRoundDetails', response.data);
      
      return response.data;
    } catch (error) {
      ApiLogger.logError('getRoundDetails', error as Error, request);
      
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return {
          code: -2,
          status: 'NOT_FOUND',
        };
      }
      
      return {
        code: -1,
        status: 'UNKNOWN_ERROR',
      };
    }
  }

  private validateRoundDetailsRequest(request: RoundDetailsRequest): void {
    const requiredFields = ['customer', 'roundId', 'gameId', 'lang'];
    const missing = requiredFields.filter(field => !request[field as keyof RoundDetailsRequest]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required round details fields: ${missing.join(', ')}`);
    }

    if (request.gameId.length > 50) {
      throw new Error('Game ID must not exceed 50 characters');
    }
  }

  createSuccessResponse(
    type: 'json' | 'image' | 'html',
    data: unknown
  ): RoundDetailsResponse {
    const response: RoundDetailsResponse = {
      code: 0,
      status: 'SUCCESS',
    };

    switch (type) {
      case 'json':
        response.json = data as RoundDetailsResponse['json'];
        break;
      case 'image':
        response.image = data as RoundDetailsResponse['image'];
        break;
      case 'html':
        response.html = data as RoundDetailsResponse['html'];
        break;
      default:
        throw new Error(`Invalid response type: ${type}`);
    }

    return response;
  }

  createErrorResponse(code: -1 | -2, status: 'UNKNOWN_ERROR' | 'NOT_FOUND'): RoundDetailsResponse {
    return {
      code,
      status,
    };
  }

  formatRoundDate(date: Date): string {
    return date.toISOString();
  }

  validateJsonRoundDetails(data: RoundDetailsResponse['json']): void {
    if (!data) {
      throw new Error('JSON round details data is required');
    }

    const requiredFields = ['gameId', 'gameName', 'roundId', 'roundDate', 'betAmount', 'winAmount', 'currency'];
    const missing = requiredFields.filter(field => !data[field as keyof typeof data]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required JSON round details fields: ${missing.join(', ')}`);
    }

    if (typeof data.betAmount !== 'number' || data.betAmount < 0) {
      throw new Error('Bet amount must be a non-negative number');
    }

    if (typeof data.winAmount !== 'number' || data.winAmount < 0) {
      throw new Error('Win amount must be a non-negative number');
    }
  }

  validateImageRoundDetails(data: RoundDetailsResponse['image']): void {
    if (!data) {
      throw new Error('Image round details data is required');
    }

    if (!data.url) {
      throw new Error('Image URL is required');
    }

    try {
      new URL(data.url);
    } catch {
      throw new Error('Invalid image URL format');
    }

    if (data.height !== undefined && (typeof data.height !== 'number' || data.height <= 0)) {
      throw new Error('Image height must be a positive number');
    }

    if (data.width !== undefined && (typeof data.width !== 'number' || data.width <= 0)) {
      throw new Error('Image width must be a positive number');
    }
  }

  validateHtmlRoundDetails(data: RoundDetailsResponse['html']): void {
    if (!data) {
      throw new Error('HTML round details data is required');
    }

    if (!data.content) {
      throw new Error('HTML content is required');
    }

    if (data.height !== undefined && (typeof data.height !== 'number' || data.height <= 0)) {
      throw new Error('HTML height must be a positive number');
    }

    if (data.width !== undefined && (typeof data.width !== 'number' || data.width <= 0)) {
      throw new Error('HTML width must be a positive number');
    }
  }
}
