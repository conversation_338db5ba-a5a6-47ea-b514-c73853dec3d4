import { GameService } from './game';
import { GameOpenParams, DemoGameOpenParams } from '../types/api';

describe('GameService', () => {
  let gameService: GameService;

  beforeEach(() => {
    gameService = new GameService();
  });

  describe('buildGameUrl', () => {
    it('should build correct game URL with all parameters', () => {
      const params: GameOpenParams = {
        currency: 'USD',
        customer: '2019045569026',
        demo: false,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://casinoapi.sporting.dev.com/apiclient/gameslistsearch',
        platform: 'd',
        tableId: 'v1',
        token: '4DD8621822DE6',
        trader: 'ST499',
        country: 'SI'
      };

      const url = gameService.buildGameUrl(params);

      expect(url).toContain('currency=USD');
      expect(url).toContain('customer=2019045569026');
      expect(url).toContain('demo=false');
      expect(url).toContain('gameId=test_game');
      expect(url).toContain('lang=en');
      expect(url).toContain('platform=d');
      expect(url).toContain('tableId=v1');
      expect(url).toContain('token=4DD8621822DE6');
      expect(url).toContain('trader=ST499');
      expect(url).toContain('country=SI');
    });

    it('should build URL without optional parameters', () => {
      const params: GameOpenParams = {
        currency: 'USD',
        customer: '2019045569026',
        demo: false,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        trader: 'ST499'
      };

      const url = gameService.buildGameUrl(params);

      expect(url).toContain('currency=USD');
      expect(url).toContain('customer=2019045569026');
      expect(url).not.toContain('tableId=');
      expect(url).not.toContain('token=');
      expect(url).not.toContain('country=');
    });
  });

  describe('buildDemoGameUrl', () => {
    it('should build correct demo game URL', () => {
      const params: DemoGameOpenParams = {
        currency: 'FUN',
        demo: true,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        tableId: 'v1',
        trader: 'ST499'
      };

      const url = gameService.buildDemoGameUrl(params);

      expect(url).toContain('currency=FUN');
      expect(url).toContain('demo=true');
      expect(url).toContain('gameId=test_game');
      expect(url).toContain('lang=en');
      expect(url).toContain('platform=d');
      expect(url).toContain('tableId=v1');
      expect(url).toContain('trader=ST499');
    });
  });

  describe('validateGameParams', () => {
    it('should validate correct parameters', () => {
      const params: GameOpenParams = {
        currency: 'USD',
        customer: '2019045569026',
        demo: false,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        trader: 'ST499'
      };

      expect(() => gameService.validateGameParams(params)).not.toThrow();
    });

    it('should throw error for missing required fields', () => {
      const params = {
        currency: 'USD',
        customer: '2019045569026',
        demo: false,
        // missing gameId
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        trader: 'ST499'
      } as GameOpenParams;

      expect(() => gameService.validateGameParams(params)).toThrow('Missing required game parameters');
    });

    it('should throw error for invalid platform', () => {
      const params: GameOpenParams = {
        currency: 'USD',
        customer: '2019045569026',
        demo: false,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'invalid' as 'd',
        trader: 'ST499'
      };

      expect(() => gameService.validateGameParams(params)).toThrow('Platform must be "d" (desktop) or "m" (mobile)');
    });

    it('should throw error for gameId too long', () => {
      const params: GameOpenParams = {
        currency: 'USD',
        customer: '2019045569026',
        demo: false,
        gameId: 'a'.repeat(51), // 51 characters
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        trader: 'ST499'
      };

      expect(() => gameService.validateGameParams(params)).toThrow('Game ID must not exceed 50 characters');
    });
  });

  describe('validateDemoGameParams', () => {
    it('should validate correct demo parameters', () => {
      const params: DemoGameOpenParams = {
        currency: 'FUN',
        demo: true,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        trader: 'ST499'
      };

      expect(() => gameService.validateDemoGameParams(params)).not.toThrow();
    });

    it('should throw error for wrong currency', () => {
      const params: DemoGameOpenParams = {
        currency: 'USD' as 'FUN',
        demo: true,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        trader: 'ST499'
      };

      expect(() => gameService.validateDemoGameParams(params)).toThrow('Demo game currency must be "FUN"');
    });

    it('should throw error for demo not true', () => {
      const params: DemoGameOpenParams = {
        currency: 'FUN',
        demo: false as true,
        gameId: 'test_game',
        lang: 'en',
        lobby: 'http://example.com',
        platform: 'd',
        trader: 'ST499'
      };

      expect(() => gameService.validateDemoGameParams(params)).toThrow('Demo parameter must be true for demo games');
    });
  });
});
