import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { config } from '../config';
import { SecurityUtils } from '../utils/security';
import { ApiLogger } from '../utils/logger';

export class HttpClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: `${config.pces.baseEngineUrl}casino-engine/generic/${config.pces.vendorCode}/v5/`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Generic-Id': config.pces.genericId,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    this.client.interceptors.request.use(
      (config) => {
        if (config.data && typeof config.data === 'object') {
          const requestWithHash = SecurityUtils.addHashToRequest(config.data);
          config.data = requestWithHash;
          config.headers = {
            ...config.headers,
            'Hash': requestWithHash.hash,
          };
        }
        return config;
      },
      (error) => {
        ApiLogger.logError('Request Interceptor', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        ApiLogger.logError('Response Interceptor', error);
        return Promise.reject(error);
      }
    );
  }

  async post<T = unknown, R = unknown>(
    endpoint: string,
    data: T,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<R>> {
    try {
      ApiLogger.logRequest(endpoint, data);
      
      const response = await this.client.post<R>(endpoint, data, config);
      
      ApiLogger.logResponse(endpoint, response.data);
      
      return response;
    } catch (error) {
      ApiLogger.logError(endpoint, error as Error, data);
      throw error;
    }
  }

  async get<R = unknown>(
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<R>> {
    try {
      ApiLogger.logRequest(endpoint, config?.params);
      
      const response = await this.client.get<R>(endpoint, config);
      
      ApiLogger.logResponse(endpoint, response.data);
      
      return response;
    } catch (error) {
      ApiLogger.logError(endpoint, error as Error, config?.params);
      throw error;
    }
  }

  async delete<R = unknown>(
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<R>> {
    try {
      ApiLogger.logRequest(endpoint, config?.params);
      
      const response = await this.client.delete<R>(endpoint, config);
      
      ApiLogger.logResponse(endpoint, response.data);
      
      return response;
    } catch (error) {
      ApiLogger.logError(endpoint, error as Error, config?.params);
      throw error;
    }
  }
}
