import { HttpClient } from './http-client';
import {
  AuthRequest,
  AuthResponse,
  DebitCreditRequest,
  DebitRequest,
  CreditRequest,
  RollbackRequest,
  PromoRequest,
  BaseResponse,
} from '../types/api';
import { ApiLogger } from '../utils/logger';

export class WalletService {
  private httpClient: HttpClient;

  constructor() {
    this.httpClient = new HttpClient();
  }

  async authenticate(request: AuthRequest): Promise<AuthResponse> {
    try {
      const response = await this.httpClient.post<AuthRequest, AuthResponse>('auth', request);
      return response.data;
    } catch (error) {
      ApiLogger.logError('auth', error as Error, request);
      throw this.handleWalletError(error);
    }
  }

  async debitCredit(request: DebitCreditRequest): Promise<BaseResponse> {
    try {
      const response = await this.httpClient.post<DebitCreditRequest, BaseResponse>('debit-credit', request);
      return response.data;
    } catch (error) {
      ApiLogger.logError('debit-credit', error as Error, request);
      throw this.handleWalletError(error);
    }
  }

  async debit(request: DebitRequest): Promise<BaseResponse> {
    try {
      const response = await this.httpClient.post<DebitRequest, BaseResponse>('debit', request);
      return response.data;
    } catch (error) {
      ApiLogger.logError('debit', error as Error, request);
      throw this.handleWalletError(error);
    }
  }

  async credit(request: CreditRequest): Promise<BaseResponse> {
    try {
      const response = await this.httpClient.post<CreditRequest, BaseResponse>('credit', request);
      return response.data;
    } catch (error) {
      ApiLogger.logError('credit', error as Error, request);
      throw this.handleWalletError(error);
    }
  }

  async rollback(request: RollbackRequest): Promise<BaseResponse> {
    try {
      const response = await this.httpClient.post<RollbackRequest, BaseResponse>('rollback', request);
      return response.data;
    } catch (error) {
      ApiLogger.logError('rollback', error as Error, request);
      throw this.handleWalletError(error);
    }
  }

  async promo(request: PromoRequest): Promise<BaseResponse> {
    try {
      const response = await this.httpClient.post<PromoRequest, BaseResponse>('promo', request);
      return response.data;
    } catch (error) {
      ApiLogger.logError('promo', error as Error, request);
      throw this.handleWalletError(error);
    }
  }

  private handleWalletError(error: unknown): Error {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { data: { code: number; status: string } } };
      if (axiosError.response?.data) {
        const { code, status } = axiosError.response.data;
        return new WalletError(code, status);
      }
    }
    
    return new Error('Unknown wallet error occurred');
  }
}

export class WalletError extends Error {
  constructor(
    public readonly code: number,
    public readonly status: string
  ) {
    super(`Wallet Error ${code}: ${status}`);
    this.name = 'WalletError';
  }

  isRetryable(): boolean {
    const retryableCodes = [1, 6, -20101, -20304];
    return retryableCodes.includes(this.code);
  }

  shouldStopSending(): boolean {
    const stopCodes = [2, 3, 4, 5, 7, -20120, -20130, -20201, -20202, -20203, -20204, -20301, -20302, -20303, -20305, -20306, -20307, -20308, -20309, -20310, -20311, -20312, -20313, -20314, -20315, -20316];
    return stopCodes.includes(this.code);
  }

  shouldRevertRound(): boolean {
    const revertCodes = [-20201, -20202, -20203, -20204, -20302, -20307, -20308];
    return revertCodes.includes(this.code);
  }
}
