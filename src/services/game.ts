import { config } from '../config';
import { GameOpenParams, DemoGameOpenParams } from '../types/api';
import { ApiLogger } from '../utils/logger';

export class GameService {
  buildGameUrl(params: GameOpenParams): string {
    try {
      const url = new URL(config.game.gameUrl);
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });

      const gameUrl = url.toString();
      ApiLogger.logRequest('buildGameUrl', { params, gameUrl });
      
      return gameUrl;
    } catch (error) {
      ApiLogger.logError('buildGameUrl', error as Error, params);
      throw new Error('Failed to build game URL');
    }
  }

  buildDemoGameUrl(params: DemoGameOpenParams): string {
    try {
      const url = new URL(config.game.demoGameUrl);
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });

      const demoUrl = url.toString();
      ApiLogger.logRequest('buildDemoGameUrl', { params, demoUrl });
      
      return demoUrl;
    } catch (error) {
      ApiLogger.logError('buildDemoGameUrl', error as Error, params);
      throw new Error('Failed to build demo game URL');
    }
  }

  validateGameParams(params: GameOpenParams): void {
    const requiredFields = ['currency', 'customer', 'gameId', 'lang', 'lobby', 'platform', 'trader'];
    const missing = requiredFields.filter(field => !params[field as keyof GameOpenParams]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required game parameters: ${missing.join(', ')}`);
    }

    if (!['d', 'm'].includes(params.platform)) {
      throw new Error('Platform must be "d" (desktop) or "m" (mobile)');
    }

    if (params.gameId.length > 50) {
      throw new Error('Game ID must not exceed 50 characters');
    }

    if (params.tableId && params.tableId.length > 50) {
      throw new Error('Table ID must not exceed 50 characters');
    }
  }

  validateDemoGameParams(params: DemoGameOpenParams): void {
    if (params.currency !== 'FUN') {
      throw new Error('Demo game currency must be "FUN"');
    }

    if (params.demo !== true) {
      throw new Error('Demo parameter must be true for demo games');
    }

    const requiredFields = ['gameId', 'lang', 'lobby', 'platform', 'trader'];
    const missing = requiredFields.filter(field => !params[field as keyof DemoGameOpenParams]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required demo game parameters: ${missing.join(', ')}`);
    }

    if (!['d', 'm'].includes(params.platform)) {
      throw new Error('Platform must be "d" (desktop) or "m" (mobile)');
    }
  }

  createGameOpenParams(baseParams: Partial<GameOpenParams>): GameOpenParams {
    const defaultParams: Partial<GameOpenParams> = {
      lang: 'en',
      platform: 'd',
      demo: false,
    };

    const params = { ...defaultParams, ...baseParams } as GameOpenParams;
    this.validateGameParams(params);
    
    return params;
  }

  createDemoGameOpenParams(baseParams: Partial<DemoGameOpenParams>): DemoGameOpenParams {
    const defaultParams: Partial<DemoGameOpenParams> = {
      currency: 'FUN',
      demo: true,
      lang: 'en',
      platform: 'd',
    };

    const params = { ...defaultParams, ...baseParams } as DemoGameOpenParams;
    this.validateDemoGameParams(params);
    
    return params;
  }
}
