import axios, { AxiosInstance } from 'axios';
import { config } from '../config';
import { FreespinApiRequest } from '../types/api';
import { ApiLogger } from '../utils/logger';

export class FreespinService {
  private client: AxiosInstance;

  constructor() {
    if (!config.game.freespinUrl) {
      throw new Error('Freespin URL not configured');
    }

    this.client = axios.create({
      baseURL: config.game.freespinUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async giveFreespins(request: FreespinApiRequest): Promise<void> {
    try {
      this.validateFreespinRequest(request);
      
      ApiLogger.logRequest('giveFreespins', request);
      
      await this.client.post('/', request);
      
      ApiLogger.logResponse('giveFreespins', { success: true });
    } catch (error) {
      ApiLogger.logError('giveFreespins', error as Error, request);
      throw new Error(`Failed to give freespins: ${(error as Error).message}`);
    }
  }

  async cancelFreespins(freespinRef: string): Promise<void> {
    try {
      if (!freespinRef) {
        throw new Error('Freespin reference is required');
      }

      ApiLogger.logRequest('cancelFreespins', { freespinRef });
      
      await this.client.delete(`/${freespinRef}/`);
      
      ApiLogger.logResponse('cancelFreespins', { success: true });
    } catch (error) {
      ApiLogger.logError('cancelFreespins', error as Error, { freespinRef });
      throw new Error(`Failed to cancel freespins: ${(error as Error).message}`);
    }
  }

  private validateFreespinRequest(request: FreespinApiRequest): void {
    const requiredFields = [
      'customers',
      'games',
      'numberOfFreespins',
      'freespinRef',
      'wagerRequirement',
      'validFrom',
      'validUntil'
    ];

    const missing = requiredFields.filter(field => {
      const value = request[field as keyof FreespinApiRequest];
      return value === undefined || value === null;
    });

    if (missing.length > 0) {
      throw new Error(`Missing required freespin fields: ${missing.join(', ')}`);
    }

    if (!Array.isArray(request.customers) || request.customers.length === 0) {
      throw new Error('Customers must be a non-empty array');
    }

    if (!Array.isArray(request.games) || request.games.length === 0) {
      throw new Error('Games must be a non-empty array');
    }

    if (request.numberOfFreespins <= 0) {
      throw new Error('Number of freespins must be greater than 0');
    }

    if (request.wagerRequirement < 0) {
      throw new Error('Wager requirement cannot be negative');
    }

    if (new Date(request.validFrom) >= new Date(request.validUntil)) {
      throw new Error('Valid from date must be before valid until date');
    }

    if (request.maxWin !== undefined && request.maxWin < 0) {
      throw new Error('Max win cannot be negative');
    }

    if (request.costPerBet !== undefined && request.costPerBet < 0) {
      throw new Error('Cost per bet cannot be negative');
    }
  }

  createFreespinRequest(baseRequest: Partial<FreespinApiRequest>): FreespinApiRequest {
    const now = new Date();
    const defaultValidUntil = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000)); // 7 days from now

    const request: FreespinApiRequest = {
      customers: baseRequest.customers || [],
      games: baseRequest.games || [],
      numberOfFreespins: baseRequest.numberOfFreespins || 10,
      freespinRef: baseRequest.freespinRef || this.generateFreespinRef(),
      wagerRequirement: baseRequest.wagerRequirement || 0,
      validFrom: baseRequest.validFrom || now,
      validUntil: baseRequest.validUntil || defaultValidUntil,
      ...baseRequest,
    };

    this.validateFreespinRequest(request);
    return request;
  }

  private generateFreespinRef(): string {
    return `fs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
