import { WalletService, WalletError } from './wallet';
import { GameService } from './game';
import { FreespinService } from './freespin';
import { RoundDetailsService } from './round-details';
import { SecurityUtils } from '../utils/security';
import { ApiLogger, logger } from '../utils/logger';
import {
  AuthRequest,
  DebitCreditRequest,
  DebitRequest,
  CreditRequest,
  RollbackRequest,
  PromoRequest,
  GameOpenParams,
  DemoGameOpenParams,
  FreespinApiRequest,
  RoundDetailsRequest,
} from '../types/api';

export class IntegrationService {
  private walletService: WalletService;
  private gameService: GameService;
  private freespinService?: FreespinService;
  private roundDetailsService?: RoundDetailsService;

  constructor() {
    this.walletService = new WalletService();
    this.gameService = new GameService();
    
    try {
      this.freespinService = new FreespinService();
    } catch (error) {
      logger.warn('Freespin service not available', { error: (error as Error).message });
    }
    
    try {
      this.roundDetailsService = new RoundDetailsService();
    } catch (error) {
      logger.warn('Round details service not available', { error: (error as Error).message });
    }
  }

  async authenticatePlayer(customer: string, token: string): Promise<{ balance: number; bonusBalance: number; traderId: number }> {
    const request: AuthRequest = {
      customer,
      token,
      hash: '', // Will be added by HTTP client
    };

    const response = await this.walletService.authenticate(request);
    
    if (response.code !== 0) {
      throw new WalletError(response.code, response.status);
    }

    return {
      balance: response.balance || 0,
      bonusBalance: response.bonusBalance || 0,
      traderId: response.traderId || 0,
    };
  }

  async placeBetAndSettle(
    customer: string,
    token: string,
    gameId: string,
    betAmount: number,
    winAmount: number,
    currency: string
  ): Promise<{ balance: number; bonusBalance: number; trxId?: number; creditTrxId?: number }> {
    const request: DebitCreditRequest = {
      customer,
      token,
      gameId,
      amount: betAmount,
      creditAmount: winAmount,
      currency,
      betId: SecurityUtils.generateBetId(),
      trxId: SecurityUtils.generateTransactionId(),
      creditTrxId: SecurityUtils.generateTransactionId(),
      hash: '', // Will be added by HTTP client
    };

    const response = await this.walletService.debitCredit(request);
    
    if (response.code !== 0) {
      throw new WalletError(response.code, response.status);
    }

    return {
      balance: response.balance || 0,
      bonusBalance: response.bonusBalance || 0,
      trxId: response.trxId,
      creditTrxId: response.creditTrxId,
    };
  }

  async placeBet(
    customer: string,
    token: string,
    gameId: string,
    amount: number,
    currency: string
  ): Promise<{ balance: number; bonusBalance: number; trxId?: number; betId: string }> {
    const betId = SecurityUtils.generateBetId();
    const request: DebitRequest = {
      customer,
      token,
      gameId,
      amount,
      currency,
      betId,
      trxId: SecurityUtils.generateTransactionId(),
      hash: '', // Will be added by HTTP client
    };

    const response = await this.walletService.debit(request);
    
    if (response.code !== 0) {
      throw new WalletError(response.code, response.status);
    }

    return {
      balance: response.balance || 0,
      bonusBalance: response.bonusBalance || 0,
      trxId: response.trxId,
      betId,
    };
  }

  async settleBet(
    customer: string,
    token: string,
    gameId: string,
    amount: number,
    currency: string,
    betId: string
  ): Promise<{ balance: number; bonusBalance: number; trxId?: number }> {
    const request: CreditRequest = {
      customer,
      token,
      gameId,
      amount,
      currency,
      betId,
      trxId: SecurityUtils.generateTransactionId(),
      hash: '', // Will be added by HTTP client
    };

    const response = await this.walletService.credit(request);
    
    if (response.code !== 0) {
      throw new WalletError(response.code, response.status);
    }

    return {
      balance: response.balance || 0,
      bonusBalance: response.bonusBalance || 0,
      trxId: response.trxId,
    };
  }

  async rollbackTransaction(
    customer: string,
    token: string,
    gameId: string,
    trxId: string
  ): Promise<{ balance: number; bonusBalance: number; trxId?: number }> {
    const request: RollbackRequest = {
      customer,
      token,
      trxId,
      gameId,
      hash: '', // Will be added by HTTP client
    };

    const response = await this.walletService.rollback(request);
    
    if (response.code !== 0) {
      throw new WalletError(response.code, response.status);
    }

    return {
      balance: response.balance || 0,
      bonusBalance: response.bonusBalance || 0,
      trxId: response.trxId,
    };
  }

  async processPromotion(
    customer: string,
    token: string,
    gameId: string,
    amount: number,
    currency: string,
    promoType: PromoRequest['promo']['promoType'],
    promoRef: string
  ): Promise<{ balance: number; bonusBalance: number; trxId?: number }> {
    const request: PromoRequest = {
      customer,
      token,
      gameId,
      amount,
      currency,
      betId: SecurityUtils.generateBetId(),
      trxId: SecurityUtils.generateTransactionId(),
      promo: {
        promoType,
        promoRef,
      },
      hash: '', // Will be added by HTTP client
    };

    const response = await this.walletService.promo(request);
    
    if (response.code !== 0) {
      throw new WalletError(response.code, response.status);
    }

    return {
      balance: response.balance || 0,
      bonusBalance: response.bonusBalance || 0,
      trxId: response.trxId,
    };
  }

  buildGameUrl(params: GameOpenParams): string {
    return this.gameService.buildGameUrl(params);
  }

  buildDemoGameUrl(params: DemoGameOpenParams): string {
    return this.gameService.buildDemoGameUrl(params);
  }

  async giveFreespins(request: FreespinApiRequest): Promise<void> {
    if (!this.freespinService) {
      throw new Error('Freespin service not available');
    }
    
    return this.freespinService.giveFreespins(request);
  }

  async cancelFreespins(freespinRef: string): Promise<void> {
    if (!this.freespinService) {
      throw new Error('Freespin service not available');
    }
    
    return this.freespinService.cancelFreespins(freespinRef);
  }

  async getRoundDetails(request: RoundDetailsRequest) {
    if (!this.roundDetailsService) {
      throw new Error('Round details service not available');
    }
    
    return this.roundDetailsService.getRoundDetails(request);
  }
}
