/**
 * Should be the first Lines
 */
import { createInternalServer, createServer } from "./base";
import { INestApplication } from "@nestjs/common";
import { SwaggerModule } from "@nestjs/swagger";
import { BootstrapConfig, BootstrapMockConfig } from "./bootstrap.config";
import { logging } from "@skywind-group/sw-utils";
/**
 *  Startup function, include configuration of the internal server
 */
export async function bootstrapServer(bootstrapConfig: BootstrapConfig): Promise<INestApplication> {
    logging.setRootLogger(bootstrapConfig.serviceName);
    const app = await createServer(bootstrapConfig);
    await createInternalServer({
        port: bootstrapConfig.internalPort,
        versionFile: bootstrapConfig.versionFile,
        serviceName: bootstrapConfig.serviceName
    });
    return app;
}

export async function bootstrapMock(bootstrapConfig: BootstrapMockConfig): Promise<INestApplication> {
    logging.setRootLogger(bootstrapConfig.serviceName);
    await createInternalServer({
        port: bootstrapConfig.internalPort,
        versionFile: bootstrapConfig.versionFile,
        serviceName: bootstrapConfig.serviceName
    });

    const app = await createServer(bootstrapConfig);

    const swagger = require("../../resources/swagger.json");
    swagger.info.title = `${bootstrapConfig.serviceName.toUpperCase()} - ${swagger.info.title}`;
    swagger.info.description = `${bootstrapConfig.serviceName.toUpperCase()} - ${swagger.info.description}`;
    swagger.parameters.action.enum = bootstrapConfig.actions;
    SwaggerModule.setup("/docs", app, swagger);
    app.enableCors({ origin: "*" });

    return app;
}
