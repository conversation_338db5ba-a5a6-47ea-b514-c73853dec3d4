import { Type } from "@nestjs/common";

/**
 * Configuration of the server
 */
export interface BootstrapConfig {
    /**
     * Module to start
     */
    module: Type<any>;
    /**
     * The name of the service to return in the health check endpoint
     */
    serviceName: string;
    /**
     * The version file location
     */
    versionFile: string;
    /**
     * Internal server port
     */
    internalPort?: number;
    /**
     * Server port
     */
    port: number;
    /**
     * Secure Keys
     */
    secureKeys?: string[]
}

export interface BootstrapMockConfig extends BootstrapConfig {
    actions: string[];
}
