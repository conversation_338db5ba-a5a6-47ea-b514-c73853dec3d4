import { DynamicModule, Module, Type } from "@nestjs/common";
import { ServerInfoModule, ServerInfoModuleConfig } from "..";

@Module({})
export class BootstrapModule {
    public static register(moduleType: Type<any>, serverInfoModuleConfig: ServerInfoModuleConfig): DynamicModule {
        return {
            module: BootstrapModule,
            imports: [moduleType, ServerInfoModule.register(serverInfoModuleConfig)]
        };
    }
}
