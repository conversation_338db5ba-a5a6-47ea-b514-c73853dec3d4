import { SecurityUtils } from './security';

describe('SecurityUtils', () => {
  describe('calculateHash', () => {
    it('should calculate hash correctly', () => {
      const requestBody = {
        customer: '2020120400005',
        token: 'D8ECE868830CE30D467D0D715414D81D3685A0368FD7CBE257FBAB6599D6BE8CD2D8777328F804FB',
        gameId: 'Test_game',
        amount: '2',
        currency: 'TRY',
        betId: 5334875,
        trxId: 22198,
        freespin: {
          freespinRef: '23a234de532'
        }
      };

      const hash = SecurityUtils.calculateHash(requestBody);
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBe(64); // SHA-256 produces 64 character hex string
      expect(hash).toMatch(/^[a-f0-9]+$/); // Should be lowercase hex
    });

    it('should produce consistent hashes for same input', () => {
      const requestBody = { test: 'data' };
      
      const hash1 = SecurityUtils.calculateHash(requestBody);
      const hash2 = SecurityUtils.calculateHash(requestBody);
      
      expect(hash1).toBe(hash2);
    });

    it('should produce different hashes for different inputs', () => {
      const requestBody1 = { test: 'data1' };
      const requestBody2 = { test: 'data2' };
      
      const hash1 = SecurityUtils.calculateHash(requestBody1);
      const hash2 = SecurityUtils.calculateHash(requestBody2);
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('validateHash', () => {
    it('should validate correct hash', () => {
      const requestBody = { test: 'data' };
      const correctHash = SecurityUtils.calculateHash(requestBody);
      
      const isValid = SecurityUtils.validateHash(requestBody, correctHash);
      
      expect(isValid).toBe(true);
    });

    it('should reject incorrect hash', () => {
      const requestBody = { test: 'data' };
      const incorrectHash = 'incorrect_hash';
      
      const isValid = SecurityUtils.validateHash(requestBody, incorrectHash);
      
      expect(isValid).toBe(false);
    });

    it('should be case insensitive', () => {
      const requestBody = { test: 'data' };
      const hash = SecurityUtils.calculateHash(requestBody);
      const upperCaseHash = hash.toUpperCase();
      
      const isValid = SecurityUtils.validateHash(requestBody, upperCaseHash);
      
      expect(isValid).toBe(true);
    });
  });

  describe('addHashToRequest', () => {
    it('should add hash to request', () => {
      const requestBody = { test: 'data' };
      
      const requestWithHash = SecurityUtils.addHashToRequest(requestBody);
      
      expect(requestWithHash).toHaveProperty('hash');
      expect(requestWithHash.test).toBe('data');
      expect(typeof requestWithHash.hash).toBe('string');
    });

    it('should replace existing hash', () => {
      const requestBody = { test: 'data', hash: 'old_hash' };
      
      const requestWithHash = SecurityUtils.addHashToRequest(requestBody);
      
      expect(requestWithHash.hash).not.toBe('old_hash');
      expect(requestWithHash.test).toBe('data');
    });
  });

  describe('generateTransactionId', () => {
    it('should generate unique transaction IDs', () => {
      const id1 = SecurityUtils.generateTransactionId();
      const id2 = SecurityUtils.generateTransactionId();
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
      expect(id1.length).toBe(32); // 16 bytes = 32 hex characters
    });
  });

  describe('generateBetId', () => {
    it('should generate unique bet IDs', () => {
      const id1 = SecurityUtils.generateBetId();
      const id2 = SecurityUtils.generateBetId();
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
      expect(id1).toMatch(/^bet_\d+_[a-f0-9]{8}$/);
      expect(id2).toMatch(/^bet_\d+_[a-f0-9]{8}$/);
    });
  });

  describe('isTokenExpired', () => {
    it('should return false for recent token', () => {
      const recentTime = new Date(Date.now() - 60000); // 1 minute ago
      
      const isExpired = SecurityUtils.isTokenExpired(recentTime);
      
      expect(isExpired).toBe(false);
    });

    it('should return true for old token', () => {
      const oldTime = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      
      const isExpired = SecurityUtils.isTokenExpired(oldTime);
      
      expect(isExpired).toBe(true);
    });
  });
});
