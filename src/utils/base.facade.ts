import { OperationForbidden } from "../errors";
import { supportCheckImplementation } from "../utils/checkExistsSupport";

/**
 * Base class for facades
 */
export class BaseFacade {
    /**
     * Check if the object has implementation of the method
     * If not then raise OperationForbidden error
     *
     * @param obj object to check
     * @param key object key
     */
    protected checkExists<T, K extends keyof T>(obj: T, key: K): T[K] {
        if (!this.exists(obj, key)) {
            throw new OperationForbidden(`method ${key} is not implemented`);
        }
        const func: () => any = obj[key] as any;
        return func.bind(obj);
    }

    /**
     * Check if the object has implementation of the method
     *
     * @param obj object to check
     * @param key object key
     */
    protected exists<T, K extends keyof T>(obj: T, key: K): boolean {
        return (
            (obj && !!obj[key] && !supportCheckImplementation<T>(obj)) ||
            (supportCheckImplementation<T>(obj) && obj.isImplemented(key))
        );
    }
}
