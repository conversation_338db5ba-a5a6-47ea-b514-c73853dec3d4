import { retry as retryWithConfig, RetryConfig } from "@skywind-group/sw-utils";
import { isFunction } from "util";
import { ConnectionError } from "@skywind-group/sw-wallet-adapter-core";
import { OperatorTransientError } from "../errors";

export type RetryFunction = (err?: Error) => Promise<boolean>;

export async function retry<T>(
    retryConfig: RetryConfig | RetryFunction,
    action: () => Promise<T>,
    shouldRetry?: (err: Error) => boolean,
    wrapError?: (err: Error) => Error
): Promise<T> {
    if (retryConfig) {
        if (isFunction(retryConfig)) {
            const retryFunction = retryConfig as RetryFunction;
            do {
                try {
                    return await action();
                } catch (err) {
                    if (!(await retryFunction(err))) {
                        throw err;
                    }
                }
            } while (true);
        } else {
            const defaultShouldRetry = (err) => {
                return err instanceof ConnectionError || err instanceof OperatorTransientError;
            };

            return retryWithConfig(retryConfig as RetryConfig, action, shouldRetry || defaultShouldRetry, wrapError);
        }
    } else {
        return action();
    }
}
