import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";
import { validate, ValidationError } from "class-validator";
import { plainToClass } from "class-transformer";
import { ValidationError as SwValidationError } from "@errors/sw.errors";

@Injectable()
export class ValidationPipe implements PipeTransform<any> {
    async transform(value: any, { metatype }: ArgumentMetadata) {
        if (!metatype || this.isNative(metatype)) {
            return value;
        }
        const object = plainToClass(metatype, value);
        const errors: ValidationError[] = await validate(object);

        if (errors.length) {
            throw new SwValidationError(this.convertValidationErrors(errors));
        }
        return value;
    }

    private isNative(metatype: Function): boolean {
        const types: Function[] = [String, Boolean, Number, Array, Object];
        return types.includes(metatype);
    }

    private convertValidationErrors(errors: ValidationError[]): string {
        return errors
            .map((error: ValidationError) => Object.keys(error.constraints).map((k) => error.constraints[k])[0])
            .toString();
    }
}
