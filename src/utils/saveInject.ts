import { isClass } from "@nestjs/core/middleware/utils";
import { ServiceProvider } from "../interfaces/types";
import { ExistingProvider, Provider } from "@nestjs/common";

export function safeInject<T>(service: ServiceProvider<T>, ...provide: string[]): Provider[] {
    if (isClass(service)) {
        return [
            service,
            ...provide.map((p) => {
                return { useExisting: service, provide: p } as ExistingProvider;
            })
        ];
    } else {
        return provide.map((p) => {
            return { ...service, provide: p } as ExistingProvider;
        });
    }
}
