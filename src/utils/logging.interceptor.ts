import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { Observable } from "rxjs";
import { tap } from "rxjs/operators";
import { logging, measures } from "@skywind-group/sw-utils";
import { HttpArgumentsHost } from "@nestjs/common/interfaces";
import { FastifyRequest } from "fastify";
import measureProvider = measures.measureProvider;
import { InfoController } from "../internal/info.controller";
import { getSecuredObjectData } from "@skywind-group/sw-wallet-adapter-core";

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
    private logger = logging.logger("request-response");

    constructor(private secureKeys) {}

    public intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const httpArgumentsHost: HttpArgumentsHost = context.switchToHttp();
        const request: FastifyRequest = httpArgumentsHost.getRequest();
        if (context.getClass() !== InfoController) {
            const info = {
                method: `${context.getClass()?.name}.${context.getHandler()?.name}`,
                url: measureProvider.getTransaction()
            };

            this.logger.info(
                {
                    ...info,
                    body: request.body && getSecuredObjectData(request.body, this.secureKeys),
                    query: request.query,
                    headers: request.headers
                },
                "Request"
            );

            return next.handle().pipe(tap((res) => this.logger.info({ ...info, response: res }, "Response")));
        } else {
            return next.handle();
        }
    }
}
