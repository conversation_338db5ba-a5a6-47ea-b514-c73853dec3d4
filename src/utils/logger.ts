import winston from 'winston';
import { config } from '../config';

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

const transports: winston.transport[] = [
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
  }),
];

if (config.logging.file) {
  transports.push(
    new winston.transports.File({
      filename: config.logging.file,
      format: logFormat,
    })
  );
}

export const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports,
  defaultMeta: {
    service: 'sw-integration-pronet',
  },
});

export class ApiLogger {
  static logRequest(endpoint: string, requestData: unknown): void {
    logger.info('API Request', {
      endpoint,
      requestData: this.sanitizeLogData(requestData),
    });
  }

  static logResponse(endpoint: string, responseData: unknown): void {
    logger.info('API Response', {
      endpoint,
      responseData: this.sanitizeLogData(responseData),
    });
  }

  static logError(endpoint: string, error: Error, requestData?: unknown): void {
    logger.error('API Error', {
      endpoint,
      error: error.message,
      stack: error.stack,
      requestData: requestData ? this.sanitizeLogData(requestData) : undefined,
    });
  }

  private static sanitizeLogData(data: unknown): unknown {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sanitized = { ...data as Record<string, unknown> };
    
    const sensitiveFields = ['hash', 'token', 'genericSecretKey', 'password'];
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***REDACTED***';
      }
    }

    return sanitized;
  }
}
