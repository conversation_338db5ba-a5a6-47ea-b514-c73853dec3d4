import { Balance, Balances, MerchantGameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import {
    BalanceRequest,
    CommitBonusPaymentRequest,
    CommitPaymentRequest,
    PaymentService,
    RefundRequest,
    TransferRequest
} from "../interfaces/payment.service";
import { HttpGateway, HttpHandler } from "../interfaces/http";
import { Injectable } from "@nestjs/common";
import { ModuleRef } from "@nestjs/core";
import { OnModuleInit } from "@nestjs/common/interfaces/hooks/on-init.interface";
import { getHandlerName } from "../utils/handler";
import { OperationForbidden } from "../errors";
import { CheckImplementationSupport, CheckProviderService } from "../utils/checkExistsSupport";

export class HttpBaseAdapter<T> implements OnModuleInit, CheckImplementationSupport<T> {
    protected httpGateway: HttpGateway;
    protected checkProviderService: CheckProviderService;

    constructor(private readonly moduleRef: ModuleRef) {}

    public onModuleInit(): any {
        this.httpGateway = this.moduleRef.get(HttpGateway);
        this.checkProviderService = this.moduleRef.get(CheckProviderService);
    }

    protected getHandle<K extends keyof T>(handler: K): HttpHandler {
        if (!this.isImplemented(handler)) {
            throw new OperationForbidden(`Unsupported http handler ${handler}`);
        }
        return this.moduleRef.get<HttpHandler>(getHandlerName(handler as string));
    }

    public isImplemented<K extends keyof T>(key: K): boolean {
        return this.checkProviderService.exists(getHandlerName(key as string));
    }
}
