import crypto from 'crypto';
import { config } from '../config';

export class SecurityUtils {
  static calculateHash(requestBody: Record<string, unknown>): string {
    const jsonString = JSON.stringify(requestBody);
    const toHash = jsonString + config.pces.genericSecretKey;
    
    return crypto
      .createHash(config.security.hashAlgorithm)
      .update(toHash)
      .digest('hex')
      .toLowerCase();
  }

  static validateHash(requestBody: Record<string, unknown>, receivedHash: string): boolean {
    const calculatedHash = this.calculateHash(requestBody);
    return calculatedHash === receivedHash.toLowerCase();
  }

  static addHashToRequest<T extends Record<string, unknown>>(requestBody: T): T & { hash: string } {
    const bodyWithoutHash = { ...requestBody };
    delete bodyWithoutHash.hash;
    
    const hash = this.calculateHash(bodyWithoutHash);
    
    return {
      ...requestBody,
      hash,
    };
  }

  static generateTransactionId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  static generateBetId(): string {
    return `bet_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
  }

  static isTokenExpired(tokenCreatedAt: Date): boolean {
    const now = new Date();
    const expirationTime = new Date(tokenCreatedAt.getTime() + (config.security.tokenTimeoutMinutes * 60 * 1000));
    return now > expirationTime;
  }
}
