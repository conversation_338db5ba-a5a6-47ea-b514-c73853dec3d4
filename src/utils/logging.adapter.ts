import { LoggerService } from "@nestjs/common";
import { logging } from "@skywind-group/sw-utils";

export class <PERSON>ggerAdapter implements LoggerService {
    private readonly logger: logging.Logger;

    constructor(name: string) {
        this.logger = logging.logger(name);
    }

    public log(message: string): void {
        this.logger.info(message);
    }

    public error(message: string, trace: string): void {
        this.logger.error(message, trace);
    }

    public warn(message: string): void {
        this.logger.warn(message);
    }

    public debug(message: string): void {
        this.logger.debug(message);
    }

    public verbose(message: string): void {
        this.logger.debug(message);
    }
}
