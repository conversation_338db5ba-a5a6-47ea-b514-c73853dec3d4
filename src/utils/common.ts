import config from "@config";
import { generator } from "random-number";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import * as swErrors from "@errors/sw.errors";
import { RoundIsAlreadyClosedError } from "@errors/sw.errors";
import { SWJurisdiction } from "@entities/sw.entities";
import * as lookup from "country-code-lookup";

export function sumMajorUnits(a: number = 0, b: number = 0): number {
    const aggregator = Math.round(config.currencyUnitMultiplier * a) + Math.round(config.currencyUnitMultiplier * b);
    return aggregator / config.currencyUnitMultiplier;
}

export function sumMajorUnitsInArray(units: number[]): number {
    const aggregator = units.reduce(
        (acc: number, unit: number = 0) => acc + Math.round(config.currencyUnitMultiplier * unit),
        0
    );
    return aggregator / config.currencyUnitMultiplier;
}

export function buildMerchantCode(jurisdiction: string, merchantCode?: string): string {
    return `${merchantCode || config.merchantCode}__${jurisdiction}`;
}

export function generateNumber(length: number = config.messageIdNumberLength) {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length);
    return generator({ min, max, integer: true })();
}

export function createDateStringWithOperatorFormat(value?: any): string {
    const result = value ? new Date(value).toISOString() : new Date().toISOString();
    return result.substr(0, result.length - 1) + "0000Z";
}

export function convertSecondsToTime(value: number) {
    if (!value) {
        return "";
    }
    return new Date(value * 1000).toISOString().substr(11, 8);
}

export function roundIsAlreadyClosed(error: SWError): boolean {
    return (
        (error instanceof swErrors.TransactionNotFound && error.isResubmissionError) ||
        error instanceof RoundIsAlreadyClosedError
    );
}

export function safeDecodeURIComponent(uri: string): string {
    try {
        return decodeURIComponent(uri);
    } catch (err) {
        return;
    }
}

export function getApiVersion(jurisdiction?: string) {
    switch (jurisdiction) {
        case SWJurisdiction.IT:
            return config.italyOperatorApiVersion;
        default:
            return config.operatorApiVersion;
    }
}

// gets Alpha-2 country code
export function getTwoLetterCountryCode(countryCode?: string): string {
    if (countryCode) {
        if (countryCode.length === 2) {
            // Country is Alpha-2
            return countryCode;
        }
        const country = lookup.byIso(countryCode);
        if (country) {
            return country.iso2;
        }
    }
    return null;
}
