import { ArgumentsHost, Catch, ExceptionFilter, HttpException } from "@nestjs/common";
import { FastifyReply } from "fastify";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { logging, measures } from "@skywind-group/sw-utils";

@Catch()
export class ErrorFilter implements ExceptionFilter {
    private readonly logger: logging.Logger = logging.logger("error");

    public catch(error: Error, host: ArgumentsHost) {
        if (error) {
            measures.measureProvider.saveError(error);

            const ctx = host.switchToHttp();

            const response = ctx.getResponse<FastifyReply<any>>();

            if (this.isSWError(error)) {
                this.logger[error?.getErrorLevel() || "error"](error, "SWError");
                response.status(error.responseStatus || 500).send({
                    code: error.code,
                    message: error.message,
                    extraData: error.extraData,
                    data: error.data
                });
            } else if (error instanceof HttpException) {
                this.logger.error(error, "Http error");
                response.status(error.getStatus() || 500).send({
                    code: 1,
                    message: error.message
                });
            } else {
                this.logger.error(error, "Internal server error");
                response.status(500).send({
                    code: 1,
                    message: `Internal server error: ${error.message}`
                });
            }
        }
    }

    public isSWError(err: any): err is SWError {
        return err.responseStatus && err.code;
    }
}
