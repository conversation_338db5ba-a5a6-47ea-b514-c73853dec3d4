import { suite, test } from "mocha-typescript";

import * as sinon from "sinon";
import { retry } from "./retry";
import { assert, expect } from "chai";

@suite()
class RetrySpec {
    public async after() {
        sinon.restore();
    }

    @test
    public async skipRetry() {
        const action = () => {
            throw new Error("skipRetry");
        };

        try {
            await retry(undefined, action);
            assert.fail("Expect error");
        } catch (err) {
            expect(err.message).eq("skipRetry");
        }
    }

    @test
    public async testCustomRetry() {
        const action = () => {
            throw new Error("customRetry");
        };

        let retryCounter = 3;
        const customRetry = async (err: Error) => {
            return --retryCounter > 0;
        };

        try {
            await retry(customRetry, action);
            assert.fail("Expect error");
        } catch (err) {
            expect(err.message).eq("customRetry");
        }
        expect(retryCounter).eq(0);
    }

    @test
    public async testCustomRetrySuccess() {
        let retryCounter = 3;
        const action = async () => {
            if (retryCounter > 1) {
                throw new Error("customRetry");
            }
            return "ok";
        };

        const customRetry = async (err: Error) => {
            return --retryCounter > 0;
        };

        expect(await retry(customRetry, action)).eq("ok");
        expect(retryCounter).eq(1);
    }
}
