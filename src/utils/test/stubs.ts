import { RoundRepository } from "@wallet/round/round.repository";
import { RoundEntity } from "@wallet/round/round.entity";
import { RoundDto } from "@wallet/round/round.dto";

export const roundRepository = ({
    findOne: () => {
        /*empty*/
    },
    insertOne: () => {
        /*empty*/
    },
    create: (data: any) => {
        return returnFakeRoundEntity(data);
    }
} as any) as RoundRepository;

export function returnFakeRoundEntity(data: any): RoundEntity {
    return {
        ...data,
        toInfo(): RoundDto {
            return {
                operatorRoundId: this.operatorRoundId,
                swRoundId: this.swRoundId
            };
        }
    } as RoundEntity;
}
