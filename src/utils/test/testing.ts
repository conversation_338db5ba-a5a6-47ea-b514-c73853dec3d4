import { testing } from "@skywind-group/sw-utils";
import { expect } from "chai";

import * as _ from "lodash";

export interface ExpectedRequest {
    url: string;
    body: { [field: string]: any };
    headers: { [field: string]: any };
    query: { [field: string]: any };
    method: string;
    keysToVoid?: KeysToVoid;
}

export interface KeysToVoid {
    headers?: string[];
    body?: string[];
    query?: string[];
}

export function checkRequests(requestMock: testing.RequestMock, expectedRequests: ExpectedRequest[] = []) {
    expectedRequests.forEach((eRequest, i) => {
        const actualRequest = _.cloneDeep(requestMock.args[i]) as ExpectedRequest;
        eRequest.keysToVoid &&
            Object.keys(eRequest.keysToVoid).forEach((part) => {
                actualRequest[part] = voidObjectKeys(actualRequest[part], eRequest.keysToVoid[part]);
            });

        delete eRequest.keysToVoid;
        expect(actualRequest).deep.eq(eRequest);
    });
}

export function voidObjectKeys(obj: any, keys: string[]): any {
    if (!keys.length) {
        return;
    }
    for (const key of keys) {
        if (obj.hasOwnProperty(key)) {
            delete obj[key];
        }
    }
    return obj;
}
