import { Logger } from "typeorm/index";
import { QueryRunner } from "typeorm/query-runner/QueryRunner";
import { logging } from "@skywind-group/sw-utils";

export interface IndexedParameters {
    [index: string]: any; // index starts from 1
}

export class DbLogger implements Logger {
    private readonly logger: logging.Logger;

    constructor() {
        this.logger = logging.logger("postgres");
    }

    logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
        this.logger.info({ parameters: this.getIndexedParameters(parameters) }, query);
    }

    logQueryError(error: string | Error, query: string, parameters?: any[], queryRunner?: QueryRunner) {
        this.logger.error(
            { parameters: this.getIndexedParameters(parameters) },
            `Error: ${error.toString()}, query: ${query}`
        );
    }

    logQuerySlow(time: number, query: string, parameters?: any[], queryRunner?: QueryRunner) {
        this.logger.warn({ parameters: this.getIndexedParameters(parameters) }, `Time: ${time}, query: ${query}`);
    }

    logSchemaBuild(message: string, queryRunner?: QueryRunner) {
        this.logger.info(message);
    }

    logMigration(message: string, queryRunner?: QueryRunner) {
        this.logger.info(message);
    }

    log(level: "log" | "info" | "warn", message: any, queryRunner?: QueryRunner) {
        switch (level) {
            case "log":
                this.logger.info(message);
            case "info":
                this.logger.info(message);
                break;
            case "warn":
                this.logger.error(message);
                break;
        }
    }

    getIndexedParameters(parameters: any[] = []): IndexedParameters {
        const indexedParameters: IndexedParameters = {};
        for (const [i, v] of Object.entries(parameters)) {
            indexedParameters[`$${+i + 1}`] = v;
        }
        return indexedParameters;
    }
}
