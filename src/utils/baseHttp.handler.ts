import config from "@config";
import { HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { OperatorError, OperatorJurisdiction } from "@entities/operator.entities";
import { logging, safeGet, sleep } from "@skywind-group/sw-utils";
import { ConnectionError, getSecuredObjectData, MerchantInfo, SWError } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import { mapOperatorToSWError } from "@errors/operator.errors";
import * as swErrors from "@errors/sw.errors";
import { SWJurisdiction } from "@entities/sw.entities";
import { IntegrationGameTokenData, IntegrationGameTokenDataHolder } from "@entities/integration.entities";
import { createDateStringWithOperatorFormat, generateNumber, getApiVersion } from "@utils/common";

export type HTTP_METHOD = "get" | "post" | "put" | "delete";

export interface HttpHandlerRequest {
    path: string;
    method: HTTP_METHOD;
    payload: any;
    jurisdiction: string;
    merchantInfo: MerchantInfo;
    retryAvailable: boolean;
}

/* All responses will contain a field called ErrorDetails which will be a list of any errors that may have
 occurred in the request. They will only be returned when the status code is not success (200, 201 and 202),
 otherwise the value will be null. The list will contain the following for each error:
 */
export const successResponses = [200, 201, 202];

export class BaseHttpHandler {
    protected readonly log = logging.logger("http-handler");

    protected async buildHttpRequest({
        path,
        method,
        payload,
        jurisdiction,
        merchantInfo,
        retryAvailable = true
    }: HttpHandlerRequest): Promise<HTTPOperatorRequest> {
        const httpRequest: HTTPOperatorRequest = {
            baseUrl: merchantInfo.params.serverUrl,
            uri: this.buildUri(path, jurisdiction),
            method: method,
            payload: payload,
            options: {
                headers: {
                    "api-version": getApiVersion(jurisdiction)
                },
                username: merchantInfo.params.username,
                password: merchantInfo.params.password
            },
            retry: retryAvailable ? this.retry() : undefined
        };
        this.log.debug(getSecuredObjectData(httpRequest, config.securedKeys), "Http request");

        return httpRequest;
    }

    protected buildBaseRequest() {
        return {
            MessageID: generateNumber(),
            UTCTimeStamp: createDateStringWithOperatorFormat()
        };
    }

    public buildUri(path: string, jurisdiction: string) {
        // MEMO: GIB, MGA, GB2 are considered as UK operator regulatory
        const correctJurisdiction = this.getJurisdiction(jurisdiction);
        return `${correctJurisdiction}/${path}`;
    }

    private getJurisdiction(jurisdiction?: string) {
        switch (jurisdiction) {
            case SWJurisdiction.GI:
            case SWJurisdiction.MT:
            case SWJurisdiction.GB2:
                return OperatorJurisdiction.UK;
            case SWJurisdiction.IT:
                return OperatorJurisdiction.IGP;
            case SWJurisdiction.GR:
                return OperatorJurisdiction.GR;
            case SWJurisdiction.ES:
                return OperatorJurisdiction.ES;
            default:
                return jurisdiction;
        }
    }

    public async parseHttpResponse<O_RES = any>(response: superagent.Response): Promise<O_RES> {
        this.log.debug(getSecuredObjectData(response.body, config.securedKeys), "Http response");

        if (!successResponses.includes(response.status)) {
            const operatorError: OperatorError = this.getOperatorError(response);
            const swError: SWError = mapOperatorToSWError(operatorError, response);
            this.logError(operatorError, swError);
            throw swError;
        }
        return response.body;
    }

    public retry(): (err) => Promise<boolean> {
        let attempt = config.operator.retryPolicy.attempts;
        return async (err: Error): Promise<boolean> => {
            if ((err instanceof ConnectionError || err instanceof swErrors.GeneralError) && --attempt > 0) {
                await sleep(config.operator.retryPolicy.sleepInterval);
                return true;
            } else {
                return false;
            }
        };
    }

    private getOperatorError(response: superagent.Response): OperatorError {
        const errors: OperatorError[] = safeGet(response, "body", "ErrorDetails");
        return (errors && errors.length && errors[0]) || ({} as OperatorError);
    }

    private logError(operatorError: OperatorError, swError: SWError) {
        this.log.error({
            ...swError,
            message: `${operatorError.ErrorCode}: ${operatorError.ErrorMessage}`
        } as SWError);
    }

    public getGameTokenData<T extends IntegrationGameTokenDataHolder>(req: T): IntegrationGameTokenData {
        return req.gameTokenData || req.gameToken;
    }
}
