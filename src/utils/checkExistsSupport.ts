import { ModuleRef } from "@nestjs/core";
import { Injectable } from "@nestjs/common";

/**
 * Check if the method has 'real' implementation.
 * It use for the cases when optional method is implemented,
 * but it delegates the flow the another optional component
 */
export interface CheckImplementationSupport<T> {
    isImplemented<K extends keyof T>(key: K): boolean;
}

export function supportCheckImplementation<T>(obj: any): obj is CheckImplementationSupport<T> {
    const casted: CheckImplementationSupport<T> = obj as any;
    return !!casted?.isImplemented;
}

/**
 * Workaround for nestsjs, to be able to check it the provider exists
 * without catching error everytime
 */
@Injectable()
export class CheckProviderService {
    private readonly cache: Map<string, boolean> = new Map();

    constructor(private readonly module: ModuleRef) {}

    public exists(name: string): boolean {
        let result = this.cache.get(name);
        if (result === undefined) {
            try {
                result = !!this.module.get(name);
            } catch (UnknownElementException) {
                result = false;
            }
            this.cache.set(name, result);
        }

        return result;
    }
}
