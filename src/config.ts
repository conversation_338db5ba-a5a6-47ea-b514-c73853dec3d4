// tslint:disable:max-line-length
import { HttpGatewayConfig } from "@skywind-group/sw-integration-core";
import { logging } from "@skywind-group/sw-utils";
import { CronExpression } from "@nestjs/schedule";

function logQueries(sql: string, timing?: number) {
    logging.logger("sequelize").info([sql, timing]);
}

const http: HttpGatewayConfig = {
    operatorUrl: process.env.OPERATOR_HTTP_URL || "https://gservices.b365uat.com:8110/API",
    defaultOptions: {
        timeout: +process.env.OPERATOR_HTTP_TIMEOUT || 5000,
        proxy: process.env.OPERATOR_HTTP_PROXY
    },
    keepAlive: {
        maxFreeSockets: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 100,
        socketActiveTTL: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 60000,
        freeSocketKeepAliveTimeout: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 30000
    },
    ssl: {
        ca: process.env.OPERATOR_SSL_CA,
        key: process.env.OPERATOR_SSL_KEY,
        cert: process.env.OPERATOR_SSL_CERT,
        password: process.env.OPERATOR_SSL_PASSWORD
    }
};

const dataTransfer = {
    merchantType: process.env.MERCHANT_TYPE || "b365",
    merchantCode: process.env.MERCHANT_CODE || "b365",
    defaultPlayerIp: process.env.DEFAULT_PLAYER_IP || "127.0.0.1",
    defaultJurisdiction: process.env.DEFAULT_JURISDICTION || "UK",
    roundImageHistoryTTL: +process.env.ROUND_IMAGE_HISTORY_TTL || 3600 // 1 hour (in seconds)
};

const sw = {
    swOperatorUrl: process.env.OPERATOR_API_BASE_URL || "http://localhost:3007",
    swInternalMapiUrl: process.env.INTERNAL_MAPI_URL || "http://localhost:4004",
    swTickerUrl: process.env.TICKER_API_URL || "http://localhost:5001"
};

const db = {
    database: process.env.PGDATABASE || "management",
    user: process.env.PGUSER,
    password: process.env.PGPASSWORD,
    host: process.env.PGHOST || "db",
    port: +process.env.PGPORT || 5432,
    ssl: {
        isEnabled: process.env.PG_SECURE_CONNECTION === "true",
        ca: process.env.PG_CA_CERT || "./ca.pem"
    },
    maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
    maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
    schema: process.env.PGSCHEMA || "public",
    syncOnStart: process.env.SYNC_ON_START === "true",
    logEnabled: process.env.PG_MASTER_LOGGING === "true",
    cache: {
        isEnabled: process.env.PG_MASTER_CACHE === "true",
        ttl: +process.env.PG_MASTER_CACHE_TTL || 30000 // in ms
    },
    connectionTimeoutInMs: +process.env.PG_MASTER_CONNECTION_TIMEOUT_MS || 10000
};

const criticalFiles = {
    regulation: process.env.CRITICAL_FILES_REGULATION || "italian",
    cronPattern: process.env.CRITICAL_FILES_CRON_PATTERN || CronExpression.EVERY_DAY_AT_8AM,
    cache: {
        stdTTL: +process.env.CRITICAL_FILES_MERCHANT_INFO_CACHE_TTL || 3600, // 1 hour
        checkperiod: +process.env.CRITICAL_FILES_MERCHANT_INFO_CACHE_CHECKPERIOD || 0,
        useClones: process.env.CRITICAL_FILES_MERCHANT_INFO_CACHE_USE_CLONES === "true"
    }
};

const config = {
    environment: process.env.NODE_ENV || "development",

    isProduction: (): boolean => {
        return config.environment === "production";
    },

    // region: process.env.REGION_TYPE || "default",

    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 3000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 3001,
        operatorPort: +process.env.SERVER_OPERATOR_PORT || 3002,
        mockPort: +process.env.SERVER_MOCK_PORT || 3003
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4054,
        api: {
            isEnabled: process.env.INTERNAL_API === "true"
        }
    },

    operator: {
        name: "b365",
        secretKey: process.env.OPERATOR_SECRET_KEY || "", // TODO: fill in if needed
        licenseId: +process.env.OPERATOR_LICENSE_ID || 134, // TODO: fill in if needed
        cryptoAlgorythm: process.env.OPERATOR_CRYPTO_ALG || "sha256",
        retryPolicy: {
            sleepInterval: +(process.env.RETRIES_SLEEP_TIMEOUT || 10000), // in ms (default 10 sec)
            attempts: +process.env.RETRIES_ATTEMPTS || 3 // in ms (default 3 times)
        },
        regulation: process.env.REGULATION || "UK"
    },

    queryLogging:
        process.env.POSTGRES_QUERY_LOGGING === "true"
            ? logQueries
            : () => {
                  /* empty */
              },
    currencyUnitMultiplier: +process.env.CURRENCY_UNIT_MULTIPLIER || 100,
    messageIdNumberLength: +process.env.MESSAGE_ID_NUMBER_LENGTH || 9,
    operatorApiVersion: process.env.OPERATOR_API_VERSION || "3.0",
    italyOperatorApiVersion: process.env.ITALY_OPERATOR_API_VERSION || "1.0",
    securedKeys: ["password", "username", "PrivateToken", "privateToken"],

    http,
    db,
    criticalFiles,
    ...dataTransfer,
    ...sw
};

export default config;
