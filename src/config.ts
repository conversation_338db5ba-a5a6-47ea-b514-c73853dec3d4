// tslint:disable:max-line-length
const config = {
    logging: {
        logLevel: process.env.LOG_LEVEL || "info",
        loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || (process.env.GRAYLOG_HOST && "graylog") || "console") as any,
        rootLogger: process.env.LOGGING_ROOT_LOGGER || "sw-integration-api",
        defaultSecureKeys: [
            "password", "newPassword", "key", "token", "accessToken", "secretKey", "merch_pwd",
            "cust_session_id", "sharedKey"
        ]
    },

    defaultHttpProxy: process.env.DEFAULT_HTTP_PROXY
};

export default config;
