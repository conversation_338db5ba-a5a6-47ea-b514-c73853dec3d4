export enum Names {
    ServerInfoProvider = "ServerInfoProvider",
    KeepAliveConfig = "SWIntegration_KeepAliveConfig",
    OperatorUrl = "SWIntegration_OperatorUrl",
    HttpGatewayConfig = "SWIntegration_HHttpGatewayConfig",
    XmlService = "SWIntegration_XmlService",
    PaymentSupportService = "SWIntegration_PaymentSupportService",
    FreeBetPaymentSupport = "SWIntegration_FreeBetPaymentSupport",
    JackpotPaymentSupportService = "SWIntegration_JackpotPaymentSupportService",
    RefundBetSupportService = "SWIntegration_RefundBetSupportService",
    TransferSupportService = "SWIntegration_TransferSupportService",
    BonusPaymentSupportService = "SWIntegration_BonusPaymentSupportService",
    BrokenGameSupportService = "SWIntegration_BrokenGameSupportService",
    RegulationSupportService = "SWIntegration_RegulationSupportService",
    FreeBetInfoService = "SWIntegration_FreeBetInfoService",
    OfflineBonusPaymentService = "SWIntegration_OfflineBonusPaymentService",

    HttpMethodHandlerPrefix = "SWIntegration_HttpHandler_",

    CreateGameUrlSupport = "SWIntegration_CreateGameUrlSupport",
    CreateGameTokenSupport = "SWIntegration_CreateGameTokenSupport",
    KeepAliveSupport = "SWIntegration_KeepAliveSupport",
    LoginTerminalSupport = "SWIntegration_LoginTerminalSupport",
    LogoutGameSupport = "SWIntegration_LogoutGameSupport"
}
