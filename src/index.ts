import { logging, measures } from "@skywind-group/sw-utils";
import config from "./config";

measures.measureProvider.baseInstrument();

logging.setUpOutput({ type: config.logging.loggingOutput, logLevel: config.logging.logLevel });
logging.setRootLogger(config.logging.rootLogger);

export * from "./interfaces/http";
export * from "./interfaces/xmlHttp";
export * from "./interfaces/types";
export * from "./interfaces/payment.service";
export * from "./interfaces/startGame.service";
export * from "./payment/payment.module";
export * from "./startgame/startGame.module";
export * from "./internal/internal.module";
export * from "./internal/serverInfo.module";
export * from "./names";
export * from "./utils/logging.adapter";
export * from "./utils/logging.interceptor";
export * from "./utils/clientIp.decorator";
export * from "./bootstrap/bootstrap";
export * from "./bootstrap/bootstrap.config";
export * from "./errors";
export * as mock from "./mock";
export * from "./constants";
