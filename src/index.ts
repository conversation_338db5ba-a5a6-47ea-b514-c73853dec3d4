import { Server } from './server';
import { logger } from './utils/logger';
import { config } from './config';

async function main(): Promise<void> {
  try {
    logger.info('Starting ProNet Gaming Integration Service', {
      nodeEnv: config.server.nodeEnv,
      port: config.server.port,
      vendorCode: config.pces.vendorCode,
    });

    const server = new Server();
    server.start();

    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      process.exit(0);
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      process.exit(0);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection', { reason, promise });
    });

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server', { error: (error as Error).message });
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
  });
}
