import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import bodyParser from 'body-parser';
import { config } from './config';
import { logger } from './utils/logger';
import { IntegrationService } from './services/integration';
import { WalletError } from './services/wallet';

export class Server {
  private app: express.Application;
  private integrationService: IntegrationService;

  constructor() {
    this.app = express();
    this.integrationService = new IntegrationService();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(bodyParser.json());
    this.app.use(bodyParser.urlencoded({ extended: true }));

    this.app.use((req, res, next) => {
      logger.info('Incoming request', {
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      next();
    });
  }

  private setupRoutes(): void {
    this.app.get('/health', (req, res) => {
      res.json({ status: 'OK', timestamp: new Date().toISOString() });
    });

    this.app.post('/api/auth', async (req, res) => {
      try {
        const { customer, token } = req.body;
        const result = await this.integrationService.authenticatePlayer(customer, token);
        res.json(result);
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/bet-settle', async (req, res) => {
      try {
        const { customer, token, gameId, betAmount, winAmount, currency } = req.body;
        const result = await this.integrationService.placeBetAndSettle(
          customer, token, gameId, betAmount, winAmount, currency
        );
        res.json(result);
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/bet', async (req, res) => {
      try {
        const { customer, token, gameId, amount, currency } = req.body;
        const result = await this.integrationService.placeBet(customer, token, gameId, amount, currency);
        res.json(result);
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/settle', async (req, res) => {
      try {
        const { customer, token, gameId, amount, currency, betId } = req.body;
        const result = await this.integrationService.settleBet(customer, token, gameId, amount, currency, betId);
        res.json(result);
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/rollback', async (req, res) => {
      try {
        const { customer, token, gameId, trxId } = req.body;
        const result = await this.integrationService.rollbackTransaction(customer, token, gameId, trxId);
        res.json(result);
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/promo', async (req, res) => {
      try {
        const { customer, token, gameId, amount, currency, promoType, promoRef } = req.body;
        const result = await this.integrationService.processPromotion(
          customer, token, gameId, amount, currency, promoType, promoRef
        );
        res.json(result);
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/game-url', (req, res) => {
      try {
        const params = req.body;
        const gameUrl = this.integrationService.buildGameUrl(params);
        res.json({ gameUrl });
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/demo-game-url', (req, res) => {
      try {
        const params = req.body;
        const demoUrl = this.integrationService.buildDemoGameUrl(params);
        res.json({ demoUrl });
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.post('/api/freespins', async (req, res) => {
      try {
        const request = req.body;
        await this.integrationService.giveFreespins(request);
        res.json({ success: true });
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.delete('/api/freespins/:freespinRef', async (req, res) => {
      try {
        const { freespinRef } = req.params;
        await this.integrationService.cancelFreespins(freespinRef);
        res.json({ success: true });
      } catch (error) {
        this.handleError(error, res);
      }
    });

    this.app.get('/api/round-details', async (req, res) => {
      try {
        const request = req.query as unknown as any;
        const result = await this.integrationService.getRoundDetails(request);
        res.json(result);
      } catch (error) {
        this.handleError(error, res);
      }
    });
  }

  private setupErrorHandling(): void {
    this.app.use((req, res) => {
      res.status(404).json({ error: 'Not Found' });
    });

    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error', { error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Internal Server Error' });
    });
  }

  private handleError(error: unknown, res: express.Response): void {
    if (error instanceof WalletError) {
      res.status(400).json({
        error: 'Wallet Error',
        code: error.code,
        status: error.status,
        message: error.message,
      });
    } else if (error instanceof Error) {
      res.status(400).json({
        error: 'Bad Request',
        message: error.message,
      });
    } else {
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An unknown error occurred',
      });
    }
  }

  start(): void {
    this.app.listen(config.server.port, () => {
      logger.info(`Server started on port ${config.server.port}`);
    });
  }

  getApp(): express.Application {
    return this.app;
  }
}
