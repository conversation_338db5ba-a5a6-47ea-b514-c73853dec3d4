import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import config from "@config";
import { DbLogger } from "@utils/db-logger";
import { RoundEntity } from "@wallet/round/round.entity";

@Module({
    imports: [
        TypeOrmModule.forRoot({
            type: "postgres",
            host: config.db.host,
            port: config.db.port,
            username: config.db.user,
            password: config.db.password,
            database: config.db.database,
            entities: [RoundEntity],
            synchronize: config.db.syncOnStart,
            logging: true,
            logger: config.db.logEnabled && new DbLogger(),
            cache: config.db.cache.isEnabled && { duration: config.db.cache.ttl },
            connectTimeoutMS: config.db.connectionTimeoutInMs
        })
    ]
})
export class DbModule {}
