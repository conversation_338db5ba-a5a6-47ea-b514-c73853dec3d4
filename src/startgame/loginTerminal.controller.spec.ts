import { Test, TestingModule } from "@nestjs/testing";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { StartGameFacade } from "./startGame.facade";
import { LoginTerminalController } from "./loginTerminal.controller";

@suite()
class LoginTerminalControllerSpec {
    private controller: LoginTerminalController;
    private facadeStub: SinonStubbedInstance<StartGameFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(StartGameFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [LoginTerminalController],
            providers: [
                {
                    provide: StartGameFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<LoginTerminalController>(LoginTerminalController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testCreateGameURL() {
        const response: any = {
            type: "login_terminal_response"
        };
        const req: any = {
            type: "login_terminal"
        };
        this.facadeStub.loginTerminalPlayer.resolves(response);
        const result = await this.controller.loginTerminal(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.loginTerminalPlayer.lastCall.args).deep.eq([req]);
    }
}
