import { DynamicModule, Module, Provider } from "@nestjs/common";
import { Names } from "../names";

import { Imports, ServiceProvider } from "../interfaces/types";
import { getStartGameHandlers, StartGameModuleConfig, StartGameService } from "../interfaces/startGame.service";
import { StartGameController } from "./startGame.controller";
import { KeepAliveController } from "./keepalive.controller";
import { LoginTerminalController } from "./loginTerminal.controller";
import { StartGameFacade } from "./startGame.facade";
import { HttpGateway, XmlHttpGateway } from "..";
import { getHandlerName } from "../utils/handler";
import { safeInject } from "../utils/saveInject";
import { ErrorFilter } from "../utils/error.filter";
import { CheckProviderService } from "../utils/checkExistsSupport";
import { HttpStartGameServiceAdapter } from "./httpStartGame.adapter";
import { LogoutController } from "./logout.controller";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";

@Module({
    controllers: [StartGameController, KeepAliveController, LoginTerminalController, LogoutController]
})
export class StartGameModule {
    public static register(
        startGameServiceProvider: ServiceProvider<StartGameService>,
        imports?: Imports
    ): DynamicModule {
        return {
            module: StartGameModule,
            imports,
            providers: [
                ...safeInject(
                    startGameServiceProvider,
                    Names.CreateGameTokenSupport,
                    Names.CreateGameUrlSupport,
                    Names.KeepAliveSupport,
                    Names.LoginTerminalSupport,
                    Names.LogoutGameSupport
                ),
                ErrorFilter,
                CheckProviderService,
                StartGameFacade
            ]
        };
    }

    public static registerWithConfig(config: StartGameModuleConfig, imports?: Imports): DynamicModule {
        const providers: Provider[] = [];
        const exports = [];
        if (config.createGameURL) {
            providers.push(...safeInject(config.createGameURL, Names.CreateGameUrlSupport));
        } else if (config.http) {
            providers.push({ provide: Names.CreateGameUrlSupport, useClass: HttpStartGameServiceAdapter } as Provider);
        }

        if (config.createGameToken) {
            providers.push(...safeInject(config.createGameToken, Names.CreateGameTokenSupport));
        } else if (config.http) {
            providers.push({
                provide: Names.CreateGameTokenSupport,
                useClass: HttpStartGameServiceAdapter
            } as Provider);
        }

        if (config.keepAlive) {
            providers.push(...safeInject(config.keepAlive, Names.KeepAliveSupport));
        } else if (config.http) {
            providers.push({ provide: Names.KeepAliveSupport, useClass: HttpStartGameServiceAdapter } as Provider);
        }

        if (config.loginTerminal) {
            providers.push(...safeInject(config.loginTerminal, Names.LoginTerminalSupport));
        } else if (config.http) {
            providers.push({ provide: Names.LoginTerminalSupport, useClass: HttpStartGameServiceAdapter } as Provider);
        }

        if (config.logoutGame) {
            providers.push(...safeInject(config.logoutGame, Names.LogoutGameSupport));
        } else if (config.http) {
            providers.push({ provide: Names.LogoutGameSupport, useClass: HttpStartGameServiceAdapter } as Provider);
        }

        if (config.http) {
            providers.push(HttpGateway);
            exports.push(HttpGateway);
            providers.push(XmlHttpGateway);
            exports.push(XmlHttpGateway);
            providers.push(
                {
                    provide: Names.HttpGatewayConfig,
                    useValue: config.http.gatewayConfig
                },
                {
                    provide: Names.XmlService,
                    useValue: new XmlService()
                }
            );
            if (config.http.handlers) {
                for (const handler of Object.keys(config.http.handlers)) {
                    providers.push({
                        provide: getHandlerName(handler),
                        ...config.http.handlers[handler]
                    });
                }
            } else {
                for (const provider of getStartGameHandlers()) {
                    const key = Reflect.getMetadata("http_handler", provider);
                    providers.push({
                        provide: getHandlerName(key),
                        useClass: provider
                    });
                }
            }
        }

        return {
            module: StartGameModule,
            imports,
            providers: [...providers, ErrorFilter, CheckProviderService, StartGameFacade],
            exports
        };
    }
}
