import { Test, TestingModule } from "@nestjs/testing";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { MerchantGameURLInfo } from "@skywind-group/sw-wallet-adapter-core";
import { StartGameFacade } from "./startGame.facade";
import { StartGameController } from "./startGame.controller";

@suite()
class StartGameControllerSpec {
    private controller: StartGameController;
    private facadeStub: SinonStubbedInstance<StartGameFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(StartGameFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [StartGameController],
            providers: [
                {
                    provide: StartGameFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<StartGameController>(StartGameController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testCreateGameURL() {
        const response: MerchantGameURLInfo = {
            tokenData: undefined,
            urlParams: { param1: 1 }
        };
        const req: any = {
            type: "create_game_url"
        };
        this.facadeStub.createGameUrl.resolves(response);
        const result = await this.controller.createGameUrl("sw_al", req as any);
        expect(result).eq(response);
        expect(this.facadeStub.createGameUrl.lastCall.args).deep.eq([{ gameCode: "sw_al", ...req }]);
    }

    @test
    public async testCreateGameTokenData() {
        const response: any = {
            type: "creat_game_token_response"
        };
        const req: any = {
            type: "creat_game_token"
        };
        this.facadeStub.createGameTokenData.resolves(response);
        const result = await this.controller.createGameTokenData("sw_al", req as any);
        expect(result).eq(response);
        expect(this.facadeStub.createGameTokenData.lastCall.args).deep.eq([{ gameCode: "sw_al", ...req }]);
    }
}
