import { Inject, Injectable, Optional } from "@nestjs/common";
import { Names } from "../names";
import {
    GameLogoutRequest,
    GameLogoutResponse,
    LoginTerminalResponse,
    MerchantGameURLInfo,
    MerchantStartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import {
    CreateGameTokenRequest,
    CreateGameTokenSupport,
    CreateGameUrlRequest,
    CreateGameUrlSupport,
    KeepAliveRequest,
    KeepAliveSupport,
    LoginTerminalPlayerRequest,
    LoginTerminalPlayerSupport,
    LogoutGameSupport
} from "../interfaces/startGame.service";
import { MerchantGameTokenInfo } from "@skywind-group/sw-wallet-adapter-core/src/skywind/definitions/startGame";
import { BaseFacade } from "../utils/base.facade";

@Injectable()
export class StartGameFacade extends BaseFacade {
    constructor(
        @Inject(Names.CreateGameUrlSupport)
        private readonly createGameUrlSupport: CreateGameUrlSupport,
        @Inject(Names.CreateGameTokenSupport)
        private readonly createGameTokenSupport: CreateGameTokenSupport,
        @Optional()
        @Inject(Names.KeepAliveSupport)
        private readonly keepAliveSupport?: KeepAliveSupport,
        @Optional()
        @Inject(Names.LoginTerminalSupport)
        private readonly loginTerminalPlayerSupport?: LoginTerminalPlayerSupport,
        @Optional()
        @Inject(Names.LogoutGameSupport)
        private readonly logoutGameSupport?: LogoutGameSupport
    ) {
        super();
    }

    public async createGameUrl(req: CreateGameUrlRequest): Promise<MerchantGameURLInfo> {
        return this.createGameUrlSupport.createGameUrl(req);
    }

    public async createGameTokenData(req: CreateGameTokenRequest): Promise<MerchantGameTokenInfo> {
        return this.createGameTokenSupport.createGameTokenData(req);
    }

    public async keepAlive(req: KeepAliveRequest): Promise<void> {
        return this.checkExists(this.keepAliveSupport, "keepAlive")(req);
    }

    public async loginTerminalPlayer(
        req: LoginTerminalPlayerRequest
    ): Promise<LoginTerminalResponse<MerchantStartGameTokenData>> {
        return this.checkExists(this.loginTerminalPlayerSupport, "loginTerminalPlayer")(req);
    }

    public async logoutGame(req: GameLogoutRequest): Promise<GameLogoutResponse> {
        return this.logoutGameSupport.logoutGame(req);
    }
}
