import { Body, Controller, Param, Post, UseFilters } from "@nestjs/common";
import { StartGameFacade } from "./startGame.facade";
import { CreateGameTokenRequest, CreateGameUrlRequest } from "../interfaces/startGame.service";
import { MerchantGameURLInfo } from "@skywind-group/sw-wallet-adapter-core";
import { MerchantGameTokenInfo } from "@skywind-group/sw-wallet-adapter-core/src/skywind/definitions/startGame";
import { ErrorFilter } from "../utils/error.filter";

@UseFilters(ErrorFilter)
@Controller("/games")
export class StartGameController {
    constructor(private readonly startGameFacade: StartGameFacade) {}

    @Post("/:gameCode/url")
    public async createGameUrl(
        @Param("gameCode") gameCode: string,
        @Body() req: Omit<CreateGameUrlRequest, "gameCode">
    ): Promise<MerchantGameURLInfo> {
        return this.startGameFacade.createGameUrl({ gameCode, ...req });
    }

    @Post("/:gameCode/token")
    public async createGameTokenData(
        @Param("gameCode") gameCode: string,
        @Body() req: Omit<CreateGameTokenRequest, "gameCode">
    ): Promise<MerchantGameTokenInfo> {
        return this.startGameFacade.createGameTokenData({ gameCode, ...req });
    }
}
