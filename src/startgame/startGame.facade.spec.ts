import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import { expect, use } from "chai";
import { HttpStartGameServiceAdapter } from "./httpStartGame.adapter";
import "chai-as-promised";
import { StartGameFacade } from "./startGame.facade";
import { OperationForbidden } from "../errors";

use(require("chai-as-promised"));

@suite()
class StartGameFacadeSpec {
    public async after() {
        sinon.restore();
    }

    @test
    public async createGameUrl() {
        const startGameStub = sinon.createStubInstance(HttpStartGameServiceAdapter);

        const facade = new StartGameFacade(startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        const res = { url: "http://localhost" };
        startGameStub.createGameUrl.withArgs(req as any).resolves(res as any);
        const result = await facade.createGameUrl(req as any);
        expect(result).eq(res);
        expect(startGameStub.createGameUrl.lastCall.args[0]).eq(req);
    }

    @test
    public async createGameTokenData() {
        const startGameStub = sinon.createStubInstance(HttpStartGameServiceAdapter);

        const facade = new StartGameFacade(startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        const res = { url: "http://localhost" };
        startGameStub.createGameTokenData.withArgs(req as any).resolves(res as any);
        const result = await facade.createGameTokenData(req as any);
        expect(result).eq(res);
        expect(startGameStub.createGameTokenData.lastCall.args[0]).eq(req);
    }

    @test
    public async keepAlive() {
        const startGameStub = sinon.createStubInstance(HttpStartGameServiceAdapter);

        const facade = new StartGameFacade(startGameStub, startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        const res = { url: "http://localhost" };
        startGameStub.keepAlive.withArgs(req as any).resolves(res as any);
        startGameStub.isImplemented.withArgs("keepAlive").returns(true);
        const result = await facade.keepAlive(req as any);
        expect(result).eq(res);
        expect(startGameStub.keepAlive.lastCall.args[0]).eq(req);
    }

    @test
    public async loginTerminalPlayer() {
        const startGameStub = sinon.createStubInstance(HttpStartGameServiceAdapter);

        const facade = new StartGameFacade(startGameStub, startGameStub, startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        const res = { url: "http://localhost" };
        startGameStub.loginTerminalPlayer.withArgs(req as any).resolves(res as any);
        startGameStub.isImplemented.withArgs("loginTerminalPlayer").returns(true);
        const result = await facade.loginTerminalPlayer(req as any);
        expect(result).eq(res);
        expect(startGameStub.loginTerminalPlayer.lastCall.args[0]).eq(req);
    }

    @test
    public async forbidden_1() {
        const startGameStub = sinon.createStubInstance(HttpStartGameServiceAdapter);

        const facade = new StartGameFacade(startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        startGameStub.loginTerminalPlayer.withArgs(req as any).resolves({} as any);
        await expect(facade.loginTerminalPlayer(req as any)).to.be.rejectedWith(OperationForbidden);
    }

    @test
    public async forbidden_2() {
        const startGameStub = sinon.createStubInstance(HttpStartGameServiceAdapter);

        const facade = new StartGameFacade(startGameStub, startGameStub);

        const req = { gameCode: "sw_al" };
        startGameStub.loginTerminalPlayer.withArgs(req as any).resolves({} as any);
        startGameStub.isImplemented.withArgs("loginTerminalPlayer").returns(false);
        await expect(facade.loginTerminalPlayer(req as any)).to.be.rejectedWith(OperationForbidden);
    }
}
