import { Test, TestingModule } from "@nestjs/testing";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { StartGameFacade } from "./startGame.facade";
import { KeepAliveController } from "./keepalive.controller";

@suite()
class StartControllerSpec {
    private controller: KeepAliveController;
    private facadeStub: SinonStubbedInstance<StartGameFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(StartGameFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [KeepAliveController],
            providers: [
                {
                    provide: StartGameFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<KeepAliveController>(KeepAliveController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testCreateGameURL() {
        const response: any = {
            type: "keep_alive_response"
        };
        const req: any = {
            type: "keep_alive"
        };
        this.facadeStub.keepAlive.resolves(response);
        const result = await this.controller.keepalive(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.keepAlive.lastCall.args).deep.eq([req]);
    }
}
