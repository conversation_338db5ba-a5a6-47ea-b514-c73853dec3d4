import { Body, Controller, Post, UseFilters } from "@nestjs/common";
import { StartGameFacade } from "./startGame.facade";
import { KeepAliveRequest } from "../interfaces/startGame.service";
import { ErrorFilter } from "../utils/error.filter";

@UseFilters(ErrorFilter)
@Controller("/games")
export class KeepAliveController {
    constructor(private readonly startGameFacade: StartGameFacade) {}

    @Post("/keep-alive")
    public async keepalive(@Body() req: KeepAliveRequest): Promise<void> {
        return this.startGameFacade.keepAlive(req);
    }
}
