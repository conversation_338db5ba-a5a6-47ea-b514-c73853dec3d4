import { Body, Controller, Post, UseFilters } from "@nestjs/common";
import { GameLogoutRequest, GameLogoutResponse } from "@skywind-group/sw-wallet-adapter-core";
import { StartGameFacade } from "./startGame.facade";
import { ErrorFilter } from "../utils/error.filter";

@UseFilters(ErrorFilter)
@Controller("/logoutGame")
export class LogoutController {
    constructor(private readonly startGameFacade: StartGameFacade) {}

    @Post("/")
    public async logoutGame(@Body() req: GameLogoutRequest): Promise<GameLogoutResponse> {
        return this.startGameFacade.logoutGame(req);
    }
}
