import {
    GameLogoutRequest,
    GameLogoutResponse,
    LoginTerminalResponse,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantStartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { Injectable } from "@nestjs/common";
import {
    CreateGameTokenRequest,
    CreateGameUrlRequest,
    KeepAliveRequest,
    LoginTerminalPlayerRequest,
    StartGameService
} from "../interfaces/startGame.service";
import { ModuleRef } from "@nestjs/core";
import { HttpBaseAdapter } from "../utils/httpBase.adaper";

@Injectable()
export class HttpStartGameServiceAdapter extends HttpBaseAdapter<StartGameService> implements StartGameService {
    constructor(moduleRef: ModuleRef) {
        super(moduleRef);
    }

    public async createGameTokenData(req: CreateGameTokenRequest): Promise<MerchantGameTokenInfo> {
        return this.httpGateway.request(req, this.getHandle("createGameTokenData"));
    }

    public async createGameUrl(req: CreateGameUrlRequest): Promise<MerchantGameURLInfo> {
        return this.httpGateway.request(req, this.getHandle("createGameUrl"));
    }

    public async keepAlive(req: KeepAliveRequest<MerchantGameTokenData>): Promise<void> {
        return this.httpGateway.request(req, this.getHandle("keepAlive"));
    }

    public async loginTerminalPlayer(
        req: LoginTerminalPlayerRequest
    ): Promise<LoginTerminalResponse<MerchantStartGameTokenData>> {
        return this.httpGateway.request(req, this.getHandle("loginTerminalPlayer"));
    }

    public async logoutGame(req: GameLogoutRequest): Promise<GameLogoutResponse> {
        return this.httpGateway.request(req, this.getHandle("logoutGame"));
    }
}
