import { Body, Controller, Post, UseFilters } from "@nestjs/common";
import { StartGameFacade } from "./startGame.facade";
import { LoginTerminalPlayerRequest } from "../interfaces/startGame.service";
import { ErrorFilter } from "../utils/error.filter";
import { LoginTerminalResponse, MerchantStartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";

@UseFilters(ErrorFilter)
@Controller("/games")
export class LoginTerminalController {
    constructor(private readonly startGameFacade: StartGameFacade) {}

    @Post("/login-terminal")
    public async loginTerminal(
        @Body() req: LoginTerminalPlayerRequest
    ): Promise<LoginTerminalResponse<MerchantStartGameTokenData>> {
        return this.startGameFacade.loginTerminalPlayer(req);
    }
}
