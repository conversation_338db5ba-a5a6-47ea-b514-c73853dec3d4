import { Test, TestingModule } from "@nestjs/testing";
import { LogoutController } from "./logout.controller";
import { expect } from "chai";
import { suite, test } from "mocha-typescript";
import { SinonStubbedInstance } from "sinon";
import { StartGameFacade } from "./startGame.facade";
import * as sinon from "sinon";

@suite()
class StartGameControllerSpec {
    private controller: LogoutController;
    private facadeStub: SinonStubbedInstance<StartGameFacade>;

    public async before() {
        this.facadeStub = sinon.createStubInstance(StartGameFacade);
        const module: TestingModule = await Test.createTestingModule({
            controllers: [LogoutController],
            providers: [
                {
                    provide: StartGameFacade,
                    useValue: this.facadeStub
                }
            ]
        }).compile();

        this.controller = module.get<LogoutController>(LogoutController);
    }

    public async after() {
        sinon.restore();
    }

    @test
    public testDefined() {
        expect(this.controller).is.not.undefined;
    }

    @test
    public async testLogoutGame() {
        const response: any = {
            type: "logout_game_response"
        };
        const req: any = {
            type: "create_game_url"
        };
        this.facadeStub.logoutGame.resolves(response);
        const result = await this.controller.logoutGame(req as any);
        expect(result).eq(response);
        expect(this.facadeStub.logoutGame.lastCall.args).deep.eq([req]);
    }
}
