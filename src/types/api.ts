export interface GameOpenParams {
  currency: string;
  customer: string;
  demo: boolean;
  gameId: string;
  lang: string;
  lobby: string;
  platform: 'd' | 'm';
  tableId?: string;
  token?: string;
  trader: string;
  country?: string;
}

export interface DemoGameOpenParams {
  currency: 'FUN';
  demo: true;
  gameId: string;
  lang: string;
  lobby: string;
  platform: 'd' | 'm';
  tableId?: string;
  trader: string;
}

export interface BaseRequest {
  customer: string;
  token: string;
  hash: string;
}

export interface AuthRequest extends BaseRequest {}

export interface DebitCreditRequest extends BaseRequest {
  gameId: string;
  amount: number;
  creditAmount: number;
  currency: string;
  betId: string;
  trxId: string;
  creditTrxId: string;
  tip?: boolean;
}

export interface DebitRequest extends BaseRequest {
  gameId: string;
  amount: number;
  currency: string;
  betId: string;
  trxId: string;
  tip?: boolean;
}

export interface CreditRequest extends BaseRequest {
  gameId: string;
  amount: number;
  currency: string;
  betId: string;
  trxId: string;
  freespin?: FreespinData;
}

export interface RollbackRequest extends BaseRequest {
  trxId: string;
  gameId: string;
}

export interface PromoRequest extends BaseRequest {
  gameId?: string;
  amount: number;
  currency: string;
  betId: string;
  trxId: string;
  promo: PromoData;
}

export interface FreespinData {
  freespinRef: string;
  requested?: boolean;
  remainingRounds?: number;
  totalWinnings?: number;
}

export interface PromoData {
  promoType: 'FSW' | 'JPW' | 'CB' | 'TW' | 'RW' | 'REW' | 'CDW' | 'RB';
  promoRef: string;
  freeSpinData?: FreespinData;
}

export interface BaseResponse {
  code: number;
  status: string;
  currency?: string;
  balance?: number;
  bonusBalance?: number;
  trxId?: number;
  creditTrxId?: number;
  traderId?: number;
}

export interface AuthResponse extends BaseResponse {
  traderId: number;
}

export interface FreespinApiRequest {
  customers: string[];
  games: string[];
  numberOfFreespins: number;
  freespinRef: string;
  wagerRequirement: number;
  validFrom: Date;
  validUntil: Date;
  maxWin?: number;
  costPerBet?: number;
  coinValueLevel?: number;
  lines?: number;
  coins?: number;
  denomination?: number;
  betPerLine?: number;
}

export interface RoundDetailsRequest {
  customer: string;
  roundId: string;
  gameId: string;
  lang: string;
}

export interface RoundDetailsResponse {
  code: number;
  status: 'SUCCESS' | 'UNKNOWN_ERROR' | 'NOT_FOUND';
  json?: JsonRoundDetails;
  image?: ImageRoundDetails;
  html?: HtmlRoundDetails;
}

export interface JsonRoundDetails {
  gameId: string;
  gameName: string;
  roundId: string;
  roundDate: string;
  betAmount: number;
  winAmount: number;
  currency: string;
}

export interface ImageRoundDetails {
  url: string;
  height?: number;
  width?: number;
}

export interface HtmlRoundDetails {
  content: string;
  height?: number;
  width?: number;
}

export type StatusCode = 
  | 0    // SUCCESS
  | -1   // UNKNOWN_ERROR
  | -2   // NOT_FOUND
  | 1    // UNKNOWN_ERROR
  | 2    // UNAUTHORIZED_REQUEST
  | 3    // NOT_INTEGRATED
  | 4    // TOKEN_CUSTOMER_MISMATCH
  | 5    // UNSUPPORTED_API_VERSION
  | 6    // INTERNAL_CACHE_ERROR
  | 7    // PROMOTION_TYPE_NOT_SUPPORTED
  | -20120 // BET_RECORD_NOT_FOUND
  | -20112 // BET_ALREADY_WON
  | -20101 // AUTHENTICATION_FAILED
  | -20130 // GAME_NOT_FOUND
  | -20201 // BET_LIMIT_REACHED
  | -20202 // LOSS_LIMIT_REACHED
  | -20203 // SESSION_LIMIT_REACHED
  | -20204 // PROFIT_LIMIT_REACHED
  | -20301 // INVALID_CASINO_VENDOR
  | -20302 // ALL_BET_ARE_OFF
  | -20303 // INVALID_GAME
  | -20304 // CUSTOMER_NOT_FOUND
  | -20305 // INVALID_CURRENCY
  | -20306 // INSUFFICIENT_FUNDS
  | -20307 // PLAYER_SUSPENDED
  | -20308 // REQUIRED_FIELD_MISSING
  | -20309 // DATA_OUT_OF_RANGE
  | -20310 // BET_ALREADY_SETTLED
  | -20311 // TOKEN_TIMEOUT
  | -20312 // TOKEN_INVALID
  | -20313 // TRANSACTION_NOT_FOUND
  | -20314 // NEGATIVE_DEPOSIT
  | -20315 // NEGATIVE_WITHDRAWAL
  | -20316; // TOKEN_NOT_FOUND
