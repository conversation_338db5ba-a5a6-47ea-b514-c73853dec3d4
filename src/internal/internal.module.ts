import { DynamicModule, Module } from "@nestjs/common";
import { ServerInfoProvider } from "./serverInfo.provider";
import { MetricsController } from "./metrics.controller";
import { ServerInfoModule, ServerInfoModuleConfig } from "./serverInfo.module";

export interface InternalModuleConfig extends ServerInfoModuleConfig {
    port?: number;
}

/**
 * Internal module to handle metrics/versions/health endpoints
 */
@Module({
    controllers: [MetricsController]
})
export class InternalModule {
    public static register(config: InternalModuleConfig): DynamicModule {
        return {
            module: InternalModule,
            imports: [ServerInfoModule.register(config)]
        };
    }
}
