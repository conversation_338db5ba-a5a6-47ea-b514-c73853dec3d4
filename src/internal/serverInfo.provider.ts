import { FactoryProvider } from "@nestjs/common";
import { getEnvironmentInfo, lazy } from "@skywind-group/sw-utils";
import { Names } from "../names";
import * as fs from "fs";

export interface ServerInfo {
    version: string;
    serverName: string;
    environmentInfo: any;
}

export const ServerInfoProvider = (serviceName: string, versionFile: string): FactoryProvider<ServerInfo> => {
    const version = lazy(() => {
        try {
            return fs.readFileSync(versionFile, "utf8");
        } catch (err) {
            console.log(err);
            return undefined;
        }
    });

    return {
        provide: Names.ServerInfoProvider,
        useFactory: () => {
            return {
                version: version.get(),
                environmentInfo: getEnvironmentInfo(),
                serverName: serviceName
            };
        }
    };
};
