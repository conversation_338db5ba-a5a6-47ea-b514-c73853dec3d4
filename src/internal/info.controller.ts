import { Controller, Get, Inject } from "@nestjs/common";
import { Names } from "../names";
import { ServerInfo } from "./serverInfo.provider";

@Controller("/v1")
export class InfoController {
    constructor(@Inject(Names.ServerInfoProvider) private readonly serverInfo: ServerInfo) {}

    @Get("/version")
    public getVersion(): string {
        return this.serverInfo.version;
    }

    @Get("/health")
    public getHealth(): any {
        return {
            serverName: this.serverInfo.serverName,
            ...this.serverInfo.environmentInfo
        };
    }
}
