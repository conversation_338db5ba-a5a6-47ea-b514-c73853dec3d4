import dotenv from 'dotenv';

dotenv.config({ path: '.env.test' });

process.env.NODE_ENV = 'test';
process.env.BASE_ENGINE_URL = 'http://test.example.com/';
process.env.VENDOR_CODE = 'test_vendor';
process.env.GENERIC_ID = 'test_generic_id';
process.env.GENERIC_SECRET_KEY = 'test_secret_key';
process.env.GAME_URL = 'http://test-game.example.com/game';
process.env.DEMO_GAME_URL = 'http://test-game.example.com/demo';
process.env.LOG_LEVEL = 'error';

jest.setTimeout(30000);
