import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorFreeSpinTransactionRequest,
    OperatorFreeSpinTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/common";
import { Injectable } from "@nestjs/common";
import { IntegrationPaymentRequest } from "@entities/integration.entities";

@Injectable()
export class FreeBetWinHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const winRequest: OperatorFreeSpinTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Return,
            Amount: req.request.totalWin,
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.WIN,
            GameRoundID: req.operatorRoundId,
            FreeSpinToken: req.gameTokenData.freeSpinToken
        };

        return this.buildHttpRequest({
            path: "FreeSpin/Transaction",
            method: "post",
            payload: winRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response, req: IntegrationPaymentRequest): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorFreeSpinTransactionResponse>(response);
        const balance: Balance = {
            main: parsedResponse.Balance.TotalAmount,
            freeBets: { amount: parsedResponse.NumberOfSpinsRemaining }
        };
        balance.previousValue = sumMajorUnits(balance.main, -req.request.totalWin);
        return balance;
    }
}
