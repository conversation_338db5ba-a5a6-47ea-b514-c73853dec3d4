import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationPaymentRequest } from "@entities/integration.entities";
import {
    OperatorFreeSpinTransactionRequest,
    OperatorFreeSpinTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";

export class FreeBetHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const betRequest: OperatorFreeSpinTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Stake,
            Amount: req.request.bet,
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.BET,
            GameRoundID: req.operatorRoundId,
            FreeSpinToken: req.gameTokenData.freeSpinToken
        };

        return this.buildHttpRequest({
            path: "FreeSpin/Transaction",
            method: "post",
            payload: betRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorFreeSpinTransactionResponse>(response);
        return {
            main: parsedResponse.Balance.TotalAmount,
            previousValue: parsedResponse.Balance.TotalAmount,
            freeBets: { amount: parsedResponse.NumberOfSpinsRemaining }
        };
    }
}
