import "module-alias/register";
import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import * as chaiAsPromised from "chai-as-promised";
import * as request from "superagent";
import { mapping, testing } from "@skywind-group/sw-utils";
import { expect, should, use } from "chai";
import { Test } from "@nestjs/testing";
import { HttpGateway, HttpGatewayConfig, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { Names } from "@names";
import {
    IntegrationGameTokenData,
    IntegrationGetFreeBetInfoRequest,
    IntegrationPaymentRequest
} from "@entities/integration.entities";
import { InsufficientFreebet, InvalidFreebet, MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import { FreeBetService } from "@wallet/freebet/free.bet.service";
import { GetFreeSpinsInfoHandler } from "@wallet/freebet/free.bet.info.http.handler";
import {
    OperatorFreeSpinTransactionResponse,
    OperatorGetFreeSpinsRequest,
    OperatorGetFreeSpinsResponse,
    OperatorStartGameRoundResponse,
    OperatorTransactionResponse,
    OperatorTransactionType
} from "@entities/operator.entities";
import { checkRequests } from "@utils/test/testing";
import { FreeBetWinHttpHandler } from "@wallet/freebet/free.bet.win.http.handler";
import { RoundService } from "@wallet/round/round.service";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@payment/round.http.handler";
import { BalanceHttpHandler } from "@payment/balance.http.handler";
import { FreeBetHttpHandler } from "@wallet/freebet/free.bet.http.handler";
import _ = require("lodash");
import status200 = testing.status200;
import DBModel = mapping.DBModel;
import { JackpotWinHttpHandler } from "@wallet/jackpot/jackpot.win.http.handler";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { BetHttpHandler } from "@payment/bet.http.handler";
import { WinHttpHandler } from "@payment/win.http.handler";
import { RefundHttpHandler } from "@wallet/refund/refund.http.handler";
import { PaymentService } from "@payment/payment.service";
import { ValidationError } from "@errors/sw.errors";
import { BonusHttpHandler } from "@payment/bonus.http.handler";
import { RoundDto } from "@wallet/round/round.dto";
import { returnFakeRoundEntity, roundRepository } from "@utils/test/stubs";
import { AwardPromoHttpHandler } from "@payment/awardPromo.http.handler";

should();
use(chaiAsPromised);

const httpGatewayConfig = {
    operatorUrl: "http://operator/API",
    keepAlive: {
        socketActiveTTL: 60000,
        maxFreeSockets: 100,
        freeSocketKeepAliveTimeout: 12000
    }
} as HttpGatewayConfig;

const gameTokenData = {
    merchantType: "b365",
    merchantCode: "b365__UK",
    sessionId: "session_1232434",
    country: "GB",
    currency: "EUR",
    playerCode: "b365_player",
    language: "en",
    brandId: 1111,
    gameCode: "GAME001",
    privateToken: "private_token",
    channelId: 222,
    jurisdiction: "UK",
    isPromoInternal: false,
    freeSpinToken: "fs_token"
} as IntegrationGameTokenData;

const merchantInfo = {
    brandId: 1111,
    type: "b365",
    code: "b365__UK",
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
} as MerchantInfo;

const getFreeBetInfoRequest: IntegrationGetFreeBetInfoRequest = {
    gameToken: gameTokenData,
    merchantInfo,
    freeBetRequest: {
        gameToken: "token",
        coinMultiplier: 10,
        stakeAll: [1, 2, 3]
    }
};

const freeBetRequest: IntegrationPaymentRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: "",
        eventId: 0
    }
};

@suite()
class FreeBetServiceSpec {
    private requestMock: testing.RequestMock = testing.requestMock(request);
    private findRoundStub: sinon.SinonStub;
    private createRoundStub: sinon.SinonStub;
    private freeBetService: FreeBetService;

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                HttpGateway,
                {
                    provide: CoreNames.HttpGatewayConfig,
                    useValue: httpGatewayConfig
                },
                {
                    provide: Names.RoundRepository,
                    useValue: roundRepository
                },
                GetFreeSpinsInfoHandler,
                FreeBetService,
                RoundService,
                AwardPromoHttpHandler,
                StartRoundHttpHandler,
                EndRoundHttpHandler,
                FreeBetHttpHandler,
                FreeBetWinHttpHandler,
                BalanceHttpHandler,
                JackpotWinHttpHandler,
                JackpotStatisticsMapper,
                BetHttpHandler,
                WinHttpHandler,
                RefundHttpHandler,
                PaymentService,
                BonusHttpHandler
            ]
        }).compile();
        this.freeBetService = moduleRef.get<FreeBetService>(FreeBetService);
        this.createRoundStub = sinon.stub(roundRepository, "insertOne");
        this.findRoundStub = sinon.stub(roundRepository, "findOne");
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    @test()
    public async missingFreeSpinToken() {
        const request = _.cloneDeep(getFreeBetInfoRequest);
        delete request.gameToken.freeSpinToken;
        await expect(this.freeBetService.getFreeBetInfo(request)).to.be.rejectedWith(InsufficientFreebet);
    }

    @test()
    public async wrongTotalBetMultiplier() {
        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 1.5,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 3,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );
        await expect(this.freeBetService.getFreeBetInfo(getFreeBetInfoRequest)).to.be.rejectedWith(InvalidFreebet);
    }

    @test()
    public async wrongBet() {
        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 5,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 3,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );
        await expect(this.freeBetService.getFreeBetInfo(getFreeBetInfoRequest)).to.be.rejectedWith(InvalidFreebet);
    }

    @test()
    public async noFreeBetsLeft() {
        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 1,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 0,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );
        const result = await this.freeBetService.getFreeBetInfo(getFreeBetInfoRequest);
        expect(result).deep.equal({
            amount: 0,
            coin: 1
        });
    }

    @test()
    public async getInfoHappyPath() {
        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 2,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 3,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );
        const result = await this.freeBetService.getFreeBetInfo(getFreeBetInfoRequest);

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const getFreeSpinBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            FreeSpinToken: "fs_token"
        } as OperatorGetFreeSpinsRequest;
        checkRequests(this.requestMock, [
            {
                body: {},
                headers: expectedRequestHeader,
                method: "GET",
                query: getFreeSpinBody,
                url: "http://operator/API/UK/FreeSpin",
                keysToVoid: {
                    query: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(result).deep.equal({ amount: 3, coin: 2 });
    }

    @test()
    public async doFreeBet() {
        this.findRoundStub.returns(Promise.resolve(returnFakeRoundEntity({ operatorRoundId: "game_round" })));
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/FreeSpin/Transaction",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                ErrorDetails: [],
                NumberOfSpinsRemaining: 1
            } as OperatorFreeSpinTransactionResponse)
        );

        const betRequest = _.cloneDeep(freeBetRequest);
        betRequest.request.freeBetCoin = 1;
        betRequest.request.bet = 13.5;
        const result = await this.freeBetService.commitFreeBetPayment(betRequest);
        const request = this.requestMock.args[1];
        expect(request.body).deep.include({
            Amount: 13.5,
            FreeSpinToken: "fs_token",
            GameRoundID: "game_round",
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Stake
        });
        expect(result).deep.equal({
            freeBets: {
                amount: 1
            },
            main: 10.5,
            previousValue: 10.5
        });
    }

    @test()
    public async freeBetWin() {
        this.findRoundStub.returns(Promise.resolve(returnFakeRoundEntity({ operatorRoundId: "game_round" })));
        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/FreeSpin/Transaction",
            status200({
                Balance: { TotalAmount: 100509 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                ErrorDetails: [],
                NumberOfSpinsRemaining: 1
            } as OperatorFreeSpinTransactionResponse)
        );

        const winRequest = _.cloneDeep(freeBetRequest);
        winRequest.request.win = 1.5;
        winRequest.request.totalWin = 100500;
        winRequest.request.roundEnded = true;

        const result = await this.freeBetService.commitFreeBetWinPayment(winRequest);
        const request = this.requestMock.args[0];
        expect(request.body).deep.include({
            Amount: 100500,
            FreeSpinToken: "fs_token",
            GameRoundID: "game_round",
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return
        });
        expect(result).deep.equal({
            freeBets: {
                amount: 1
            },
            main: 100509,
            previousValue: 9
        });
    }

    @test()
    public async getBalanceOnZeroBet() {
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );
        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 1.5,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 3,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );

        const result = await this.freeBetService.commitFreeBetPayment(freeBetRequest);
        expect(result).deep.equal({
            freeBets: {
                amount: 3
            },
            main: 10.5
        });
    }

    @test()
    public async getBalanceOnMiddleWinInRound() {
        this.findRoundStub.returns(Promise.resolve(returnFakeRoundEntity({ operatorRoundId: "game_round" })));
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );
        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 1.5,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 3,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );

        const result = await this.freeBetService.commitFreeBetWinPayment(freeBetRequest);
        expect(result).deep.equal({
            freeBets: {
                amount: 3
            },
            main: 10.5
        });
    }

    @test()
    public async freeBetsDisabledForGame() {
        const request = _.cloneDeep(getFreeBetInfoRequest);
        request.gameToken.freeBetsDisabled = true;
        await expect(this.freeBetService.getFreeBetInfo(request)).to.be.rejectedWith(ValidationError);
    }
}
