import { FreeBetInfoSupport, FreeBetPaymentSupport, HttpGateway } from "@skywind-group/sw-integration-core";
import {
    IntegrationGameTokenData,
    IntegrationGetFreeBetInfoRequest,
    IntegrationPaymentRequest
} from "@entities/integration.entities";
import { Balance, FreeBetInfo, InvalidFreebet, InsufficientFreebet } from "@skywind-group/sw-wallet-adapter-core";
import { Injectable } from "@nestjs/common";
import { GetFreeSpinsInfoHandler } from "@wallet/freebet/free.bet.info.http.handler";
import { OperatorGetFreeSpinsResponse } from "@entities/operator.entities";
import { RoundService } from "@wallet/round/round.service";
import { FreeBetHttpHandler } from "@wallet/freebet/free.bet.http.handler";
import { FreeBetWinHttpHandler } from "@wallet/freebet/free.bet.win.http.handler";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@payment/round.http.handler";
import { BalanceHttpHandler } from "@payment/balance.http.handler";
import { measures } from "@skywind-group/sw-utils";
import measure = measures.measure;
import { PaymentService } from "@payment/payment.service";
import { UNKNOWN_BALANCE } from "@entities/sw.entities";
import { MerchantNonRetriableError, rollbackCondition, ValidationError } from "@errors/sw.errors";

@Injectable()
export class FreeBetService
    implements FreeBetInfoSupport<IntegrationGameTokenData>, FreeBetPaymentSupport<IntegrationGameTokenData> {
    constructor(
        private httpGateway: HttpGateway,
        private freeBetInfoHandler: GetFreeSpinsInfoHandler,
        private roundService: RoundService,
        private freeBetHandler: FreeBetHttpHandler,
        private freeBetWinHandler: FreeBetWinHttpHandler,
        private startRoundHandler: StartRoundHttpHandler,
        private endRoundHandler: EndRoundHttpHandler,
        private balanceHandler: BalanceHttpHandler,
        private freeSpinsInfoHandler: GetFreeSpinsInfoHandler,
        private paymentService: PaymentService
    ) {}

    async getFreeBetInfo(req: IntegrationGetFreeBetInfoRequest): Promise<FreeBetInfo> {
        this.validateFreeBetMode(req);
        const operatorFreeSpinsInfo: OperatorGetFreeSpinsResponse = await this.httpGateway.request<
            IntegrationGetFreeBetInfoRequest,
            OperatorGetFreeSpinsResponse
        >(req, this.freeBetInfoHandler);
        const { coinMultiplier, stakeAll } = req.freeBetRequest;
        if (
            coinMultiplier !== operatorFreeSpinsInfo.NumberOfLines ||
            !stakeAll.includes(operatorFreeSpinsInfo.StakeAmountPerLine)
        ) {
            throw new InvalidFreebet();
        }
        return {
            amount: operatorFreeSpinsInfo.NumberOfSpinsRemaining,
            coin: operatorFreeSpinsInfo.StakeAmountPerLine
        };
    }

    @measure({ name: "FreeBetService.commitFreeBetPayment", isAsync: true })
    async commitFreeBetPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        this.validateFreeBetMode(req);

        if (!req.request.freeBetCoin) {
            return this.paymentService.getBalance(req);
        }
        let operatorRoundId;
        try {
            operatorRoundId = await this.paymentService.openAndGetRoundIfNeeded(req);
        } catch (err) {
            if (!req.request.offlineRetry && rollbackCondition(err)) {
                // To prevent retry of bet we need to return http4xx error, because we don't have refund for free bets
                throw new MerchantNonRetriableError("Cannot start round");
            }
            throw err;
        }
        return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
            { ...req, operatorRoundId },
            this.freeBetHandler
        );
    }

    @measure({ name: "FreeBetService.commitFreeBetWinPayment", isAsync: true })
    async commitFreeBetWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        // Prevent getBalance request during finalization
        if (req.request.finalizationType) {
            return UNKNOWN_BALANCE;
        }

        this.validateFreeBetMode(req);
        const operatorRoundId = await this.roundService.getOperatorRoundId(req.request.roundPID);
        let balance: Balance;

        if (req.request.roundEnded && req.request.totalWin) {
            balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId },
                this.freeBetWinHandler
            );
        } else {
            balance = await this.paymentService.getBalance(req);
        }

        await this.paymentService.closeRoundIfNeeded(req, operatorRoundId);
        return balance;
    }

    private validateFreeBetMode(req: IntegrationPaymentRequest | IntegrationGetFreeBetInfoRequest) {
        const tokenData = this.balanceHandler.getGameTokenData(req);
        if (!tokenData.freeSpinToken) {
            throw new InsufficientFreebet();
        }
        if (tokenData.freeBetsDisabled) {
            throw new ValidationError("Free bets disabled for this game");
        }
    }
}
