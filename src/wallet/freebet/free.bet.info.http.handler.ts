import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGetFreeBetInfoRequest, IntegrationPaymentRequest } from "@entities/integration.entities";
import { OperatorGetFreeSpinsRequest, OperatorGetFreeSpinsResponse } from "@entities/operator.entities";
import { BaseHttpHand<PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";

export class GetFreeSpinsInfoHandler extends BaseHttpHandler
    implements HttpHandler<IntegrationPaymentRequest | IntegrationGetFreeBetInfoRequest> {
    public async build(
        req: IntegrationPaymentRequest | IntegrationGetFreeBetInfoRequest
    ): Promise<HTTPOperatorRequest> {
        const tokenData =
            (<IntegrationPaymentRequest>req).gameTokenData || (<IntegrationGetFreeBetInfoRequest>req).gameToken;
        const freeBetInfoRequest: OperatorGetFreeSpinsRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: tokenData.privateToken,
            FreeSpinToken: tokenData.freeSpinToken,
            GameCode: tokenData.gameCode
        };
        return this.buildHttpRequest({
            path: "FreeSpin",
            method: "get",
            payload: freeBetInfoRequest,
            jurisdiction: tokenData.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: false
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<OperatorGetFreeSpinsResponse> {
        return await this.parseHttpResponse<OperatorGetFreeSpinsResponse>(response);
    }
}
