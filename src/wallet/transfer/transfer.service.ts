import { Injectable } from "@nestjs/common";
import { HttpGateway, TransferPaymentSupport } from "@skywind-group/sw-integration-core";
import {
    Balance,
    MerchantAdapterAPIError,
    RequireRefundBetError,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import {
    IntegrationGameTokenData,
    IntegrationKeepAliveRequest,
    IntegrationPaymentRequest,
    IntegrationTransferRequest
} from "@entities/integration.entities";
import {
    OperatorEndGameRoundResponse,
    OperatorGetRealityCheckResponse,
    OperatorStartGameRoundResponse
} from "@entities/operator.entities";
import { RoundService } from "@wallet/round/round.service";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@wallet/payment/round.http.handler";
import { BalanceHttpHandler } from "@wallet/payment/balance.http.handler";
import { TransferInHttpHandler } from "@wallet/transfer/transferIn.handler";
import { TransferOutHttpHandler } from "@wallet/transfer/transferOut.handler";
import { rollbackCondition } from "@errors/sw.errors";
import { logging, measures } from "@skywind-group/sw-utils";
import { sumMajorUnits } from "@utils/common";
import { PaymentService } from "@payment/payment.service";
import measure = measures.measure;
import { RealityCheckHttpHandler } from "@wallet/regulation/regulation.http.handler";

const log = logging.logger("transfer-service");

@Injectable()
export class TransferService implements TransferPaymentSupport<IntegrationGameTokenData> {
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly roundService: RoundService,
        private readonly transferInHandler: TransferInHttpHandler,
        private readonly transferOutHandler: TransferOutHttpHandler,
        private readonly startRoundHandler: StartRoundHttpHandler,
        private readonly endRoundHandler: EndRoundHttpHandler,
        private readonly balanceHandler: BalanceHttpHandler,
        private readonly realityCheckHandler: RealityCheckHttpHandler,
        private readonly paymentService: PaymentService
    ) {}

    @measure({ name: "PaymentService.transferIn", isAsync: true })
    public async transferIn(req: IntegrationTransferRequest): Promise<Balance> {
        try {
            const operatorRoundId = await this.openAndGetRoundIfNeeded(req);

            return await this.httpGateway.request<IntegrationTransferRequest, Balance>(
                { ...req, operatorRoundId },
                this.transferInHandler
            );
        } catch (err) {
            if (this.isRefundNeeded(req, err)) {
                log.info("Refund operator's bet");
                throw new RequireRefundBetError();
            }

            // status: 400 - 499 - rollback (code 800 - refund)
            // status: 500 - offline retry
            log.info("Rollback bet");
            throw err;
        }
    }

    @measure({ name: "PaymentService.transferOut", isAsync: true })
    public async transferOut(req: IntegrationTransferRequest): Promise<Balance> {
        const zeroTransfer: boolean = !req.request.amount;
        const operatorRoundId = await this.roundService.getOperatorRoundId(req.request.roundPID, zeroTransfer);
        let balance: Balance;
        if (zeroTransfer) {
            balance = await this.paymentService.getBalance(req);
        } else {
            balance = await this.httpGateway.request<IntegrationTransferRequest, Balance>(
                { ...req, operatorRoundId },
                this.transferOutHandler
            );
        }
        await this.closeRoundIfNeeded(req, operatorRoundId);
        return balance;
    }

    public async keepAlive(req: IntegrationKeepAliveRequest): Promise<void> {
        // in order to check reality check status
        await this.httpGateway.request<IntegrationKeepAliveRequest, OperatorGetRealityCheckResponse>(
            req,
            this.realityCheckHandler
        );
    }

    private async openAndGetRoundIfNeeded(req: IntegrationTransferRequest): Promise<string> {
        try {
            return await this.roundService.getOperatorRoundId(req.request.roundPID);
        } catch (error) {
            if (error instanceof MerchantAdapterAPIError) {
                const startRoundResponse = await this.httpGateway.request<
                    IntegrationPaymentRequest,
                    OperatorStartGameRoundResponse
                >(req, this.startRoundHandler);

                await this.roundService.setOperatorRoundId({
                    operatorRoundId: startRoundResponse.GameRoundID,
                    swRoundId: req.request.roundPID
                });

                return startRoundResponse.GameRoundID;
            }

            throw error;
        }
    }

    private async closeRoundIfNeeded(req: IntegrationTransferRequest, operatorRoundId: string) {
        if (operatorRoundId && req.request.roundEnded) {
            await this.httpGateway.request<IntegrationTransferRequest, OperatorEndGameRoundResponse>(
                { ...req, operatorRoundId },
                this.endRoundHandler
            );
        }
    }

    private isRefundNeeded(req: IntegrationTransferRequest, error: SWError): boolean {
        return !!(
            // TODO: check with jp
            (!req.request.offlineRetry && rollbackCondition(error))
        );
    }

    private isRoundClosureNeeded(req: IntegrationTransferRequest): boolean {
        return !!(
            req.request.roundEnded &&
            // Should not close round if transfer is partial
            sumMajorUnits(req.request.previousAmount, -req.request.amount || 0)
        );
    }
}
