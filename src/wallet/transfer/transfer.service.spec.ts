import "module-alias/register";
import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import * as chaiAsPromised from "chai-as-promised";
import * as request from "superagent";
import { mapping, testing } from "@skywind-group/sw-utils";
import { expect, should, use } from "chai";
import { Test } from "@nestjs/testing";
import * as _ from "lodash";
import { HttpGateway, HttpGatewayConfig, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { PaymentService } from "@payment/payment.service";
import { BalanceHttpHandler } from "@payment/balance.http.handler";
import { BetHttpHandler } from "@payment/bet.http.handler";
import { WinHttpHandler } from "@payment/win.http.handler";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@payment/round.http.handler";
import { RoundService } from "@wallet/round/round.service";
import { RefundService } from "@wallet/refund/refund.service";
import { RefundHttpHandler } from "@wallet/refund/refund.http.handler";
import { Names } from "@names";
import { IntegrationGameTokenData, IntegrationTransferRequest } from "@entities/integration.entities";
import { Balance, ConnectionError, MerchantInfo, RequireRefundBetError } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorActionType,
    OperatorEndGameRoundRequest,
    OperatorEndGameRoundResponse,
    OperatorRealityCheckDetails,
    OperatorRealityCheckStatus,
    OperatorStartGameRoundRequest,
    OperatorStartGameRoundResponse,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType
} from "@entities/operator.entities";
import { checkRequests } from "@utils/test/testing";
import * as swErrors from "@errors/sw.errors";
import { RGRealityCheckError } from "@errors/sw.errors";
import { b365ErrorCodes } from "@errors/operator.errors";
import config from "@config";
import { JackpotWinHttpHandler } from "@wallet/jackpot/jackpot.win.http.handler";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { TransferInHttpHandler } from "@wallet/transfer/transferIn.handler";
import { TransferOutHttpHandler } from "@wallet/transfer/transferOut.handler";
import { TransferService } from "@wallet/transfer/transfer.service";
import { GetFreeSpinsInfoHandler } from "@wallet/freebet/free.bet.info.http.handler";
import status200 = testing.status200;
import status500 = testing.status500;
import onCall = testing.onCall;
import DBModel = mapping.DBModel;
import status400 = testing.status400;
import { BonusHttpHandler } from "@payment/bonus.http.handler";
import { RealityCheckHttpHandler } from "@wallet/regulation/regulation.http.handler";
import { RoundDto } from "@wallet/round/round.dto";
import { returnFakeRoundEntity, roundRepository } from "@utils/test/stubs";
import { AwardPromoHttpHandler } from "@payment/awardPromo.http.handler";

should();
use(chaiAsPromised);

const httpGatewayConfig = {
    operatorUrl: "http://operator/API",
    keepAlive: {
        socketActiveTTL: 60000,
        maxFreeSockets: 100,
        freeSocketKeepAliveTimeout: 12000
    }
} as HttpGatewayConfig;

const retryFakeFunc = () => {
    let attempt = config.operator.retryPolicy.attempts;
    return async (err: Error): Promise<boolean> =>
        (err instanceof ConnectionError || err instanceof swErrors.GeneralError) && --attempt > 0;
};

const gameTokenData = {
    merchantType: "b365",
    merchantCode: "b365__UK",
    sessionId: "session_1232434",
    country: "GB",
    currency: "EUR",
    playerCode: "b365_player",
    language: "en",
    brandId: 1111,
    gameCode: "GAME001",
    privateToken: "private_token",
    channelId: 222,
    jurisdiction: "UK",
    isPromoInternal: false
} as IntegrationGameTokenData;

const merchantInfo = {
    brandId: 1111,
    type: "b365",
    code: "b365__UK",
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
} as MerchantInfo;

const transferInRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        amount: 50,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: "",
        eventId: 1
    }
} as IntegrationTransferRequest;

const transferOutRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        amount: 50,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: "",
        roundEnded: true,
        totalWin: 50
    }
} as IntegrationTransferRequest;

@suite()
class TransferServiceSpec {
    public requestMock: testing.RequestMock = testing.requestMock(request);
    public findRoundStub: sinon.SinonStub;
    public createRoundStub: sinon.SinonStub;
    public retryTransferOutStub: sinon.SinonStub;
    public retryTransferInStub: sinon.SinonStub;
    public endRoundSpy: sinon.SinonSpy;
    public transferService: TransferService;
    public refundService: RefundService;
    public transferOutHttpHandler: TransferOutHttpHandler;
    public transferInHttpHandler: TransferInHttpHandler;
    public endRoundHttpHandler: EndRoundHttpHandler;
    public fakeRetry;

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                HttpGateway,
                {
                    provide: CoreNames.HttpGatewayConfig,
                    useValue: httpGatewayConfig
                },
                {
                    provide: Names.RoundRepository,
                    useValue: roundRepository
                },
                RoundService,
                EndRoundHttpHandler,
                AwardPromoHttpHandler,
                StartRoundHttpHandler,
                BalanceHttpHandler,
                TransferService,
                TransferInHttpHandler,
                TransferOutHttpHandler,
                RealityCheckHttpHandler,
                PaymentService,
                BetHttpHandler,
                WinHttpHandler,
                JackpotWinHttpHandler,
                JackpotStatisticsMapper,
                RefundHttpHandler,
                GetFreeSpinsInfoHandler,
                BonusHttpHandler
            ]
        }).compile();

        this.transferService = moduleRef.get<TransferService>(TransferService);
        this.transferOutHttpHandler = moduleRef.get<TransferOutHttpHandler>(TransferOutHttpHandler);
        this.transferInHttpHandler = moduleRef.get<TransferInHttpHandler>(TransferInHttpHandler);
        this.endRoundHttpHandler = moduleRef.get<EndRoundHttpHandler>(EndRoundHttpHandler);
        this.createRoundStub = sinon.stub(roundRepository, "insertOne");
        this.findRoundStub = sinon.stub(roundRepository, "findOne");
        this.retryTransferOutStub = sinon.stub(this.transferOutHttpHandler, "retry");
        this.retryTransferInStub = sinon.stub(this.transferInHttpHandler, "retry");
        this.endRoundSpy = sinon.spy(this.endRoundHttpHandler, "build");
        this.fakeRetry = sinon.fake(retryFakeFunc());
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    @test()
    public async transferIn() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const startGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            ChannelID: 222,
            ExternalGameRoundID: "1"
        } as OperatorStartGameRoundRequest;

        const betTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.TransferFunds,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 10.5,
            previousValue: 60.5
        } as Balance;

        const actualResponse = await this.transferService.transferIn(transferInRequest);

        checkRequests(this.requestMock, [
            {
                body: startGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "StartTimeUTC", "UTCTimeStamp"]
                }
            },
            {
                body: betTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async doRefund() {
        this.retryTransferInStub.returns(this.fakeRetry);

        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        const betTransactionErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            ErrorDetails: [
                {
                    ErrorMessage: "A technical error occurred when processing the request",
                    ErrorCode: b365ErrorCodes.TechnicalError
                }
            ]
        };

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            onCall(status500(betTransactionErrorResponse))
                .onCall(status500(betTransactionErrorResponse))
                .onCall(status500(betTransactionErrorResponse))
                .onCall(status500(betTransactionErrorResponse))
        );

        await this.transferService.transferIn(transferInRequest).should.eventually.rejectedWith(RequireRefundBetError);
    }

    @test()
    public async transferOut() {
        this.findRoundStub.returns(Promise.resolve(returnFakeRoundEntity({ operatorRoundId: "game_round" })));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.TransferFunds,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 50.5
        } as Balance;

        const actualResponse = await this.transferService.transferOut(transferOutRequest);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async transferOutWithJpWin() {
        this.findRoundStub.returns(Promise.resolve(returnFakeRoundEntity({ operatorRoundId: "game_round" })));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.TransferFunds,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round",
            JackpotDetails: [
                {
                    Amount: 50.5,
                    Code: "SW-JP",
                    NetworkId: "none"
                }
            ]
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 50.5
        } as Balance;

        const jpTransferRoundRequest = _.cloneDeep(transferOutRequest);
        jpTransferRoundRequest.request.jackpotDetails = {
            jackpots: {
                "SW-JP": {
                    jp_pot_1: {
                        win: 25.2512
                    } as any,
                    jp_pot_2: {
                        win: 25.2512
                    } as any
                }
            }
        } as any;

        const actualResponse = await this.transferService.transferOut(jpTransferRoundRequest);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async doRetryTransferOut() {
        this.retryTransferOutStub.returns(this.fakeRetry);
        this.findRoundStub.returns(Promise.resolve(returnFakeRoundEntity({ operatorRoundId: "game_round" })));

        const winTransactionErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            AmountInGBP: null,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: [
                {
                    ErrorMessage: "A technical error occurred when processing the request",
                    ErrorCode: b365ErrorCodes.TechnicalError
                }
            ]
        } as OperatorTransactionResponse;
        const winTransactionResponse = {
            Balance: { TotalAmount: 100 },
            CurrencyCode: "EUR",
            AmountInGBP: null,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: []
        } as OperatorTransactionResponse;

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            onCall(status500(winTransactionErrorResponse)).onCall(status200(winTransactionResponse))
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.TransferFunds,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100,
            previousValue: 50
        } as Balance;

        const actualResponse = await this.transferService.transferOut(transferOutRequest);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(this.fakeRetry.callCount).to.equal(1);
        expect(expectedResponse).to.be.deep.equal(actualResponse);
    }

    @test()
    public async realityCheck() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status400({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {
                    AccountHistoryUrl: "http://chebureck-egor.com",
                    RealityCheckStatus: OperatorRealityCheckStatus.Due
                },
                ErrorDetails: [
                    {
                        ErrorCode: "RealityCheckPending",
                        ErrorMessage: "The user needs to respond to a reality check to continue gambling."
                    }
                ]
            } as OperatorTransactionResponse)
        );

        await this.transferService
            .transferIn(transferInRequest)
            .should.eventually.rejectedWith(RGRealityCheckError)
            .then((err) => {
                expect(err.extraData).to.be.deep.equal({
                    messageArray: [
                        {
                            buttons: [
                                {
                                    label: "Keep Playing",
                                    gameAction: "continue",
                                    translate: true,
                                    serverCall: {
                                        regulatoryAction: "resetRealityCheck"
                                    }
                                },
                                {
                                    label: "Quit",
                                    gameAction: "lobby",
                                    translate: true,
                                    serverCall: {
                                        regulatoryAction: "closeSession"
                                    }
                                },
                                {
                                    gameAction: "link",
                                    link: "http://chebureck-egor.com",
                                    openLinkInNewTab: true,
                                    label: "Show game history",
                                    translate: true
                                }
                            ],
                            translate: true,
                            msgTitle: ""
                        }
                    ],
                    useServerMessage: false
                });
            });
    }

    @test()
    public async doNotOpenRound() {}

    @test()
    public async zeroTransferOutRoundOpened() {
        this.findRoundStub.returns(Promise.resolve(returnFakeRoundEntity({ operatorRoundId: "game_round" })));

        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const req = _.cloneDeep(transferOutRequest);
        req.request.amount = 0;
        const actualResponse = await this.transferService.transferOut(req);

        expect(actualResponse).to.be.deep.equal({ main: 10.5 });
    }

    @test()
    public async zeroTransferOutRoundNotFound() {
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );
        const req = _.cloneDeep(transferOutRequest);
        req.request.amount = 0;
        const actualResponse = await this.transferService.transferOut(req);
        expect(actualResponse).to.be.deep.equal({ main: 10.5 });
    }
}
