import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationPaymentRequest, IntegrationTransferRequest } from "@entities/integration.entities";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { sumMajorUnits } from "@utils/common";
import { Injectable } from "@nestjs/common";

@Injectable()
export class TransferInHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    constructor(/*private readonly jackpotStatisticsMapper: JackpotStatisticsMapper*/) {
        super();
    }

    public async build(req: IntegrationTransferRequest): Promise<HTTPOperatorRequest> {
        const betRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.TransferFunds,
            Amount: req.request.amount,
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.BET,
            GameRoundID: req.operatorRoundId
        };

        // TODO: check JP in the arcade games
        /*if (req.request.totalJpContribution && req.request.jackpotDetails) {
            betRequest.JackpotDetails = this.jackpotStatisticsMapper.mapContributionStatistics(req.request);
        }*/

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: betRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response, req: IntegrationTransferRequest): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorTransactionResponse>(response);
        const balance: Balance = { main: parsedResponse.Balance.TotalAmount };
        balance.previousValue = sumMajorUnits(balance.main, req.request.amount);
        return balance;
    }
}
