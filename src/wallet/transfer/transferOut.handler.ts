import { CommitPaymentRequest, <PERSON>ttp<PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationTransferRequest } from "@entities/integration.entities";
import { BaseHttpHand<PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { sumMajorUnitsInArray } from "@utils/common";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { Injectable } from "@nestjs/common";

@Injectable()
export class TransferOutHttpHandler extends BaseHttpHandler
    implements <PERSON>ttpHandler<CommitPaymentRequest<IntegrationGameTokenData>> {
    constructor(private readonly jackpotStatisticsMapper: JackpotStatisticsMapper) {
        super();
    }

    public async build(req: IntegrationTransferRequest): Promise<HTTPOperatorRequest> {
        const winRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.TransferFunds,
            Amount: req.request.amount, //sumMajorUnits(req.request.amount, -req.request.jpWinAmount || 0),
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.WIN,
            GameRoundID: req.operatorRoundId
        };

        if (req.request.jackpotDetails) {
            const jackpotDetails = this.jackpotStatisticsMapper.mapWinStatistics(req.request, true);
            if (jackpotDetails.length) {
                winRequest.JackpotDetails = jackpotDetails;
            }
        }

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: winRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response, req: IntegrationTransferRequest): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorTransactionResponse>(response);
        const balance: Balance = { main: parsedResponse.Balance.TotalAmount };

        // MEMO: previousValue = current - (totalWin - totalJpWin)
        balance.previousValue = sumMajorUnitsInArray([balance.main, -req.request.amount || 0]);
        return balance;
    }
}
