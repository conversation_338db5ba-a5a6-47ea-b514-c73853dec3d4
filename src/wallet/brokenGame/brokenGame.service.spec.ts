import "module-alias/register";
import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import * as chaiAsPromised from "chai-as-promised";
import { testing } from "@skywind-group/sw-utils";
import { expect, should, use } from "chai";
import { Test } from "@nestjs/testing";
import { HttpGateway, HttpGatewayConfig, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { RoundService } from "@wallet/round/round.service";
import { Names } from "@names";
import { IntegrationBrokenGameRequest, IntegrationGameTokenData } from "@entities/integration.entities";
import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { BetHttpHandler } from "@payment/bet.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";
import { EndRoundHttpHandler } from "@payment/round.http.handler";
import { BrokenGameService } from "@wallet/brokenGame/brokenGame.service";
import { FreeBetWinHttpHandler } from "@wallet/freebet/free.bet.win.http.handler";
import * as request from "superagent";
import {
    OperatorEndGameRoundResponse,
    OperatorFreeSpinTransactionResponse,
    OperatorRealityCheckDetails,
    OperatorTransactionResponse,
    OperatorTransactionType
} from "@entities/operator.entities";
import _ = require("lodash");
import status200 = testing.status200;
import status400 = testing.status400;

should();
use(chaiAsPromised);

const httpGatewayConfig = {
    operatorUrl: "http://operator/API",
    keepAlive: {
        socketActiveTTL: 60000,
        maxFreeSockets: 100,
        freeSocketKeepAliveTimeout: 12000
    }
} as HttpGatewayConfig;

const roundRepository = {
    findOne: () => {
        return { operatorRoundId: "1488" };
    },
    create: () => {
        /*empty*/
    }
} as any;

const gameTokenData = {
    merchantType: "b365",
    merchantCode: "b365__UK",
    sessionId: "session_1232434",
    country: "GB",
    currency: "EUR",
    playerCode: "b365_player",
    language: "en",
    brandId: 1111,
    gameCode: "GAME001",
    privateToken: "private_token",
    channelId: 222,
    jurisdiction: "UK",
    isPromoInternal: false
} as IntegrationGameTokenData;

const merchantInfo = {
    brandId: 1111,
    type: "b365",
    code: "b365__UK",
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
} as MerchantInfo;

const brokenGameRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        totalWin: 50,
        totalBet: 5,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        roundStatistics: {
            totalBet: 10,
            totalWin: 50
        },
        ts: "",
        roundEnded: true
    }
} as IntegrationBrokenGameRequest;

@suite()
class BrokenGameServiceSpec {
    private brokenGameService: BrokenGameService;
    private requestMock: testing.RequestMock = testing.requestMock(request);

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                HttpGateway,
                {
                    provide: CoreNames.HttpGatewayConfig,
                    useValue: httpGatewayConfig
                },
                {
                    provide: Names.RoundRepository,
                    useValue: roundRepository
                },
                RoundService,
                JackpotStatisticsMapper,
                BetHttpHandler,
                WinHttpHandler,
                EndRoundHttpHandler,
                BrokenGameService,
                FreeBetWinHttpHandler
            ]
        }).compile();
        this.brokenGameService = moduleRef.get<BrokenGameService>(BrokenGameService);
    }

    public async after() {
        sinon.restore();
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
    }

    @test()
    public async finalizeGameWithoutWin() {
        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );
        const paymentRequest = _.cloneDeep(brokenGameRequest);
        paymentRequest.request.totalWin = 0;
        paymentRequest.request.roundStatistics.balanceBefore = 1500;
        const actualResponse = await this.brokenGameService.finalizeGame(paymentRequest);
        expect(actualResponse).deep.equal({
            main: 1500,
            previousValue: 1500
        });
    }

    @test()
    public async finalizeGameWithWinWithoutJP() {
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );
        const actualResponse = await this.brokenGameService.finalizeGame(brokenGameRequest);
        expect(actualResponse).deep.equal({
            main: 100.5,
            previousValue: 50.5
        });
        const paymentRequest = this.requestMock.args[0];
        expect(paymentRequest.body).deep.include({
            Amount: 50,
            TransactionType: OperatorTransactionType.Return
        });
    }

    @test()
    public async finalizeGameWithJpWinOnly() {
        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );
        const paymentRequest = _.cloneDeep(brokenGameRequest);
        paymentRequest.request.totalWin = 900000;
        paymentRequest.request.totalJpWin = 900000;
        paymentRequest.request.roundStatistics.balanceBefore = 1500;
        paymentRequest.request.roundStatistics.balanceAfter = 1000000;
        const actualResponse = await this.brokenGameService.finalizeGame(paymentRequest);
        expect(actualResponse).deep.equal({
            main: 1000000,
            previousValue: 1000000
        });
    }

    @test()
    public async finalizeGameWithWinWithJpAndRoundWin() {
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );
        const finalize = _.cloneDeep(brokenGameRequest);
        finalize.request.totalJpWin = 900000;
        finalize.request.totalWin = 13.99 + finalize.request.totalJpWin;

        const actualResponse = await this.brokenGameService.finalizeGame(finalize);
        expect(actualResponse).deep.equal({
            main: 100.5,
            previousValue: 100.5 - 13.99
        });
        const paymentRequest = this.requestMock.args[0];
        expect(paymentRequest.body).deep.include({
            Amount: 13.99,
            TransactionType: OperatorTransactionType.Return
        });
    }

    @test()
    public async finalizeFreeBetRound() {
        this.requestMock.post(
            "http://operator/API/UK/FreeSpin/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                NumberOfSpinsRemaining: 0,
                ErrorDetails: []
            } as OperatorFreeSpinTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );
        const finalizeRequest = _.cloneDeep(brokenGameRequest);
        finalizeRequest.request.totalBet = 0;
        const actualResponse = await this.brokenGameService.finalizeGame(finalizeRequest);
        expect(actualResponse).deep.equal({
            main: 100.5,
            previousValue: 50.5,
            freeBets: {
                amount: 0
            }
        });
        const paymentRequest = this.requestMock.args[0];
        expect(paymentRequest.body).deep.include({
            Amount: 50,
            TransactionType: OperatorTransactionType.Return
        });
    }

    @test()
    public async finalizeWithOfflinePayments() {
        const actualResponse = await this.brokenGameService.finalizeWithOfflinePayments(brokenGameRequest);
        expect(actualResponse).deep.equal({ main: 0 });
    }

    @test()
    public async handleRoundAlreadyCloseError() {
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status400({
                ErrorDetails: [
                    {
                        ErrorCode: "GameRoundIsClosed",
                        ErrorMessage: "Game round 1/1000703341784 is closed"
                    }
                ]
            } as OperatorTransactionResponse)
        );
        const finalizeRequest = _.cloneDeep(brokenGameRequest);
        finalizeRequest.request.roundStatistics.balanceBefore = 1500;
        const response = await this.brokenGameService.finalizeGame(finalizeRequest);
        expect(response).deep.equal({ main: 1500, previousValue: 1500 });
    }

    @test()
    public async handleRoundAlreadyCloseErrorForFreeBets() {
        this.requestMock.post(
            "http://operator/API/UK/FreeSpin/Transaction",
            status400({
                ErrorDetails: [
                    {
                        ErrorCode: "GameRoundIsClosed",
                        ErrorMessage: "Game round 1/1000703341784 is closed"
                    }
                ]
            } as OperatorTransactionResponse)
        );
        const finalizeRequest = _.cloneDeep(brokenGameRequest);
        finalizeRequest.request.roundStatistics.balanceBefore = 1500;
        finalizeRequest.request.totalBet = 0;
        const response = await this.brokenGameService.finalizeGame(finalizeRequest);
        expect(response).deep.equal({ main: 1500, previousValue: 1500 });
    }
}
