import { BrokenGameSupport, HttpGateway } from "@skywind-group/sw-integration-core";
import {
    IntegrationBrokenGameRequest,
    IntegrationGameTokenData,
    IntegrationPaymentRequest
} from "@entities/integration.entities";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { Injectable } from "@nestjs/common";
import { UNKNOWN_BALANCE } from "@entities/sw.entities";
import { WinHttpHandler } from "@payment/win.http.handler";
import { RoundService } from "@wallet/round/round.service";
import { EndRoundHttpHandler } from "@payment/round.http.handler";
import { OperatorEndGameRoundResponse } from "@entities/operator.entities";
import { FreeBetWinHttpHandler } from "@wallet/freebet/free.bet.win.http.handler";
import { roundIsAlreadyClosed, sumMajorUnits } from "@utils/common";
import { RoundIsAlreadyClosedError } from "@errors/sw.errors";
import { logging } from "@skywind-group/sw-utils";

const log = logging.logger("broken-game-service");

/**
 * Operator's rules: With expired token it allows retries (start round, bet), wins, close round and doesn't
 * allow to get balance or free bets info!
 *
 * Jackpot wins during finalization will be handled by commitJackpotWinPayment immediately.
 * We have split payment - it means that we will have bets 0 for each spin (will be handled by payment service)
 * Free bet finalization payments will be handled by freebets service
 * Game wins during finalization will be skipped and payed at the end in finalize game payment.
 * Round will be closed after finalize game payment.
 *
 * Offline retries call getBalance, but in case of expired token return zero balance
 * */
@Injectable()
export class BrokenGameService implements BrokenGameSupport<IntegrationGameTokenData> {
    constructor(
        private httpGateway: HttpGateway,
        private roundService: RoundService,
        private winHandler: WinHttpHandler,
        private freeBetWinHandler: FreeBetWinHttpHandler,
        private endRoundHandler: EndRoundHttpHandler
    ) {}

    async finalizeGame(req: IntegrationBrokenGameRequest): Promise<Balance> {
        const operatorRoundId = await this.roundService.getOperatorRoundId(req.request.roundPID);
        let balance: Balance;
        try {
            balance = await this.doPayment(req, operatorRoundId);
        } catch (e) {
            /* When we get this error here it means that game server something bad with finalization/retry flow in GS,
             but we don't need to block this player, payments already sent to operator - returning balance from
             round statistics. Also we already have 10-20 infinite retries of finalization with round already ended error,
             this fix will close these rounds
             */
            if (e instanceof RoundIsAlreadyClosedError) {
                log.warn(e, "Wrong finalization flow", req);
                return this.getBalanceFromBalanceBeforeOfRoundStatistics(req);
            }
            throw e;
        }
        await this.closeRound(req, operatorRoundId);
        return balance;
    }

    async finalizeWithOfflinePayments(req: IntegrationPaymentRequest): Promise<Balance> {
        // NO other options, balance in history will be broken
        log.info("Skip fetching the balance during finalization payments. Return zero balance.");
        return UNKNOWN_BALANCE;
    }

    private async closeRound(req: IntegrationBrokenGameRequest, operatorRoundId: string): Promise<void> {
        try {
            await this.httpGateway.request<IntegrationPaymentRequest, OperatorEndGameRoundResponse>(
                { ...req, operatorRoundId },
                this.endRoundHandler
            );
        } catch (err) {
            if (roundIsAlreadyClosed(err)) {
                return;
            }
            throw err;
        }
    }

    private async doPayment(req: IntegrationBrokenGameRequest, operatorRoundId: string): Promise<Balance> {
        if (!req.request.totalBet) {
            return this.doFreeBetPayment(req, operatorRoundId);
        }
        return this.doRealPayment(req, operatorRoundId);
    }

    private async doRealPayment(req: IntegrationBrokenGameRequest, operatorRoundId: string): Promise<Balance> {
        // No wins in round
        if (!req.request.totalWin) {
            return this.getBalanceFromBalanceBeforeOfRoundStatistics(req);
        }
        // No jp wins in round or wins and jp wins in round
        if (sumMajorUnits(req.request.totalWin, -req.request.totalJpWin || 0) > 0) {
            return await this.httpGateway.request<IntegrationBrokenGameRequest, Balance>(
                { ...req, operatorRoundId },
                this.winHandler
            );
        }
        //Only jp wins - trying to find correct balance
        const balanceAfter = req.request.roundStatistics.balanceAfter;
        if (balanceAfter !== undefined) {
            // last payment in round was jp win
            return {
                main: balanceAfter,
                previousValue: balanceAfter
            };
        }
        return UNKNOWN_BALANCE;
    }

    private async doFreeBetPayment(req: IntegrationBrokenGameRequest, operatorRoundId: string): Promise<Balance> {
        // No win ins round
        if (!req.request.totalWin) {
            return this.getBalanceFromBalanceBeforeOfRoundStatistics(req);
        }
        return await this.httpGateway.request<IntegrationBrokenGameRequest, Balance>(
            { ...req, operatorRoundId },
            this.freeBetWinHandler
        );
    }

    private getBalanceFromBalanceBeforeOfRoundStatistics(req: IntegrationBrokenGameRequest): Balance {
        return {
            main: req.request.roundStatistics.balanceBefore,
            previousValue: req.request.roundStatistics.balanceBefore
        };
    }
}
