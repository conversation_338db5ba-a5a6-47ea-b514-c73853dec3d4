import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { JackpotDetails } from "@skywind-group/sw-wallet-adapter-core/src/skywind/definitions/payment";
import { JackpotStatisticsMapper } from "./jackpot.statistics.mapper";
import { JackpotWinDetails } from "@skywind-group/sw-wallet-adapter-core";

@suite()
class JackpotStatisticsMapperSpec {

    private mapper: JackpotStatisticsMapper = new JackpotStatisticsMapper();

    @test()
    contributionStatistics(): void {
        const request: { jackpotDetails: JackpotDetails } = {
            jackpotDetails: {
                jackpotTypes: {},
                contributionPrecision: 4,
                jackpots: {
                    jp1: {

                        p1: { contribution: { progressive: 0.000111111 }, win: 0 },
                        p2: { contribution: { progressive: 0.001, seed: 0.35 }, win: 0 },
                        p3: { contribution: { seed: 0.1444 }, win: 0 }
                    },
                    jp2: {
                        p1: { contribution: { progressive: 0.00015, seed: 0.0001 }, win: 0 }
                    }
                }
            }
        };
        const result = this.mapper.mapContributionStatistics(request as any);
        expect(result).deep.equal([
            {
                "Amount": 0.4955,
                "Code": "jp1",
                "NetworkId": "none"
            },
            {
                "Amount": 0.0002,
                "Code": "jp2",
                "NetworkId": "none"
            }
        ]);
    }

    @test()
    winStatistics(): void {
        const request: { jackpotWinDetails: JackpotWinDetails } = {
            jackpotWinDetails: {
                jp1: {
                    p1:  1.55,
                    p2:  0.01
                },
                jp2: {
                    p3: 13
                }
            }
        };
        const result = this.mapper.mapWinStatistics(request as any);
        expect(result).deep.equal([
            {
                "Amount": 1.56,
                "Code": "jp1",
                "NetworkId": "none"
            },
            {
                "Amount": 13,
                "Code": "jp2",
                "NetworkId": "none"
            }
        ]);
    }
}
