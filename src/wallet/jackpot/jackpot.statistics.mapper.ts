import { Injectable } from "@nestjs/common";
import { OperatorJackpotDetails } from "@entities/operator.entities";
import { JackpotIdDetails, JackpotPoolDetails, PaymentRequest } from "@skywind-group/sw-wallet-adapter-core";
import { calculation } from "@skywind-group/sw-utils";
import safeAddWithPrecision = calculation.safeAddWithPrecision;
import normalizeAmountByPrecision = calculation.normalizeAmountByPrecision;

@Injectable()
export class JackpotStatisticsMapper {
    private static readonly BET_PRECISION = 7;
    private static readonly WIN_PRECISION = 2;
    private static readonly EMPTY_NETWORK_ID = "none";

    mapContributionStatistics(paymentRequest: PaymentRequest): OperatorJackpotDetails[] {
        const precision = paymentRequest.jackpotDetails.contributionPrecision;
        const result: AccumulatedResult = Object.entries(paymentRequest.jackpotDetails.jackpots).reduce(
            (acc: AccumulatedResult, entry: [string, JackpotIdDetails]) => {
                const [jackpotId, details] = entry;

                const totalContribution = Object.values(details).reduce(
                    (acc: number, poolEntry: JackpotPoolDetails) => {
                        const totalContribution = safeAddWithPrecision(
                            precision,
                            poolEntry.contribution.progressive || 0,
                            poolEntry.contribution.seed || 0
                        );
                        acc = safeAddWithPrecision(precision, acc, totalContribution);
                        return acc;
                    },
                    0
                );

                acc[jackpotId] = {
                    Amount: normalizeAmountByPrecision(JackpotStatisticsMapper.BET_PRECISION, totalContribution),
                    Code: jackpotId,
                    NetworkId: JackpotStatisticsMapper.EMPTY_NETWORK_ID
                };
                return acc;
            },
            {}
        );
        return Object.values(result);
    }

    mapWinStatistics(paymentRequest: PaymentRequest, isArcade?: boolean): OperatorJackpotDetails[] {
        let result: AccumulatedResult;

        if (paymentRequest.jackpotWinDetails) {
            result = Object.entries(paymentRequest.jackpotWinDetails).reduce(
                (acc: AccumulatedResult, entry: [string, { [p: string]: number }]) => {
                    const [jackpotId, pool] = entry;
                    const totalJpWin = Object.values(pool).reduce((a, c) => a + c, 0);

                    // Don't allow zero amounts for arcade games
                    if (isArcade && !totalJpWin) {
                        return acc;
                    }

                    acc[jackpotId] = {
                        Amount: normalizeAmountByPrecision(JackpotStatisticsMapper.WIN_PRECISION, totalJpWin),
                        Code: jackpotId,
                        NetworkId: JackpotStatisticsMapper.EMPTY_NETWORK_ID
                    };
                    return acc;
                },
                {}
            );
        } else if (paymentRequest.jackpotDetails) {
            result = Object.entries(paymentRequest.jackpotDetails.jackpots).reduce(
                (acc: AccumulatedResult, entry: [string, JackpotIdDetails]) => {
                    const [jackpotId, details] = entry;
                    const totalJpWin = Object.values(details).reduce((a, { win }) => a + win, 0);

                    // Don't allow zero amounts for arcade games
                    if (isArcade && !totalJpWin) {
                        return acc;
                    }

                    acc[jackpotId] = {
                        Amount: normalizeAmountByPrecision(JackpotStatisticsMapper.WIN_PRECISION, totalJpWin),
                        Code: jackpotId,
                        NetworkId: JackpotStatisticsMapper.EMPTY_NETWORK_ID
                    };
                    return acc;
                },
                {}
            );
        }

        return Object.values(result);
    }
}

interface AccumulatedResult {
    [jackpotId: string]: OperatorJackpotDetails;
}
