import { CommitPaymentRequest, <PERSON>ttp<PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";

import { Base<PERSON>ttp<PERSON>and<PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { sumMajorUnitsInArray, sumMajorUnits } from "@utils/common";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { Injectable } from "@nestjs/common";

@Injectable()
export class JackpotWinHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    constructor(private readonly jackpotStatisticsMapper: JackpotStatisticsMapper) {
        super();
    }

    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const amount: number = this.getAmount(req);
        const winRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: amount,
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.JACKPOT,
            JackpotDetails: this.jackpotStatisticsMapper.mapWinStatistics(req.request),
            GameRoundID: req.operatorRoundId
        };

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: winRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorTransactionResponse>(response);
        const balance: Balance = { main: parsedResponse.Balance.TotalAmount };
        // TODO check history - maybe we need to decrease previous value to round win in case of round ended
        balance.previousValue = sumMajorUnits(balance.main, -req.request.win || 0);
        return balance;
    }

    private getAmount(req: IntegrationPaymentRequest): number {
        if (req.request.roundEnded && !req.request.finalizationType) {
            // In case of finalization we will pay totalRoundWin on finalizeGame
            return sumMajorUnitsInArray([req.request.totalWin, -req.request.totalJpWin || 0, req.request.win]);
        } else {
            return req.request.win || 0;
        }
    }
}
