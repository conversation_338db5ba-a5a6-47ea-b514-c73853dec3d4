import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGetBalanceRequest, IntegrationPaymentRequest } from "@entities/integration.entities";
import { OperatorGetBalanceRequest, OperatorGetBalanceResponse } from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";

export class BalanceHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    public async build(req: IntegrationGetBalanceRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);

        const balanceRequest: OperatorGetBalanceRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: gameTokenData.privateToken,
            GamingId: gameTokenData.playerCode
        };

        return this.buildHttpRequest({
            path: "Balance",
            method: "get",
            payload: balanceRequest,
            jurisdiction: gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorGetBalanceResponse>(response);
        return { main: parsedResponse.Balance.TotalAmount };
    }
}
