import { CommitPaymentRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import { Base<PERSON>ttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { OperatorAwardPromRequest, OperatorTransactionResponse, TransactionPostfix } from "@entities/operator.entities";

export class AwardPromoHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>> {
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const awardPromoRequest: OperatorAwardPromRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.AWARD_PROMO,
            GameCode: req.gameTokenData.gameCode,
            FreeSpinCount: req.request?.extraData?.freeSpinCount
        };

        return this.buildHttpRequest({
            path: "FreeSpin/Award",
            method: "post",
            payload: awardPromoRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<boolean> {
        const parsedResponse = await this.parseHttpResponse<OperatorTransactionResponse>(response);

        return parsedResponse.ErrorDetails === null;
    }
}
