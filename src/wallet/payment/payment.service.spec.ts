import "module-alias/register";
import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import * as chaiAsPromised from "chai-as-promised";
import * as request from "superagent";
import { testing } from "@skywind-group/sw-utils";
import { expect, should, use } from "chai";
import { Test } from "@nestjs/testing";
import * as _ from "lodash";
import { HttpGateway, HttpGatewayConfig, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { PaymentService } from "@payment/payment.service";
import { BalanceHttpHandler } from "@payment/balance.http.handler";
import { BetHttpHandler } from "@payment/bet.http.handler";
import { WinHttpHandler } from "@payment/win.http.handler";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@payment/round.http.handler";
import { RoundService } from "@wallet/round/round.service";
import { RefundService } from "@wallet/refund/refund.service";
import { RefundHttpHandler } from "@wallet/refund/refund.http.handler";
import { Names } from "@names";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import {
    Balance,
    ConnectionError,
    MerchantAdapterAPIError,
    MerchantInfo,
    RequireRefundBetError
} from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorActionType,
    OperatorAwardPromRequest,
    OperatorEndGameRoundRequest,
    OperatorEndGameRoundResponse,
    OperatorGetBalanceRequest,
    OperatorGetBalanceResponse,
    OperatorGetFreeSpinsResponse,
    OperatorRealityCheckDetails,
    OperatorRealityCheckStatus,
    OperatorStartGameRoundRequest,
    OperatorStartGameRoundResponse,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { checkRequests } from "@utils/test/testing";
import * as swErrors from "@errors/sw.errors";
import {
    AuthenticateFailedError,
    GameTokenExpired,
    PlayerIsSuspended,
    RGRealityCheckError,
    RoundIsAlreadyClosedError
} from "@errors/sw.errors";
import { b365ErrorCodes } from "@errors/operator.errors";
import config from "@config";
import { JackpotWinHttpHandler } from "@wallet/jackpot/jackpot.win.http.handler";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { SWJurisdiction } from "@entities/sw.entities";
import { GetFreeSpinsInfoHandler } from "@wallet/freebet/free.bet.info.http.handler";
import { BonusHttpHandler } from "@payment/bonus.http.handler";
import { roundRepository } from "@utils/test/stubs";
import status200 = testing.status200;
import status500 = testing.status500;
import onCall = testing.onCall;
import status400 = testing.status400;
import { AwardPromoHttpHandler } from "@payment/awardPromo.http.handler";

should();
use(chaiAsPromised);

const httpGatewayConfig = {
    operatorUrl: "http://operator/API",
    keepAlive: {
        socketActiveTTL: 60000,
        maxFreeSockets: 100,
        freeSocketKeepAliveTimeout: 12000
    }
} as HttpGatewayConfig;

const retryFakeFunc = () => {
    let attempt = config.operator.retryPolicy.attempts;
    return async (err: Error): Promise<boolean> =>
        (err instanceof ConnectionError || err instanceof swErrors.GeneralError) && --attempt > 0;
};

const gameTokenData = {
    merchantType: "b365",
    merchantCode: "b365__UK",
    sessionId: "session_1232434",
    country: "GB",
    currency: "EUR",
    playerCode: "b365_player",
    language: "en",
    brandId: 1111,
    gameCode: "GAME001",
    privateToken: "private_token",
    channelId: 222,
    jurisdiction: "UK",
    isPromoInternal: false
} as IntegrationGameTokenData;

const merchantInfo = {
    brandId: 1111,
    type: "b365",
    code: "b365__UK",
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
} as MerchantInfo;

const betRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        bet: 50,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: "",
        eventId: 0
    }
} as IntegrationPaymentRequest;

const winRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        win: 50,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: "",
        roundEnded: true,
        totalWin: 50
    }
} as IntegrationPaymentRequest;

const bonusRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        bet: 0,
        win: 50,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: "",
        roundEnded: true,
        eventId: 0
    }
} as IntegrationPaymentRequest;

@suite()
class PaymentServiceSpec {
    public requestMock: testing.RequestMock = testing.requestMock(request);
    public findRoundStub: sinon.SinonStub;
    public createRoundStub: sinon.SinonStub;
    public refundStub: sinon.SinonStub;
    public retryWinStub: sinon.SinonStub;
    public retryBetStub: sinon.SinonStub;
    public endRoundSpy: sinon.SinonSpy;
    public paymentService: PaymentService;
    public refundService: RefundService;
    public winHttpHandler: WinHttpHandler;
    public betHttpHandler: BetHttpHandler;
    public endRoundHttpHandler: EndRoundHttpHandler;
    public bonusHttpHandler: BonusHttpHandler;
    public fakeRetry;

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                HttpGateway,
                {
                    provide: CoreNames.HttpGatewayConfig,
                    useValue: httpGatewayConfig
                },
                {
                    provide: Names.RoundRepository,
                    useValue: roundRepository
                },
                RoundService,
                JackpotWinHttpHandler,
                JackpotStatisticsMapper,
                BetHttpHandler,
                WinHttpHandler,
                EndRoundHttpHandler,
                AwardPromoHttpHandler,
                StartRoundHttpHandler,
                RefundHttpHandler,
                BalanceHttpHandler,
                PaymentService,
                RefundService,
                GetFreeSpinsInfoHandler,
                BonusHttpHandler
            ]
        }).compile();

        this.paymentService = moduleRef.get<PaymentService>(PaymentService);
        this.refundService = moduleRef.get<RefundService>(RefundService);
        this.winHttpHandler = moduleRef.get<WinHttpHandler>(WinHttpHandler);
        this.betHttpHandler = moduleRef.get<BetHttpHandler>(BetHttpHandler);
        this.endRoundHttpHandler = moduleRef.get<EndRoundHttpHandler>(EndRoundHttpHandler);
        this.createRoundStub = sinon.stub(roundRepository, "insertOne");
        this.findRoundStub = sinon.stub(roundRepository, "findOne");
        this.refundStub = sinon.stub(this.refundService, "refundBetPayment");
        this.retryWinStub = sinon.stub(this.winHttpHandler, "retry");
        this.retryBetStub = sinon.stub(this.betHttpHandler, "retry");
        this.endRoundSpy = sinon.spy(this.endRoundHttpHandler, "build");
        this.fakeRetry = sinon.fake(retryFakeFunc());
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    @test()
    public async doBet() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const startGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            ChannelID: 222,
            ExternalGameRoundID: "1"
        } as OperatorStartGameRoundRequest;

        const betTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 10.5,
            previousValue: 60.5
        } as Balance;

        const actualResponse = await this.paymentService.commitBetPayment(betRequest);

        checkRequests(this.requestMock, [
            {
                body: startGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "StartTimeUTC", "UTCTimeStamp"]
                }
            },
            {
                body: betTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async getBalanceInsteadOfZeroBet() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const getBalanceExpectedRequestQuery = {
            PrivateToken: "private_token",
            GamingId: "b365_player"
        } as OperatorGetBalanceRequest;

        const expectedResponse = {
            main: 10.5
        } as Balance;

        const betRequestUpdated = _.cloneDeep(betRequest);
        betRequestUpdated.request.bet = 0;

        const actualResponse = await this.paymentService.commitBetPayment(betRequestUpdated);

        checkRequests(this.requestMock, [
            {
                body: {},
                headers: expectedRequestHeader,
                method: "GET",
                query: getBalanceExpectedRequestQuery,
                url: "http://operator/API/UK/Balance",
                keysToVoid: {
                    query: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async doRefund() {
        this.retryBetStub.returns(this.fakeRetry);

        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        const betTransactionErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            ErrorDetails: [
                {
                    ErrorMessage: "A technical error occurred when processing the request",
                    ErrorCode: b365ErrorCodes.TechnicalError
                }
            ]
        };

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            onCall(status500(betTransactionErrorResponse))
                .onCall(status500(betTransactionErrorResponse))
                .onCall(status500(betTransactionErrorResponse))
                .onCall(status500(betTransactionErrorResponse))
        );

        await this.paymentService.commitBetPayment(betRequest).should.eventually.rejectedWith(RequireRefundBetError);
    }

    @test()
    public async throwAuthenticationError() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status500({
                Balance: {},
                CurrencyCode: "EUR",
                ErrorDetails: [
                    {
                        ErrorMessage: "A technical error occurred when processing the request",
                        ErrorCode: b365ErrorCodes.AuthenticationFailed
                    }
                ]
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const startGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            ChannelID: 222,
            ExternalGameRoundID: "1"
        } as OperatorStartGameRoundRequest;

        const betTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        await this.paymentService.commitBetPayment(betRequest).should.eventually.rejectedWith(AuthenticateFailedError);

        checkRequests(this.requestMock, [
            {
                body: startGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "StartTimeUTC", "UTCTimeStamp"]
                }
            },
            {
                body: betTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);

        expect(this.refundStub.notCalled).to.equal(true);
    }

    @test()
    public async throwTokenExpiredError() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status500({
                Balance: {},
                CurrencyCode: "EUR",
                ErrorDetails: [
                    {
                        ErrorMessage: "The token in the request is invalid",
                        ErrorCode: b365ErrorCodes.InvalidToken
                    }
                ]
            } as OperatorTransactionResponse)
        );

        await this.paymentService.commitBetPayment(betRequest).should.eventually.rejectedWith(GameTokenExpired);
    }

    @test()
    public async throwErrorWhenCreatingRound() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );
        this.createRoundStub.returns(Promise.reject(new MerchantAdapterAPIError(null)));

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const startGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            ChannelID: 222,
            ExternalGameRoundID: "1"
        } as OperatorStartGameRoundRequest;

        await this.paymentService.commitBetPayment(betRequest).should.eventually.rejectedWith(MerchantAdapterAPIError);

        checkRequests(this.requestMock, [
            {
                body: startGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "StartTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
    }

    @test()
    public async doWin() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 50.5
        } as Balance;

        const actualResponse = await this.paymentService.commitWinPayment(winRequest);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async getBalanceInsteadOfZeroWin() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const getBalanceExpectedRequestQuery = {
            PrivateToken: "private_token",
            GamingId: "b365_player"
        } as OperatorGetBalanceRequest;

        const expectedResponse = {
            main: 10.5
        } as Balance;

        const winRequestUpdated = _.cloneDeep(winRequest);
        winRequestUpdated.request.totalWin = 0;
        const actualResponse = await this.paymentService.commitWinPayment(winRequestUpdated);

        checkRequests(this.requestMock, [
            {
                body: {},
                headers: expectedRequestHeader,
                method: "GET",
                query: getBalanceExpectedRequestQuery,
                url: "http://operator/API/UK/Balance",
                keysToVoid: {
                    query: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async throwAccountBlockedError() {
        this.retryWinStub.returns(this.fakeRetry);
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status500({
                Balance: {},
                CurrencyCode: "EUR",
                AmountInGBP: null,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: [
                    {
                        ErrorMessage: "A technical error occurred when processing the request",
                        ErrorCode: b365ErrorCodes.AccountBlocked
                    }
                ]
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        await this.paymentService.commitWinPayment(winRequest).should.eventually.rejectedWith(PlayerIsSuspended);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(this.fakeRetry.calledOnce).to.equal(true);
    }

    @test()
    public async doRetryWin() {
        this.retryWinStub.returns(this.fakeRetry);
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));

        const winTransactionErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            AmountInGBP: null,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: [
                {
                    ErrorMessage: "A technical error occurred when processing the request",
                    ErrorCode: b365ErrorCodes.TechnicalError
                }
            ]
        } as OperatorTransactionResponse;
        const winTransactionResponse = {
            Balance: { TotalAmount: 100 },
            CurrencyCode: "EUR",
            AmountInGBP: null,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: []
        } as OperatorTransactionResponse;

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            onCall(status500(winTransactionErrorResponse)).onCall(status200(winTransactionResponse))
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100,
            previousValue: 50
        } as Balance;

        const actualResponse = await this.paymentService.commitWinPayment(winRequest);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(this.fakeRetry.callCount).to.equal(1);
        expect(expectedResponse).to.be.deep.equal(actualResponse);
    }

    @test()
    public async doNotCallGettingRound() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorGetBalanceResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const getBalanceExpectedRequestQuery = {
            PrivateToken: "private_token",
            GamingId: "b365_player"
        } as OperatorGetBalanceRequest;

        const expectedResponse = {
            main: 100.5
        } as Balance;

        const winRequestUpdated = _.cloneDeep(winRequest);
        winRequestUpdated.request.roundEnded = false;
        const actualResponse = await this.paymentService.commitWinPayment(winRequestUpdated);

        checkRequests(this.requestMock, [
            {
                body: {},
                headers: expectedRequestHeader,
                method: "GET",
                query: getBalanceExpectedRequestQuery,
                url: "http://operator/API/UK/Balance",
                keysToVoid: {
                    query: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
        expect(this.endRoundSpy.notCalled).to.equal(true);
    }

    @test()
    public async doJpBet() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const startGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            ChannelID: 222,
            ExternalGameRoundID: "1"
        } as OperatorStartGameRoundRequest;

        const betTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_bet",
            GameRoundID: "game_round",
            JackpotDetails: [
                {
                    Amount: 2.0034013,
                    Code: "SW-RESPIN-KING-JP_Genesis_eu",
                    NetworkId: "none"
                }
            ]
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 10.5,
            previousValue: 60.5
        } as Balance;
        const jpBetRequest: any = _.cloneDeep(betRequest);
        jpBetRequest.request = {
            ...jpBetRequest.request,
            ...{
                totalJpContribution: 2.00340125,
                jackpotDetails: {
                    jackpotTypes: {
                        "sw-respin-king-jp": {
                            "0": "SW-RESPIN-KING-JP_Genesis_eu"
                        }
                    },
                    jackpots: {
                        "SW-RESPIN-KING-JP_Genesis_eu": {
                            "jackpot-1_3": {
                                contribution: {
                                    progressive: 0.60340125
                                },
                                win: 0
                            },
                            "jackpot-2_3": {
                                contribution: {
                                    progressive: 0.65
                                },
                                win: 0
                            },
                            "jackpot-3_3": {
                                contribution: {
                                    progressive: 0.3
                                },
                                win: 0
                            },
                            "jackpot-4_3": {
                                contribution: {
                                    progressive: 0.3
                                },
                                win: 0
                            },
                            "jackpot-5_3": {
                                contribution: {
                                    progressive: 0.15
                                },
                                win: 0
                            }
                        }
                    }
                }
            }
        };
        const actualResponse = await this.paymentService.commitBetPayment(jpBetRequest);

        checkRequests(this.requestMock, [
            {
                body: startGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "StartTimeUTC", "UTCTimeStamp"]
                }
            },
            {
                body: betTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async doJpWinWhenRoundEnded() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: 56.53,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_jp",
            GameRoundID: "game_round",
            JackpotDetails: [
                {
                    Amount: 14.6,
                    Code: "SW-RESPIN-KING-JP",
                    NetworkId: "none"
                }
            ]
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 94.72
        } as Balance;
        const jpWinRequest = _.cloneDeep(winRequest);
        jpWinRequest.request = {
            ...jpWinRequest.request,
            ...{
                jackpotWinDetails: {
                    "SW-RESPIN-KING-JP": {
                        "jackpot-2_1": 14.5955
                    }
                },
                isJPWin: true
            },
            win: 5.78,
            totalJpWin: 14.59,
            totalWin: 65.34
        };
        const actualResponse = await this.paymentService.commitJackpotWinPayment(jpWinRequest);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async doJpWinWhenRoundNotEnded() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: 5.78,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_jp",
            GameRoundID: "game_round",
            JackpotDetails: [
                {
                    Amount: 14.59,
                    Code: "SW-RESPIN-KING-JP",
                    NetworkId: "none"
                }
            ]
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 94.72
        } as Balance;
        const jpWinRequest = _.cloneDeep(winRequest);
        jpWinRequest.request = {
            ...jpWinRequest.request,
            ...{
                jackpotWinDetails: {
                    "SW-RESPIN-KING-JP": {
                        "jackpot-2_1": 14.59
                    }
                },
                isJPWin: true
            },
            win: 5.78,
            totalJpWin: 14.59,
            totalWin: 65.34,
            roundEnded: false
        };
        const actualResponse = await this.paymentService.commitJackpotWinPayment(jpWinRequest);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async getBalanceInsteadOfZeroJpWin() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const getBalanceExpectedRequestQuery = {
            PrivateToken: "private_token",
            GamingId: "b365_player"
        } as OperatorGetBalanceRequest;

        const expectedResponse = {
            main: 10.5
        } as Balance;

        const jpWinRequest = _.cloneDeep(winRequest);
        jpWinRequest.request = {
            ...jpWinRequest.request,
            ...{
                jackpotWinDetails: {
                    "SW-RESPIN-KING-JP": {
                        "jackpot-2_1": 0.001
                    }
                },
                isJPWin: true
            }
        };
        jpWinRequest.request.win = 0;
        const actualResponse = await this.paymentService.commitJackpotWinPayment(jpWinRequest);

        checkRequests(this.requestMock, [
            {
                body: {},
                headers: expectedRequestHeader,
                method: "GET",
                query: getBalanceExpectedRequestQuery,
                url: "http://operator/API/UK/Balance",
                keysToVoid: {
                    query: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async changeJurisdiction() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const startGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            ChannelID: 222,
            ExternalGameRoundID: "1"
        } as OperatorStartGameRoundRequest;

        const updatedBetRequest = _.cloneDeep(betRequest);
        updatedBetRequest.gameTokenData.jurisdiction = SWJurisdiction.GI;
        await this.paymentService.commitBetPayment(updatedBetRequest);

        checkRequests(this.requestMock, [
            {
                body: startGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "StartTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
    }

    @test()
    public async realityCheck() {
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status400({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {
                    AccountHistoryUrl: "http://chebureck-egor.com",
                    RealityCheckStatus: OperatorRealityCheckStatus.Due
                },
                ErrorDetails: [
                    {
                        ErrorCode: "RealityCheckPending",
                        ErrorMessage: "The user needs to respond to a reality check to continue gambling."
                    }
                ]
            } as OperatorTransactionResponse)
        );

        await this.paymentService
            .commitBetPayment(betRequest)
            .should.eventually.rejectedWith(RGRealityCheckError)
            .then((err) => {
                expect(err.extraData).to.be.deep.equal({
                    messageArray: [
                        {
                            buttons: [
                                {
                                    label: "Keep Playing",
                                    gameAction: "continue",
                                    translate: true,
                                    serverCall: {
                                        regulatoryAction: "resetRealityCheck"
                                    }
                                },
                                {
                                    label: "Quit",
                                    gameAction: "lobby",
                                    translate: true,
                                    serverCall: {
                                        regulatoryAction: "closeSession"
                                    }
                                },
                                {
                                    gameAction: "link",
                                    link: "http://chebureck-egor.com",
                                    openLinkInNewTab: true,
                                    label: "Show game history",
                                    translate: true
                                }
                            ],
                            translate: true,
                            msgTitle: ""
                        }
                    ],
                    useServerMessage: false
                });
            });
    }

    @test()
    public async doNotOpenRound() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const betTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 10.5,
            previousValue: 60.5
        } as Balance;

        const betRequestUpdated = _.cloneDeep(betRequest);
        betRequestUpdated.request.eventId = 1;

        const actualResponse = await this.paymentService.commitBetPayment(betRequestUpdated);

        checkRequests(this.requestMock, [
            {
                body: betTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async getBalanceWithFreeBets() {
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );
        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 2,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 3,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );

        const request: IntegrationPaymentRequest = {
            request: {} as any,
            merchantInfo,
            gameTokenData: { ...gameTokenData }
        };
        request.gameTokenData.freeSpinToken = "token";
        const result = await this.paymentService.getBalances(request);

        expect(result).deep.equal({
            EUR: {
                freeBets: {
                    amount: 3
                },
                main: 10.5
            }
        });
    }

    @test()
    public async winWithFreeBets() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 2,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 1,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );

        const winWithFsToken = _.cloneDeep(winRequest);
        winWithFsToken.gameTokenData.freeSpinToken = "t";

        const actualResponse = await this.paymentService.commitWinPayment(winWithFsToken);

        expect(actualResponse).to.be.deep.equal({
            main: 100.5,
            previousValue: 50.5,
            freeBets: {
                amount: 1
            }
        });
    }

    @test()
    public async jpWinWithFreeBets() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        this.requestMock.get(
            "http://operator/API/UK/FreeSpin",
            status200({
                StakeAmountPerLine: 2,
                CurrencyCode: "EUR",
                NumberOfLines: 10,
                NumberOfSpinsRemaining: 1,
                ErrorDetails: []
            } as OperatorGetFreeSpinsResponse)
        );

        const jpWinRequest = _.cloneDeep(winRequest);
        jpWinRequest.request = {
            ...jpWinRequest.request,
            ...{
                jackpotWinDetails: {
                    "SW-RESPIN-KING-JP": {
                        "jackpot-2_1": 14.59
                    }
                },
                isJPWin: true
            }
        };
        jpWinRequest.gameTokenData.freeSpinToken = "aaa";
        const actualResponse = await this.paymentService.commitJackpotWinPayment(jpWinRequest);

        expect(actualResponse).deep.equal({
            main: 100.5,
            previousValue: 50.5,
            freeBets: { amount: 1 }
        });
    }

    @test()
    public async getZeroBalanceWhenOfflineRetry() {
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status400({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: [
                    {
                        ErrorCode: "AnotherGameInProgress",
                        ErrorMessage: "Bad things happened"
                    }
                ]
            } as OperatorTransactionResponse)
        );
        const retryBetRequest = _.cloneDeep(betRequest);
        retryBetRequest.request.offlineRetry = true;

        const expectedResponse = { main: 0 };
        const actualResponse = await this.paymentService.getBalance(retryBetRequest);

        expect(actualResponse).deep.equal(expectedResponse);
    }

    @test()
    public async getBalanceWithFreeSpinTokenAndFreeBetsDisabled() {
        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const request: IntegrationPaymentRequest = {
            request: {} as any,
            merchantInfo,
            gameTokenData: { ...gameTokenData }
        };
        request.gameTokenData.freeSpinToken = "token";
        request.gameTokenData.freeBetsDisabled = true;
        const result = await this.paymentService.getBalances(request);

        expect(result).deep.equal({
            EUR: {
                main: 10.5
            }
        });
    }

    @test()
    public async winWithFreeSpinTokenAndFreeBetsDisabled() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const winWithFsToken = _.cloneDeep(winRequest);
        winWithFsToken.gameTokenData.freeSpinToken = "t";
        winWithFsToken.gameTokenData.freeBetsDisabled = true;

        const actualResponse = await this.paymentService.commitWinPayment(winWithFsToken);

        expect(actualResponse).to.be.deep.equal({
            main: 100.5,
            previousValue: 50.5
        });
    }

    @test()
    public async handleRoundAlreadyClosedOnWin() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));

        const winTransactionErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            AmountInGBP: null,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: [
                {
                    ErrorMessage: "Game round is closed!!!",
                    ErrorCode: b365ErrorCodes.GameRoundIsClosed
                }
            ]
        } as OperatorTransactionResponse;

        this.requestMock.post("http://operator/API/UK/Transaction", onCall(status500(winTransactionErrorResponse)));

        await this.paymentService
            .commitWinPayment(winRequest)
            .should.eventually.rejectedWith(RoundIsAlreadyClosedError);
    }

    @test()
    public async handleRoundAlreadyClosedOnWinRetry() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));

        const winTransactionErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            AmountInGBP: null,
            RealityCheck: {} as OperatorRealityCheckDetails,
            ErrorDetails: [
                {
                    ErrorMessage: "Game round is closed!!!",
                    ErrorCode: b365ErrorCodes.GameRoundIsClosed
                }
            ]
        } as OperatorTransactionResponse;

        this.requestMock.post("http://operator/API/UK/Transaction", onCall(status500(winTransactionErrorResponse)));

        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const getBalanceExpectedRequestQuery = {
            PrivateToken: "private_token",
            GamingId: "b365_player"
        } as OperatorGetBalanceRequest;

        const expectedResponse = {
            main: 10.5
        } as Balance;

        const request = {
            ...winRequest,
            request: {
                ...winRequest.request,
                retry: 1
            }
        };
        const actualResponse = await this.paymentService.commitWinPayment(request);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: {},
                headers: expectedRequestHeader,
                method: "GET",
                query: getBalanceExpectedRequestQuery,
                url: "http://operator/API/UK/Balance",
                keysToVoid: {
                    query: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(expectedResponse).to.be.deep.equal(actualResponse);
    }

    @test()
    public async doBonus() {
        this.createRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 0 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const startGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameCode: "GAME001",
            ChannelID: 222,
            ExternalGameRoundID: "1"
        } as OperatorStartGameRoundRequest;

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const betTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.PartnerFreeSpin,
            Amount: 0,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const bonusTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.PartnerFreeSpin,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_bonus",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 50.5
        } as Balance;

        const actualResponse = await this.paymentService.commitBonusPayment(bonusRequest);

        checkRequests(this.requestMock, [
            {
                body: startGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "StartTimeUTC", "UTCTimeStamp"]
                }
            },
            {
                body: betTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: bonusTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
        expect(this.createRoundStub.called).to.be.true;
    }

    @test()
    public async doBonusOpeningRoundWhenEventIdIsNotFirstItem() {
        this.createRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/GameRound",
            status200({
                GameRoundID: "game_round",
                ErrorDetails: []
            } as OperatorStartGameRoundResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 0 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedResponse = {
            main: 100.5,
            previousValue: 50.5
        } as Balance;

        const updatedBonusRequest = _.cloneDeep(bonusRequest);
        updatedBonusRequest.request.eventId = 1;
        updatedBonusRequest.request.operation = "deferred-payment";

        const actualResponse = await this.paymentService.commitBonusPayment(updatedBonusRequest);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
        expect(this.createRoundStub.called).to.be.true;
    }

    @test()
    public async doWinWithAwardFreeBets() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/Transaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                RealityCheck: {} as OperatorRealityCheckDetails,
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );
        this.requestMock.post(
            "http://operator/API/UK/FreeSpin/Award",
            status200({
                MessageID: 20002223,
                UTCTimeStamp: "2023-08-07T08:01:53.6040603Z",
                ErrorDetails: null
            })
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const winTransactionExpectedRequestBody = {
            PrivateToken: "private_token",
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: 50,
            CurrencyCode: "EUR",
            ExternalTransactionID: "v5R2nRkE_win",
            GameRoundID: "game_round"
        } as OperatorTransactionRequest;
        const FreeSpinCount = 5;
        const awardFreeBetsExpectedRequestBody = {
            PrivateToken: "private_token",
            ExternalTransactionID: `${winRequest.request.transactionId.publicId}${TransactionPostfix.AWARD_PROMO}`,
            GameCode: gameTokenData.gameCode,
            FreeSpinCount
        } as OperatorAwardPromRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 50.5
        } as Balance;

        // data to award free bets
        const winRequestWithExtraData: IntegrationPaymentRequest = JSON.parse(JSON.stringify(winRequest));
        winRequestWithExtraData.request.extraData = { assignPromotionToPlayer: true, freeSpinCount: FreeSpinCount };

        const actualResponse = await this.paymentService.commitWinPayment(winRequestWithExtraData);

        checkRequests(this.requestMock, [
            {
                body: winTransactionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/Transaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: awardFreeBetsExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/FreeSpin/Award",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }
}
