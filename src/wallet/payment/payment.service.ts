import { Injectable } from "@nestjs/common";
import {
    BalanceSupport,
    BonusPaymentSupport,
    CommitPaymentRequest,
    HttpGateway,
    JackpotPaymentSupport,
    SplitPaymentSupport
} from "@skywind-group/sw-integration-core";
import { Balance, Balances, RequireRefundBetError, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import { RoundService } from "@wallet/round/round.service";
import { BetHttpHandler } from "@wallet/payment/bet.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@wallet/payment/round.http.handler";
import { BalanceHttpHandler } from "@wallet/payment/balance.http.handler";
import { JackpotWinHttpHandler } from "@wallet/jackpot/jackpot.win.http.handler";
import { logging, measures } from "@skywind-group/sw-utils";
import { GetFreeSpinsInfoHandler } from "@wallet/freebet/free.bet.info.http.handler";
import measure = measures.measure;
import { BonusHttpHandler } from "@payment/bonus.http.handler";
import {
    OperatorActionType,
    OperatorEndGameRoundResponse,
    OperatorGetFreeSpinsResponse,
    OperatorStartGameRoundResponse
} from "@entities/operator.entities";
import { MerchantNonRetriableError, rollbackCondition, RoundIsAlreadyClosedError } from "@errors/sw.errors";
import { UNKNOWN_BALANCE } from "@entities/sw.entities";
import { roundIsAlreadyClosed, sumMajorUnits } from "@utils/common";
import { AwardPromoHttpHandler } from "@payment/awardPromo.http.handler";

const log = logging.logger("payment-service");

@Injectable()
export class PaymentService
    implements
        SplitPaymentSupport<IntegrationGameTokenData>,
        JackpotPaymentSupport<IntegrationGameTokenData>,
        BalanceSupport<IntegrationGameTokenData>,
        BonusPaymentSupport<IntegrationGameTokenData> {
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly roundService: RoundService,
        private readonly betHandler: BetHttpHandler,
        private readonly winHandler: WinHttpHandler,
        private readonly awardPromoHttpHandler: AwardPromoHttpHandler,
        private readonly startRoundHandler: StartRoundHttpHandler,
        private readonly endRoundHandler: EndRoundHttpHandler,
        private readonly balanceHandler: BalanceHttpHandler,
        private readonly jackpotWinHandler: JackpotWinHttpHandler,
        private readonly freeSpinsInfoHandler: GetFreeSpinsInfoHandler,
        private readonly bonusHandler: BonusHttpHandler
    ) {}

    /**
     *  Bonus (deferred) payment can be a part of a round besides bet/win or be included in separate one,
     *  so in both cases we open/close round.
     *  bet365 doesn't accept win without bet, so we always send zero-bet.
     *  zero-bet is not acceptable with Standard action type, so PartnerFreeSpin is used instead.
     */
    @measure({ name: "PaymentService.commitBonusPayment", isAsync: true })
    public async commitBonusPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        let balance: Balance;

        try {
            const operatorRoundId = await this.openAndGetRoundIfNeeded(req);
            await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId, actionType: OperatorActionType.PartnerFreeSpin },
                this.betHandler
            );
            balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId, actionType: OperatorActionType.PartnerFreeSpin },
                this.bonusHandler
            );
            await this.closeRoundIfNeeded(req, operatorRoundId);
            return balance;
        } catch (err) {
            if (err instanceof RoundIsAlreadyClosedError && req.request.retry) {
                log.warn(err, "Round is already closed. Fetch balance instead");
                return this.getBalance(req);
            }
            throw err;
        }
    }

    @measure({ name: "PaymentService.commitBetPayment", isAsync: true })
    public async commitBetPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            if (!req.request.bet) {
                log.info("Fetch balance instead of zero-bet");
                return this.getBalance(req);
            }
            const operatorRoundId = await this.openAndGetRoundIfNeeded(req);

            return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId },
                this.betHandler
            );
        } catch (err) {
            if (this.isRefundNeeded(req, err)) {
                log.info(err, "Refund operator's bet");
                throw new RequireRefundBetError();
            }

            // Bet has not been processed, there will be rollback
            log.info(err, "Rollback bet");
            throw err;
        }
    }

    @measure({ name: "PaymentService.commitWinPayment", isAsync: true })
    public async commitWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        let balance: Balance;
        const operatorRoundId = await this.roundService.getOperatorRoundId(req.request.roundPID);

        if (this.isWinCommittable(req)) {
            try {
                balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                    { ...req, operatorRoundId },
                    this.winHandler
                );
            } catch (err) {
                if (err instanceof RoundIsAlreadyClosedError && req.request.retry) {
                    log.warn(err, "Round is already closed. Fetch balance instead");
                    return this.getBalance(req);
                }
                throw err;
            }
            await this.addFreeBetInfoToBalance(req, balance);
        } else {
            log.info("Fetch balance instead of zero-win");
            balance = await this.getBalance(req);
        }

        // award free bets if it is the case
        if (this.isAwardPromotion(req)) {
            this.checkAwardPromotionData(req);
            await this.httpGateway.request<IntegrationPaymentRequest, Boolean>(
                { ...req, operatorRoundId },
                this.awardPromoHttpHandler
            );
        }

        await this.closeRoundIfNeeded(req, operatorRoundId);
        return balance;
    }

    @measure({ name: "PaymentService.commitJackpotWinPayment", isAsync: true })
    public async commitJackpotWinPayment(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        const operatorRoundId = await this.roundService.getOperatorRoundId(req.request.roundPID);
        let balance;

        if (!req.request.win) {
            log.info("Fetch balance instead of zero-jp-win");
            balance = await this.getBalance(req);
        } else {
            balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId },
                this.jackpotWinHandler
            );
            await this.addFreeBetInfoToBalance(req, balance);
        }
        // In case when jpWin appears during finalization
        if (!req.request.finalizationType) {
            await this.closeRoundIfNeeded(req, operatorRoundId);
        }

        return balance;
    }

    public async getBalances(req: IntegrationPaymentRequest): Promise<Balances> {
        const balance: Balance = await this.getBalance(req);
        return { [req.gameTokenData.currency]: balance };
    }

    public async getBalance(req: IntegrationPaymentRequest): Promise<Balance> {
        // Prevent getBalance request during finalization
        if (req.request?.finalizationType) {
            log.info("Skip fetching the balance during finalization payments. Return zero balance.");
            return UNKNOWN_BALANCE;
        }
        try {
            const balance: Balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                req,
                this.balanceHandler
            );
            await this.addFreeBetInfoToBalance(req, balance);
            return balance;
        } catch (err) {
            if (req.request?.offlineRetry) {
                // When request was invoked by offline-retry mechanism, we return zero-balance
                // Player doesn't see balance update
                // Zero balance will be in history, but we cannot do anything
                log.warn(err, "Balance request failed during offline retry. Return zero balance.");
                return UNKNOWN_BALANCE;
            }

            throw err;
        }
    }

    public async openAndGetRoundIfNeeded(req: IntegrationPaymentRequest): Promise<string> {
        if (req.request.eventId === 0 || req.request.operation === "deferred-payment") {
            const startRoundResponse = await this.httpGateway.request<
                IntegrationPaymentRequest,
                OperatorStartGameRoundResponse
            >(req, this.startRoundHandler);

            await this.roundService.setOperatorRoundId({
                operatorRoundId: startRoundResponse.GameRoundID,
                swRoundId: req.request.roundPID
            });

            return startRoundResponse.GameRoundID;
        }

        return this.roundService.getOperatorRoundId(req.request.roundPID);
    }

    public async closeRoundIfNeeded(req: IntegrationPaymentRequest, operatorRoundId: string) {
        if (!req.request.roundEnded) {
            return;
        }

        try {
            await this.httpGateway.request<IntegrationPaymentRequest, OperatorEndGameRoundResponse>(
                { ...req, operatorRoundId },
                this.endRoundHandler
            );
        } catch (err) {
            if (roundIsAlreadyClosed(err)) {
                return;
            }
            throw err;
        }
    }

    protected async addFreeBetInfoToBalance(req: IntegrationPaymentRequest, balance: Balance): Promise<void> {
        // When getBalance is invoked, request object is missing
        // Upon offline-retry no need to get free bet info. Player doesn't see update
        if (req.request?.offlineRetry || !req.gameTokenData.freeSpinToken || req.gameTokenData.freeBetsDisabled) {
            return;
        }

        const operatorGetFreeSpinsResponse = await this.httpGateway.request<
            IntegrationPaymentRequest,
            OperatorGetFreeSpinsResponse
        >(req, this.freeSpinsInfoHandler);
        const freeBetsCount: number = operatorGetFreeSpinsResponse.NumberOfSpinsRemaining;
        if (freeBetsCount) {
            balance.freeBets = { amount: freeBetsCount };
        }
    }

    protected isRefundNeeded(req: IntegrationPaymentRequest, error: SWError): boolean {
        return !!(
            // In case when multi bets (unfinished game when eventId > 0)
            (
                req.request.eventId === 0 &&
                // When win or jpWin broken, eventually we do offline-retry
                // Upon bet failure we do not refund once again due to unclear state of win and jpWin
                !req.request.offlineRetry &&
                rollbackCondition(error)
            )
        );
    }

    protected isWinCommittable(req: IntegrationPaymentRequest): boolean {
        return !!(
            req.request.roundEnded &&
            req.request.totalWin &&
            // Check ordinary win
            sumMajorUnits(req.request.totalWin, -req.request.totalJpWin || 0) > 0
        );
    }

    protected isAwardPromotion(req: IntegrationPaymentRequest): boolean {
        return req.request.extraData?.assignPromotionToPlayer && req.gameTokenData.isPromoInternal === false;
    }

    protected checkAwardPromotionData(req: IntegrationPaymentRequest): void {
        // check freeSpinCount
        if (req.request?.extraData?.freeSpinCount === undefined) {
            throw new MerchantNonRetriableError("freeSpinCount is missing, award can't be attribute to player");
        }
    }
}
