import { CommitPaymentRequest, Http<PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { sumMajorUnits } from "@utils/common";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { Injectable } from "@nestjs/common";

@Injectable()
export class BetHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    constructor(private readonly jackpotStatisticsMapper: JackpotStatisticsMapper) {
        super();
    }

    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const betRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Stake,
            ActionType: req.actionType || OperatorActionType.Standard,
            Amount: req.request.bet || 0,
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.BET,
            GameRoundID: req.operatorRoundId
        };
        if (req.request.totalJpContribution && req.request.jackpotDetails) {
            betRequest.JackpotDetails = this.jackpotStatisticsMapper.mapContributionStatistics(req.request);
        }

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: betRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorTransactionResponse>(response);
        const balance: Balance = { main: parsedResponse.Balance.TotalAmount };
        balance.previousValue = sumMajorUnits(balance.main, req.request.bet);
        return balance;
    }
}
