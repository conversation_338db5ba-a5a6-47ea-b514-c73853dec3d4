import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationPaymentRequest } from "@entities/integration.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import {
    OperatorStartGameRoundRequest,
    OperatorStartGameRoundResponse,
    OperatorEndGameRoundRequest,
    OperatorEndGameRoundResponse
} from "@entities/operator.entities";
import { generateNumber, createDateStringWithOperatorFormat } from "@utils/common";

export class StartRoundHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const startRoundRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            GameCode: req.gameTokenData.gameCode,
            ChannelID: req.gameTokenData.channelId,
            StartTimeUTC: createDateStringWithOperatorFormat(req.request.transactionId.timestamp),
            // Operator considers round as case insensitive value (qwerty == QWERTY)
            ExternalGameRoundID: req.request.roundId.toString()
        } as OperatorStartGameRoundRequest;

        return this.buildHttpRequest({
            path: "GameRound",
            method: "post",
            payload: startRoundRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<OperatorStartGameRoundResponse> {
        return this.parseHttpResponse<OperatorStartGameRoundResponse>(response);
    }
}

export class EndRoundHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    constructor() {
        super();
    }

    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const endRoundRequest = {
            PrivateToken: req.gameTokenData.privateToken,
            GameRoundID: req.operatorRoundId,
            EndTimeUTC: createDateStringWithOperatorFormat(req.request.transactionId.timestamp),
            MessageID: generateNumber(),
            UTCTimeStamp: createDateStringWithOperatorFormat()
        } as OperatorEndGameRoundRequest;

        return this.buildHttpRequest({
            path: "GameRound",
            method: "put",
            payload: endRoundRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<OperatorEndGameRoundResponse> {
        return this.parseHttpResponse<OperatorEndGameRoundResponse>(response);
    }
}
