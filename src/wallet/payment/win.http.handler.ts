import { CommitPaymentRequest, <PERSON>ttp<PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import { Base<PERSON>ttpHand<PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { sumMajorUnits, sumMajorUnitsInArray } from "@utils/common";

export class WinHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>> {
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const winRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: sumMajorUnits(req.request.totalWin, -req.request.totalJpWin || 0),
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.WIN,
            // JackpotDetails?: OperatorJackpotDetails[];
            GameRoundID: req.operatorRoundId
        };

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: winRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<Balance> {
        const parsedResponse = await this.parseHttpResponse<OperatorTransactionResponse>(response);
        const balance: Balance = { main: parsedResponse.Balance.TotalAmount };

        // MEMO: previousValue = current - (totalWin - totalJpWin)
        balance.previousValue = sumMajorUnitsInArray([
            balance.main,
            -req.request.totalWin || 0,
            req.request.totalJpWin
        ]);
        return balance;
    }
}
