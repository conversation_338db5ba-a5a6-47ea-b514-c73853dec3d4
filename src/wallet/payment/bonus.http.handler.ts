import { BaseHttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import { CommitBonusPaymentRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import * as superagent from "superagent";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { sumMajorUnitsInArray } from "@utils/common";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";

export class BonusHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitBonusPaymentRequest<IntegrationGameTokenData>> {
    public build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest<any>> {
        const bonusRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Return,
            ActionType: req.actionType || OperatorActionType.Standard,
            Amount: req.request.win,
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.BONUS,
            GameRoundID: req.operatorRoundId
        };

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: bonusRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(
        response: superagent.Response,
        req: CommitBonusPaymentRequest<IntegrationGameTokenData>
    ): Promise<any> {
        const parsedResponse = await this.parseHttpResponse<OperatorTransactionResponse>(response);
        const balance: Balance = { main: parsedResponse.Balance.TotalAmount };

        // MEMO: previousValue = current - win
        balance.previousValue = sumMajorUnitsInArray([balance.main, -req.request.win || 0]);
        return balance;
    }
}
