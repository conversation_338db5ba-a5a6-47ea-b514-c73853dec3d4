import { <PERSON>umn, CreateDateColumn, <PERSON><PERSON><PERSON>, UpdateDateColumn } from "typeorm";
import { RoundDto } from "@wallet/round/round.dto";

@Entity({
    name: "rounds"
})
export class RoundEntity {
    @Column({
        length: 255,
        nullable: false,
        name: "operator_round_id",
        primary: true
    })
    operatorRoundId: string;

    @Column({
        length: 255,
        nullable: false,
        name: "sw_round_id"
    })
    swRoundId: string;

    @CreateDateColumn({ name: "created_at" })
    createdAt: Date;

    @UpdateDateColumn({ name: "updated_at" })
    updatedAt: Date;

    public toInfo(): RoundDto {
        return {
            operatorRoundId: this.operatorRoundId,
            swRoundId: this.swRoundId
        };
    }
}
