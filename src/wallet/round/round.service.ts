import { Injectable, Inject } from "@nestjs/common";
import { MerchantAdapterAPIError } from "@skywind-group/sw-wallet-adapter-core";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { RoundRepository } from "@wallet/round/round.repository";
import { RoundEntity } from "@wallet/round/round.entity";
import { RoundDto } from "@wallet/round/round.dto";

@Injectable()
export class RoundService {
    constructor(private readonly roundRepository: RoundRepository) {}

    async getOperatorRoundId(swRoundId, skipError?: boolean): Promise<string> {
        try {
            const roundEntity: RoundEntity = await this.roundRepository.findOne({
                where: { swRoundId }
            });
            if (!roundEntity) {
                if (skipError) {
                    return undefined;
                }
                const error = { message: "Round service: round not found" } as SWError;
                return Promise.reject(new MerchantAdapterAPIError(null, error));
            }
            return roundEntity.operatorRoundId;
        } catch (error) {
            throw new MerchantAdapterAPIError(null, error);
        }
    }

    async setOperatorRoundId(data: RoundDto): Promise<RoundDto> {
        try {
            let roundEntity: RoundEntity = await this.roundRepository.findOne({
                where: { swRoundId: data.swRoundId }
            });
            if (roundEntity) {
                return roundEntity.toInfo();
            }
            const date = new Date();
            roundEntity = this.roundRepository.create({
                ...data,
                // default date is not set by typeorm during the insert operation
                createdAt: date,
                updatedAt: date
            });
            await this.roundRepository.insertOne(roundEntity);
            return roundEntity.toInfo();
        } catch (error) {
            throw new MerchantAdapterAPIError(null, error);
        }
    }
}
