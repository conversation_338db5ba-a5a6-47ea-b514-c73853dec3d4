import { EntityRepository, Repository } from "typeorm";
import { RoundEntity } from "@wallet/round/round.entity";

@EntityRepository(RoundEntity)
export class RoundRepository extends Repository<RoundEntity> {
    /**
     *  Inserts data to db directly. Input entity will be updated with parameters from db automatically.
     *  This method is a replacement for repository.save() which includes extra operations under the hood.
     */
    async insertOne(entity: RoundEntity): Promise<void> {
        await this.manager.createQueryBuilder().insert().into(RoundEntity).values(entity).execute();
    }
}
