import { Modu<PERSON> } from "@nestjs/common";
import { PaymentModule, StartGameModule } from "@skywind-group/sw-integration-core";
import config from "@config";
import { PaymentService } from "@wallet/payment/payment.service";
import { BalanceHttpHandler } from "@wallet/payment/balance.http.handler";
import { BetHttpHandler } from "@wallet/payment/bet.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@wallet/payment/round.http.handler";
import { JackpotWinHttpHandler } from "@wallet/jackpot/jackpot.win.http.handler";
import { GameTokenService } from "@wallet/start/gameToken.service";
import { GameUrlService } from "@wallet/start/gameUrl.service";
import { RoundService } from "@wallet/round/round.service";
import { RefundService } from "@wallet/refund/refund.service";
import { RefundHttpHandler } from "@wallet/refund/refund.http.handler";
import { LoginHttpHandler } from "@wallet/start/login.http.handler";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { GetFreeSpinsInfoHandler } from "@wallet/freebet/free.bet.info.http.handler";
import { FreeBetService } from "@wallet/freebet/free.bet.service";
import { FreeBetHttpHandler } from "@wallet/freebet/free.bet.http.handler";
import { FreeBetWinHttpHandler } from "@wallet/freebet/free.bet.win.http.handler";
import { BrokenGameService } from "@wallet/brokenGame/brokenGame.service";
import { TransferService } from "@wallet/transfer/transfer.service";
import { TransferInHttpHandler } from "@wallet/transfer/transferIn.handler";
import { TransferOutHttpHandler } from "@wallet/transfer/transferOut.handler";
import { BonusHttpHandler } from "@payment/bonus.http.handler";
import {
    RealityCheckHttpHandler,
    RealityCheckPlayerChoiceHttpHandler
} from "@wallet/regulation/regulation.http.handler";
import { RegulationService } from "@wallet/regulation/regulation.service";
import { TypeOrmModule } from "@nestjs/typeorm";
import { RoundRepository } from "@wallet/round/round.repository";
import { DbModule } from "@db/db.module";
import { AwardPromoHttpHandler } from "@payment/awardPromo.http.handler";

@Module({
    providers: [
        GameTokenService,
        GameUrlService,
        JackpotWinHttpHandler,
        RoundService,
        BetHttpHandler,
        WinHttpHandler,
        EndRoundHttpHandler,
        AwardPromoHttpHandler,
        StartRoundHttpHandler,
        RefundHttpHandler,
        BalanceHttpHandler,
        PaymentService,
        RefundService,
        LoginHttpHandler,
        JackpotStatisticsMapper,
        RealityCheckPlayerChoiceHttpHandler,
        RegulationService,
        GetFreeSpinsInfoHandler,
        FreeBetService,
        FreeBetHttpHandler,
        FreeBetWinHttpHandler,
        BrokenGameService,
        TransferService,
        TransferInHttpHandler,
        TransferOutHttpHandler,
        RealityCheckHttpHandler,
        BonusHttpHandler
    ],
    exports: [
        GameTokenService,
        GameUrlService,
        JackpotWinHttpHandler,
        RoundService,
        BetHttpHandler,
        WinHttpHandler,
        EndRoundHttpHandler,
        AwardPromoHttpHandler,
        StartRoundHttpHandler,
        RefundHttpHandler,
        BalanceHttpHandler,
        PaymentService,
        RefundService,
        LoginHttpHandler,
        RealityCheckPlayerChoiceHttpHandler,
        RegulationService,
        GetFreeSpinsInfoHandler,
        FreeBetService,
        FreeBetHttpHandler,
        FreeBetWinHttpHandler,
        BrokenGameService,
        TransferService,
        TransferInHttpHandler,
        TransferOutHttpHandler,
        RealityCheckHttpHandler,
        BonusHttpHandler
    ],
    imports: [
        StartGameModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                createGameToken: GameTokenService,
                createGameURL: GameUrlService,
                keepAlive: TransferService
            },
            [WalletModule]
        ),
        PaymentModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                payment: PaymentService,
                jackpotPayment: PaymentService,
                refund: RefundService,
                regulation: RegulationService,
                freeBetInfo: FreeBetService,
                freeBetPayment: FreeBetService,
                brokenGame: BrokenGameService,
                transfer: TransferService,
                bonusPayment: PaymentService
            },
            [WalletModule]
        ),
        TypeOrmModule.forFeature([RoundRepository]),
        DbModule
    ]
})
export class WalletModule {}
