import { Base<PERSON>ttp<PERSON><PERSON><PERSON>, HttpHandlerRequest, successResponses } from "@utils/baseHttp.handler";
import { CreateGameUrlRequest, HttpHand<PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationInitRequest } from "@entities/integration.entities";
import * as superagent from "superagent";
import { Injectable } from "@nestjs/common";
import { OperatorLoginRequest, OperatorLoginResponse } from "@entities/operator.entities";

@Injectable()
export class LoginHttpHandler extends BaseHttpHandler
    implements HttpHandler<CreateGameUrlRequest<IntegrationInitRequest>, OperatorLoginResponse> {
    public async build(req: CreateGameUrlRequest<IntegrationInitRequest>): Promise<HTTPOperatorRequest> {
        const loginRequest: OperatorLoginRequest = {
            ...this.buildBaseRequest(),
            PublicToken: req.initRequest.publicToken
        };
        return this.buildHttpRequest({
            path: "Login",
            method: "post",
            payload: loginRequest,
            jurisdiction: req.initRequest.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: false
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<OperatorLoginResponse> {
        return this.parseHttpResponse<OperatorLoginResponse>(response);
    }

    public async parseHttpResponse<OperatorLoginResponse>(
        response: superagent.Response
    ): Promise<OperatorLoginResponse> {
        if (!successResponses.includes(response.status)) {
            this.log.warn({ status: response.status, response: response.body });
            if (response.body && response.body.RelaunchUrl) {
                return response.body;
            }
        }
        return super.parseHttpResponse<OperatorLoginResponse>(response);
    }
}
