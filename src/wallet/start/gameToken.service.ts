import { CreateGameTokenRequest, CreateGameTokenSupport } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationStartGameTokenData } from "@entities/integration.entities";
import { MerchantGameTokenInfo } from "@skywind-group/sw-wallet-adapter-core";
import { Injectable } from "@nestjs/common";
import { measures } from "@skywind-group/sw-utils";
import measure = measures.measure;

@Injectable()
export class GameTokenService
    implements CreateGameTokenSupport<IntegrationStartGameTokenData, IntegrationGameTokenData> {
    @measure({ name: "GameTokenService.createGameTokenData", isAsync: true })
    public async createGameTokenData(
        req: CreateGameTokenRequest<IntegrationStartGameTokenData>
    ): Promise<MerchantGameTokenInfo<IntegrationGameTokenData>> {
        const gameTokenData: IntegrationGameTokenData = {
            playerCode: req.startGameToken.playerCode,
            gameCode: req.startGameToken.gameCode,
            brandId: req.startGameToken.brandId,
            country: req.startGameToken.country,
            currency: req.currency,
            playmode: req.startGameToken.playmode,
            merchantType: req.merchantInfo.type,
            merchantCode: req.merchantInfo.code,
            test: req.startGameToken.test,
            transferEnabled: req.transferEnabled,
            isPromoInternal: req.merchantInfo.params.isPromoInternal || false,
            privateToken: req.startGameToken.privateToken,
            jurisdiction: req.startGameToken.jurisdiction,
            channelId: req.startGameToken.channelId,
            envId: req.startGameToken.envId,
            freeSpinToken: req.startGameToken.freeSpinToken
        };
        if (req.startGameToken.disablePlayerPhantomFeatures) {
            gameTokenData.disablePlayerPhantomFeatures = req.startGameToken.disablePlayerPhantomFeatures;
        }
        return {
            gameTokenData,
            operatorSiteExternalCode: req.startGameToken.siteUrl
        };
    }
}
