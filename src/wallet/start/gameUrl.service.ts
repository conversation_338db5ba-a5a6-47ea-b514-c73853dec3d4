import { CreateGameUrlRequest, CreateGameUrlSupport, HttpGateway } from "@skywind-group/sw-integration-core";
import { IntegrationInitRequest, IntegrationStartGameTokenData } from "@entities/integration.entities";
import { SWJurisdiction, SWUrlInfo } from "@entities/sw.entities";
import { Injectable } from "@nestjs/common";
import { LoginHttpHandler } from "@wallet/start/login.http.handler";
import { OperatorLoginResponse } from "@entities/operator.entities";
import { measures } from "@skywind-group/sw-utils";
import { getDomain, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { generateNumber, getTwoLetterCountryCode, safeDecodeURIComponent } from "@utils/common";
import { ValidationError } from "@errors/sw.errors";
import measure = measures.measure;

@Injectable()
export class GameUrlService implements CreateGameUrlSupport<IntegrationInitRequest> {
    constructor(private readonly httpGateway: HttpGateway, private readonly loginHandler: LoginHttpHandler) {}

    @measure({ name: "GameTokenService.createGameUrl", isAsync: true })
    public async createGameUrl(req: CreateGameUrlRequest<IntegrationInitRequest>): Promise<SWUrlInfo> {
        let startGameToken: IntegrationStartGameTokenData;
        if (req.initRequest.playmode === PlayMode.FUN) {
            if (req.initRequest.jurisdiction !== SWJurisdiction.IT) {
                throw new ValidationError(`Fun mode is not supported for ${req.initRequest.jurisdiction}`);
            }
            startGameToken = this.createStartGameTokenDataForFunMode(req);
        } else {
            const loginResponse: OperatorLoginResponse = await this.httpGateway.request<
                CreateGameUrlRequest<IntegrationInitRequest>,
                OperatorLoginResponse
            >(req, this.loginHandler);
            startGameToken = this.createStartGameTokenData(req, loginResponse);
            if (loginResponse.ErrorDetails && loginResponse.ErrorDetails.length) {
                startGameToken.loginFailed = true;
            }
        }
        return {
            tokenData: startGameToken,
            urlParams: {
                language: req.initRequest.language,
                playmode: req.initRequest.playmode,
                lobby: req.initRequest.lobby,
                cashier: req.initRequest.cashier,
                history_url: req.initRequest.history_url,
                history2_url: req.initRequest.history_url,
                platform: req.initRequest.platform,
                rg_link_gioco: req.initRequest.giocoresponsabile,
                rg_link_adm_half: req.initRequest.ADM
            }
        };
    }

    private createStartGameTokenData(
        req: CreateGameUrlRequest<IntegrationInitRequest>,
        loginResponse: OperatorLoginResponse
    ): IntegrationStartGameTokenData {
        const token: IntegrationStartGameTokenData = {
            brandId: req.merchantInfo.brandId,
            merchantType: req.merchantInfo.type,
            merchantCode: req.merchantInfo.code,
            playerCode: loginResponse.GamingId,
            gameGroup: loginResponse.VIPLevel && loginResponse.VIPLevel.toString(),
            gameCode: req.gameCode,
            providerGameCode: req.providerGameCode,
            providerCode: req.providerCode,
            currency: loginResponse.CurrencyCode,
            country: getTwoLetterCountryCode(loginResponse.CountryCode),
            language: req.initRequest.language,
            playmode: req.initRequest.playmode,
            privateToken: loginResponse.PrivateToken,
            channelId: req.initRequest.channelId,
            jurisdiction: req.initRequest.jurisdiction,
            relaunchUrl: loginResponse.RelaunchUrl,
            freeSpinToken: req.initRequest.freeSpinToken,
            siteUrl: getDomain(safeDecodeURIComponent(req.initRequest.lobby))
        };

        if (loginResponse.AllowOffers === false) {
            token.disablePlayerPhantomFeatures = true;
        }

        if (loginResponse.WagerLimit) {
            token.dynamicMaxTotalBetLimit = loginResponse.WagerLimit;
        }

        return token;
    }

    private createStartGameTokenDataForFunMode(
        req: CreateGameUrlRequest<IntegrationInitRequest>
    ): IntegrationStartGameTokenData {
        return {
            brandId: req.merchantInfo.brandId,
            merchantType: req.merchantInfo.type,
            merchantCode: req.merchantInfo.code,
            playerCode: "FunModePlayer" + generateNumber(10),
            gameCode: req.gameCode,
            providerGameCode: req.providerGameCode,
            providerCode: req.providerCode,
            currency: "EUR",
            country: req.initRequest.jurisdiction.toUpperCase(),
            language: req.initRequest.language,
            playmode: req.initRequest.playmode,
            channelId: req.initRequest.channelId,
            jurisdiction: req.initRequest.jurisdiction,
            freeSpinToken: req.initRequest.freeSpinToken,
            siteUrl: getDomain(safeDecodeURIComponent(req.initRequest.lobby))
        };
    }
}
