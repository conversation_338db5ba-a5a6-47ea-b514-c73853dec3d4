import "module-alias/register";
import { suite, test } from "mocha-typescript";
import { Test } from "@nestjs/testing";
import config from "@config";
import { IntegrationInitRequest } from "@entities/integration.entities";
import { testing } from "@skywind-group/sw-utils";
import * as request from "superagent";
import { GameUrlService } from "@wallet/start/gameUrl.service";
import { LoginHttpHandler } from "@wallet/start/login.http.handler";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { expect, should, use } from "chai";
import { CreateGameUrlRequest } from "@skywind-group/sw-integration-core/lib/interfaces/startGame.service";
import { OperatorLoginResponse } from "@entities/operator.entities";
import { checkRequests } from "@utils/test/testing";
import * as chaiAsPromised from "chai-as-promised";
should();
use(chaiAsPromised);
import status200 = testing.status200;
import status400 = testing.status400;
import status500 = testing.status500;
import { MerchantInternalError } from "@errors/sw.errors";

/*
 https://static-de-gcpbucket.ss211208.com/game/api?
 GameCode=sw_8tr1qu&
 PublicToken=c226d329-124b-49bd-afc0-f4e8c0584965&
 ChannelID=10059&
 playmode=real&
 language=en&
 jurisdiction=UK
 &history_url=https%3a%2f%2fwww561.sagala-uat365.com%2fLobby%2fHistory%2fen-GB%2ffd0dc284-c96f-4b98-970f-3487efa73e58
 &lobby=https%3a%2f%2fwww561.sagala-uat365.com%2fLobby%2fBack%2fen-GB%2ffd0dc284-c96f-4b98-970f-3487efa73e58
 &platform=desktop
* */

const initRequest: IntegrationInitRequest = {
    jurisdiction: "UK",
    channelId: 111,
    merchantType: "b365",
    merchantCode: "b365",
    gameCode: "sw_al",
    playmode: "real" as PlayMode,
    publicToken: "I'm public token",
    language: "en",
    history_url: "https%3a%2f%2fwww561.sagala-uat365.com",
    lobby: "https%3a%2f%2fwww561.sagala-uat365.com/v1/version?hello=test",
    platform: "desktop",
    freeSpinToken: "fstocken"
};

const gameUrlRequest: CreateGameUrlRequest<IntegrationInitRequest> = {
    gameCode: initRequest.gameCode,
    merchantInfo: {
        type: "b365",
        code: "b365",
        brandId: 365,
        params: {
            username: "siroga",
            password: "666"
        }
    },
    providerCode: "sw",
    providerGameCode: "sw_al",
    initRequest
};

@suite()
class GameUrlServiceSpec {
    private gameUrlService: GameUrlService;
    private requestMock: testing.RequestMock = testing.requestMock(request);

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                GameUrlService,
                LoginHttpHandler,
                { useValue: new HttpGateway(config.http), provide: HttpGateway }
            ]
        }).compile();
        this.gameUrlService = moduleRef.get<GameUrlService>(GameUrlService);
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
    }

    @test()
    async happyPath() {
        this.requestMock.post(
            "https://gservices.b365uat.com:8110/API/UK/Login",
            status200({
                GamingId: "siroga",
                PrivateToken: "secret token",
                CurrencyCode: "BYN",
                CountryCode: "BY",
                VIPLevel: 666,
                RelaunchUrl: "http://tut.by",
                AllowOffers: false
            } as OperatorLoginResponse)
        );

        const result = await this.gameUrlService.createGameUrl(gameUrlRequest);
        const expectedRequest = {
            PublicToken: "I'm public token"
        };
        checkRequests(this.requestMock, [
            {
                body: expectedRequest,
                method: "POST",
                query: {},
                url: "https://gservices.b365uat.com:8110/API/UK/Login",
                headers: {
                    accept: "application/json",
                    "api-version": "3.0",
                    authorization: "Basic c2lyb2dhOjY2Ng=="
                },
                keysToVoid: { body: ["MessageID", "UTCTimeStamp"] }
            }
        ]);
        expect(result).deep.equal({
            tokenData: {
                brandId: 365,
                channelId: 111,
                country: "BY",
                currency: "BYN",
                gameCode: "sw_al",
                gameGroup: "666",
                jurisdiction: "UK",
                language: "en",
                merchantCode: "b365",
                merchantType: "b365",
                playerCode: "siroga",
                playmode: "real",
                privateToken: "secret token",
                providerCode: "sw",
                providerGameCode: "sw_al",
                relaunchUrl: "http://tut.by",
                freeSpinToken: "fstocken",
                siteUrl: "https://www561.sagala-uat365.com",
                disablePlayerPhantomFeatures: true
            },
            urlParams: {
                cashier: undefined,
                language: "en",
                playmode: "real",
                history_url: "https%3a%2f%2fwww561.sagala-uat365.com",
                history2_url: "https%3a%2f%2fwww561.sagala-uat365.com",
                lobby: "https%3a%2f%2fwww561.sagala-uat365.com/v1/version?hello=test",
                platform: "desktop",
                rg_link_adm_half: initRequest.ADM,
                rg_link_gioco: initRequest.giocoresponsabile
            }
        });
    }

    @test()
    async loginFailed() {
        this.requestMock.post(
            "https://gservices.b365uat.com:8110/API/UK/Login",
            status400({
                GamingId: null,
                PrivateToken: null,
                CurrencyCode: null,
                CountryCode: null,
                VIPLevel: null,
                ErrorDetails: [
                    {
                        ErrorCode: "InvalidToken",
                        ErrorMessage: "The token in the request has expired"
                    }
                ],
                RelaunchUrl: "http://tut.by"
            } as OperatorLoginResponse)
        );

        const result = await this.gameUrlService.createGameUrl(gameUrlRequest);
        expect(result).deep.equal({
            tokenData: {
                brandId: 365,
                channelId: 111,
                country: null,
                currency: null,
                gameCode: "sw_al",
                gameGroup: null,
                jurisdiction: "UK",
                language: "en",
                merchantCode: "b365",
                merchantType: "b365",
                playerCode: null,
                playmode: "real",
                privateToken: null,
                providerCode: "sw",
                providerGameCode: "sw_al",
                relaunchUrl: "http://tut.by",
                loginFailed: true,
                freeSpinToken: "fstocken",
                siteUrl: "https://www561.sagala-uat365.com"
            },
            urlParams: {
                cashier: undefined,
                language: "en",
                playmode: "real",
                history_url: "https%3a%2f%2fwww561.sagala-uat365.com",
                history2_url: "https%3a%2f%2fwww561.sagala-uat365.com",
                lobby: "https%3a%2f%2fwww561.sagala-uat365.com/v1/version?hello=test",
                platform: "desktop",
                rg_link_adm_half: initRequest.ADM,
                rg_link_gioco: initRequest.giocoresponsabile
            }
        });
    }

    @test()
    async errorHandling() {
        this.requestMock.post("https://gservices.b365uat.com:8110/API/UK/Login", status500("Internal server error"));

        await expect(this.gameUrlService.createGameUrl(gameUrlRequest)).to.be.rejectedWith(MerchantInternalError);
    }
}
