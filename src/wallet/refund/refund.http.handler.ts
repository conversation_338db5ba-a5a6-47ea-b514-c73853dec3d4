import { CommitPaymentRequest, <PERSON>ttp<PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import {
    OperatorCancelTransactionRequest,
    OperatorCancelTransactionResponse,
    OperatorEndGameRoundRequest,
    OperatorEndGameRoundResponse,
    TransactionPostfix
} from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { createDateStringWithOperatorFormat, sumMajorUnits } from "@utils/common";
import { SWJurisdiction } from "@entities/sw.entities";

export class RefundHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationPaymentRequest> {
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        let refundRequest: OperatorCancelTransactionRequest | OperatorEndGameRoundRequest;
        if (req.gameTokenData.jurisdiction === SWJurisdiction.IT) {
            refundRequest = {
                ...this.buildBaseRequest(),
                PrivateToken: req.gameTokenData.privateToken,
                EndTimeUTC: createDateStringWithOperatorFormat(req.request.transactionId.timestamp),
                GameRoundID: req.operatorRoundId
            };
        } else {
            refundRequest = {
                ...this.buildBaseRequest(),
                PrivateToken: req.gameTokenData.privateToken,
                ExternalTransactionIDToCancel: req.request.transactionId.publicId + TransactionPostfix.BET,
                GameRoundID: req.operatorRoundId
            };
        }

        return this.buildHttpRequest({
            path: req.gameTokenData.jurisdiction === SWJurisdiction.IT ? "CancelGameRound" : "CancelTransaction",
            method: "post",
            payload: refundRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo
        } as HttpHandlerRequest);
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<Balance> {
        if (req.gameTokenData.jurisdiction === SWJurisdiction.IT) {
            await this.parseHttpResponse<OperatorEndGameRoundResponse>(response);
            // At this point, the ADM session is closed, we cannot make a call to Balance because we will get an error
            // We return zero balance instead
            return { main: 0 };
        } else {
            const parsedResponse = await this.parseHttpResponse<OperatorCancelTransactionResponse>(response);
            return {
                main: parsedResponse.Balance.TotalAmount,
                previousValue: sumMajorUnits(parsedResponse.Balance.TotalAmount, -req.request.bet)
            };
        }
    }
}
