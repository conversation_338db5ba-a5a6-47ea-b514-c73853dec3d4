import "module-alias/register";
import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import * as chaiAsPromised from "chai-as-promised";
import * as request from "superagent";
import { testing } from "@skywind-group/sw-utils";
import { expect, should, use } from "chai";
import { Test } from "@nestjs/testing";
import { HttpGateway, HttpGatewayConfig, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { RoundService } from "@wallet/round/round.service";
import { RefundService } from "@wallet/refund/refund.service";
import { RefundHttpHandler } from "@wallet/refund/refund.http.handler";
import { Names } from "@names";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import { Balance, ConnectionError, MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorCancelTransactionRequest,
    OperatorCancelTransactionResponse,
    OperatorEndGameRoundRequest,
    OperatorEndGameRoundResponse,
    OperatorGetBalanceRequest,
    OperatorGetBalanceResponse,
    OperatorTransactionResponse
} from "@entities/operator.entities";
import { checkRequests } from "@utils/test/testing";
import * as swErrors from "@errors/sw.errors";
import { b365ErrorCodes } from "@errors/operator.errors";
import config from "@config";
import { EndRoundHttpHandler, StartRoundHttpHandler } from "@payment/round.http.handler";
import status200 = testing.status200;
import status500 = testing.status500;
import onCall = testing.onCall;
import { JackpotWinHttpHandler } from "@wallet/jackpot/jackpot.win.http.handler";
import { JackpotStatisticsMapper } from "@wallet/jackpot/jackpot.statistics.mapper";
import { BetHttpHandler } from "@payment/bet.http.handler";
import { WinHttpHandler } from "@payment/win.http.handler";
import { BalanceHttpHandler } from "@payment/balance.http.handler";
import { PaymentService } from "@payment/payment.service";
import { GetFreeSpinsInfoHandler } from "@wallet/freebet/free.bet.info.http.handler";
import { BonusHttpHandler } from "@payment/bonus.http.handler";
import { roundRepository } from "@utils/test/stubs";
import { AwardPromoHttpHandler } from "@payment/awardPromo.http.handler";

should();
use(chaiAsPromised);

const httpGatewayConfig = {
    operatorUrl: "http://operator/API",
    keepAlive: {
        socketActiveTTL: 60000,
        maxFreeSockets: 100,
        freeSocketKeepAliveTimeout: 12000
    }
} as HttpGatewayConfig;

const retryFakeFunc = () => {
    let attempt = config.operator.retryPolicy.attempts;
    return async (err: Error): Promise<boolean> =>
        (err instanceof ConnectionError || err instanceof swErrors.GeneralError) && --attempt > 0;
};

const gameTokenData = {
    merchantType: "b365",
    merchantCode: "b365__UK",
    sessionId: "session_1232434",
    country: "GB",
    currency: "EUR",
    playerCode: "b365_player",
    language: "en",
    brandId: 1111,
    gameCode: "GAME001",
    privateToken: "private_token",
    channelId: 222,
    jurisdiction: "UK",
    isPromoInternal: false
} as IntegrationGameTokenData;

const italyGameTokenData = {
    merchantType: "b365",
    merchantCode: "b365__IT",
    sessionId: "session_1232434",
    country: "GB",
    currency: "EUR",
    playerCode: "b365_player",
    language: "en",
    brandId: 1111,
    gameCode: "GAME001",
    privateToken: "private_token",
    channelId: 222,
    jurisdiction: "IT",
    isPromoInternal: false
} as IntegrationGameTokenData;

const merchantInfo = {
    brandId: 1111,
    type: "b365",
    code: "b365__UK",
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
} as MerchantInfo;

const italyMerchantInfo = {
    brandId: 1111,
    type: "b365",
    code: "b365__IT",
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
} as MerchantInfo;

const refundRequest = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        bet: 50,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: ""
    }
} as IntegrationPaymentRequest;

const italyRefundRequest = {
    gameTokenData: italyGameTokenData,
    merchantInfo: italyMerchantInfo,
    request: {
        gameToken: "token",
        bet: 50,
        roundId: "1",
        roundPID: "W4RkGRen",
        transactionId: {
            serialId: 2,
            publicId: "v5R2nRkE",
            timestamp: 1
        },
        ts: ""
    }
} as IntegrationPaymentRequest;

@suite()
class RefundServiceSpec {
    public requestMock: testing.RequestMock = testing.requestMock(request);
    public findRoundStub: sinon.SinonStub;
    public retryStub: sinon.SinonStub;
    public refundService: RefundService;
    public refundHttpHandler: RefundHttpHandler;
    public fakeRetry;

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                HttpGateway,
                {
                    provide: CoreNames.HttpGatewayConfig,
                    useValue: httpGatewayConfig
                },
                {
                    provide: Names.RoundRepository,
                    useValue: roundRepository
                },
                RoundService,
                JackpotWinHttpHandler,
                JackpotStatisticsMapper,
                BetHttpHandler,
                WinHttpHandler,
                EndRoundHttpHandler,
                AwardPromoHttpHandler,
                StartRoundHttpHandler,
                RefundHttpHandler,
                BalanceHttpHandler,
                PaymentService,
                RefundService,
                GetFreeSpinsInfoHandler,
                BonusHttpHandler
            ]
        }).compile();

        this.refundService = moduleRef.get<RefundService>(RefundService);
        this.refundHttpHandler = moduleRef.get<RefundHttpHandler>(RefundHttpHandler);
        this.findRoundStub = sinon.stub(roundRepository, "findOne");
        this.retryStub = sinon.stub(this.refundHttpHandler, "retry");
        this.fakeRetry = sinon.fake(retryFakeFunc());
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    @test()
    public async doRefundBet() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/UK/CancelTransaction",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                ErrorDetails: []
            } as OperatorCancelTransactionResponse)
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const refundBetExpectedRequestBody = {
            PrivateToken: "private_token",
            ExternalTransactionIDToCancel: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorCancelTransactionRequest;

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const expectedResponse = {
            main: 100.5,
            previousValue: 50.5
        } as Balance;

        const actualResponse = await this.refundService.refundBetPayment(refundRequest);

        checkRequests(this.requestMock, [
            {
                body: refundBetExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/CancelTransaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async doRetryRefundBet() {
        this.retryStub.returns(this.fakeRetry);
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));

        const refundErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            AmountInGBP: null,
            ErrorDetails: [
                {
                    ErrorMessage: "A technical error occurred when processing the request",
                    ErrorCode: b365ErrorCodes.TechnicalError
                }
            ]
        } as OperatorCancelTransactionResponse;
        const refundResponse = {
            Balance: { TotalAmount: 100 },
            CurrencyCode: "EUR",
            AmountInGBP: null,
            ErrorDetails: []
        } as OperatorCancelTransactionResponse;

        this.requestMock.post(
            "http://operator/API/UK/CancelTransaction",
            onCall(status500(refundErrorResponse)).onCall(status200(refundResponse))
        );

        this.requestMock.put(
            "http://operator/API/UK/GameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const refundBetExpectedRequestBody = {
            PrivateToken: "private_token",
            ExternalTransactionIDToCancel: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorCancelTransactionRequest;

        const endGameRoundExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const expectedResponse = {
            main: 100,
            previousValue: 50
        } as Balance;

        const actualResponse = await this.refundService.refundBetPayment(refundRequest);

        checkRequests(this.requestMock, [
            {
                body: refundBetExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/CancelTransaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: refundBetExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/CancelTransaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: endGameRoundExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "PUT",
                query: {},
                url: "http://operator/API/UK/GameRound",
                keysToVoid: {
                    body: ["MessageID", "EndTimeUTC", "UTCTimeStamp"]
                }
            }
        ]);
        expect(this.fakeRetry.callCount).to.equal(1);
        expect(expectedResponse).to.be.deep.equal(actualResponse);
    }

    @test()
    public async doRefundBetForItaly() {
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));
        this.requestMock.post(
            "http://operator/API/IGP/CancelGameRound",
            status200({
                ErrorDetails: []
            } as OperatorEndGameRoundResponse)
        );

        this.requestMock.get(
            "http://operator/API/IGP/Balance",
            status200({
                Balance: { TotalAmount: 100 },
                CurrencyCode: "EUR"
            } as OperatorGetBalanceResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "1.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const refundBetExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const expectedResponse = {
            main: 0
        } as Balance;

        const actualResponse = await this.refundService.refundBetPayment(italyRefundRequest);

        checkRequests(this.requestMock, [
            {
                body: refundBetExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/IGP/CancelGameRound",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp", "EndTimeUTC"]
                }
            }
        ]);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async doRetryRefundBetForItaly() {
        this.retryStub.returns(this.fakeRetry);
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));

        const refundErrorResponse = {
            ErrorDetails: [
                {
                    ErrorMessage: "A technical error occurred when processing the request",
                    ErrorCode: b365ErrorCodes.TechnicalError
                }
            ]
        } as OperatorEndGameRoundResponse;
        const refundResponse = {
            ErrorDetails: []
        } as OperatorEndGameRoundResponse;

        this.requestMock.post(
            "http://operator/API/IGP/CancelGameRound",
            onCall(status500(refundErrorResponse)).onCall(status200(refundResponse))
        );

        this.requestMock.get(
            "http://operator/API/IGP/Balance",
            status200({
                Balance: { TotalAmount: 100 },
                CurrencyCode: "EUR"
            } as OperatorGetBalanceResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "1.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const refundBetExpectedRequestBody = {
            PrivateToken: "private_token",
            GameRoundID: "game_round"
        } as OperatorEndGameRoundRequest;

        const expectedResponse = {
            main: 0
        } as Balance;

        const actualResponse = await this.refundService.refundBetPayment(italyRefundRequest);

        checkRequests(this.requestMock, [
            {
                body: refundBetExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/IGP/CancelGameRound",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp", "EndTimeUTC"]
                }
            },
            {
                body: refundBetExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/IGP/CancelGameRound",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp", "EndTimeUTC"]
                }
            }
        ]);
        expect(this.fakeRetry.callCount).to.equal(1);
        expect(actualResponse).to.be.deep.equal(expectedResponse);
    }

    @test()
    public async getBalanceOnError() {
        this.retryStub.returns(this.fakeRetry);
        this.findRoundStub.returns(Promise.resolve({ operatorRoundId: "game_round" }));

        const refundErrorResponse = {
            Balance: {},
            CurrencyCode: "EUR",
            AmountInGBP: null,
            ErrorDetails: [
                {
                    ErrorMessage: "Transaction not found",
                    ErrorCode: b365ErrorCodes.TransactionNotFound
                }
            ]
        } as OperatorCancelTransactionResponse;
        const refundResponse = {
            Balance: { TotalAmount: 100 },
            CurrencyCode: "EUR",
            AmountInGBP: null,
            ErrorDetails: []
        } as OperatorCancelTransactionResponse;

        this.requestMock.post(
            "http://operator/API/UK/CancelTransaction",
            onCall(status500(refundErrorResponse)).onCall(status200(refundResponse))
        );

        this.requestMock.get(
            "http://operator/API/UK/Balance",
            status200({
                Balance: { TotalAmount: 10.5 },
                CurrencyCode: "EUR",
                ErrorDetails: []
            } as OperatorTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const refundBetExpectedRequestBody = {
            PrivateToken: "private_token",
            ExternalTransactionIDToCancel: "v5R2nRkE_bet",
            GameRoundID: "game_round"
        } as OperatorCancelTransactionRequest;

        const getBalanceExpectedRequestQuery = {
            PrivateToken: "private_token",
            GamingId: "b365_player"
        } as OperatorGetBalanceRequest;

        const expectedResponse = {
            main: 10.5
        } as Balance;

        const actualResponse = await this.refundService.refundBetPayment(refundRequest);

        checkRequests(this.requestMock, [
            {
                body: refundBetExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/CancelTransaction",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            },
            {
                body: {},
                headers: expectedRequestHeader,
                method: "GET",
                query: getBalanceExpectedRequestQuery,
                url: "http://operator/API/UK/Balance",
                keysToVoid: {
                    query: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
        expect(this.fakeRetry.callCount).to.equal(1);
        expect(expectedResponse).to.be.deep.equal(actualResponse);
    }
}
