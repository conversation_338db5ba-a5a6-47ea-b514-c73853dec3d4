import { HttpGateway, RefundBetSupport } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import { Balance, SWError, MerchantAdapterAPIError } from "@skywind-group/sw-wallet-adapter-core";
import { measures, logging } from "@skywind-group/sw-utils";
import { RoundService } from "@wallet/round/round.service";
import { RefundHttpHandler } from "./refund.http.handler";
import { Injectable } from "@nestjs/common";
import { EndRoundHttpHandler } from "@payment/round.http.handler";
import { PaymentService } from "@payment/payment.service";
import * as swErrors from "@errors/sw.errors";
import measure = measures.measure;
import { OperatorEndGameRoundResponse } from "@entities/operator.entities";
import { SWJurisdiction } from "@entities/sw.entities";

const log = logging.logger("refund-service");

@Injectable()
export class RefundService implements RefundBetSupport<IntegrationGameTokenData> {
    constructor(
        private httpGateway: HttpGateway,
        private roundService: RoundService,
        private refundHandler: RefundHttpHandler,
        private endRoundHandler: EndRoundHttpHandler,
        private paymentService: PaymentService
    ) {}

    @measure({ name: "RefundService.refundBetPayment", isAsync: true })
    async refundBetPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            const operatorRoundId = await this.roundService.getOperatorRoundId(req.request.roundPID);
            const balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                { ...req, operatorRoundId },
                this.refundHandler
            );

            // For Italy, we do not need to close the round after refund because it is already closed
            if (req.gameTokenData.jurisdiction === SWJurisdiction.IT) {
                return balance;
            }

            // TODO separate error handling - we can fail on refund and will not close round on operator side
            await this.httpGateway.request<IntegrationPaymentRequest, OperatorEndGameRoundResponse>(
                { ...req, operatorRoundId },
                this.endRoundHandler
            );

            return balance;
        } catch (error) {
            if (this.isRefundCompleted(error)) {
                log.info(error, "Refund failed, but it was not required");
                return this.paymentService.getBalance(req);
            }

            throw error;
        }
    }

    private isRefundCompleted(error: SWError) {
        return (
            error instanceof swErrors.TransactionNotFound ||
            error instanceof swErrors.RoundNotFoundError ||
            error instanceof MerchantAdapterAPIError
        ); // in case when operator round id has not been stored
    }
}
