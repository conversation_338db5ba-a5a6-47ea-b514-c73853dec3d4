import "module-alias/register";
import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import * as chaiAsPromised from "chai-as-promised";
import * as request from "superagent";
import { testing } from "@skywind-group/sw-utils";
import { should, use } from "chai";
import { Test } from "@nestjs/testing";
import { HttpGateway, HttpGatewayConfig, Names as CoreNames } from "@skywind-group/sw-integration-core";
import { RegulationService } from "@wallet/regulation/regulation.service";
import { RealityCheckPlayerChoiceHttpHandler } from "@wallet/regulation/regulation.http.handler";
import { IntegrationGameTokenData, IntegrationRegulatoryActionRequest } from "@entities/integration.entities";
import { ConnectionError, MerchantInfo, PlayerRegulatoryActionsAtServer } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorCancelTransactionResponse,
    OperatorRealityCheckPlayerChoiceRequest,
    OperatorRealityCheckPlayerChoice
} from "@entities/operator.entities";
import { checkRequests } from "@utils/test/testing";
import * as swErrors from "@errors/sw.errors";
import config from "@config";
import status200 = testing.status200;

should();
use(chaiAsPromised);

const httpGatewayConfig = {
    operatorUrl: "http://operator/API",
    keepAlive: {
        socketActiveTTL: 60000,
        maxFreeSockets: 100,
        freeSocketKeepAliveTimeout: 12000
    }
} as HttpGatewayConfig;

const retryFakeFunc = () => {
    let attempt = config.operator.retryPolicy.attempts;
    return async (err: Error): Promise<boolean> =>
        (err instanceof ConnectionError || err instanceof swErrors.GeneralError) && --attempt > 0;
};

const gameTokenData = {
    merchantType: "b365",
    merchantCode: "b365__UK",
    sessionId: "session_1232434",
    country: "GB",
    currency: "EUR",
    playerCode: "b365_player",
    language: "en",
    brandId: 1111,
    gameCode: "GAME001",
    privateToken: "private_token",
    channelId: 222,
    jurisdiction: "UK",
    isPromoInternal: false
} as IntegrationGameTokenData;

const merchantInfo = {
    brandId: 1111,
    type: "b365",
    code: "b365__UK",
    params: {
        username: "b365_username",
        password: "b365_password",
        serverUrl: "http://operator/API"
    }
} as MerchantInfo;

const performRegulatoryAction = {
    gameTokenData,
    merchantInfo,
    request: {
        gameToken: "token",
        action: PlayerRegulatoryActionsAtServer.closeSession
    }
} as IntegrationRegulatoryActionRequest;

@suite()
class RegulatoryServiceSpec {
    public requestMock: testing.RequestMock = testing.requestMock(request);
    public retryStub: sinon.SinonStub;
    public regulationService: RegulationService;
    public realityCheckHttpHandler: RealityCheckPlayerChoiceHttpHandler;
    public fakeRetry;

    public async before() {
        const moduleRef = await Test.createTestingModule({
            providers: [
                HttpGateway,
                {
                    provide: CoreNames.HttpGatewayConfig,
                    useValue: httpGatewayConfig
                },
                RegulationService,
                RealityCheckPlayerChoiceHttpHandler
            ]
        }).compile();

        this.regulationService = moduleRef.get<RegulationService>(RegulationService);
        this.realityCheckHttpHandler = moduleRef.get<RealityCheckPlayerChoiceHttpHandler>(
            RealityCheckPlayerChoiceHttpHandler
        );
        this.retryStub = sinon.stub(this.realityCheckHttpHandler, "retry");
        this.fakeRetry = sinon.fake(retryFakeFunc());
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    @test()
    public async performRegulatoryAction() {
        this.requestMock.post(
            "http://operator/API/UK/RealityCheck",
            status200({
                Balance: { TotalAmount: 100.5 },
                CurrencyCode: "EUR",
                AmountInGBP: 10,
                ErrorDetails: []
            } as OperatorCancelTransactionResponse)
        );

        const expectedRequestHeader = {
            accept: "application/json",
            "api-version": "3.0",
            authorization: "Basic YjM2NV91c2VybmFtZTpiMzY1X3Bhc3N3b3Jk"
        };

        const performRegulatoryActionExpectedRequestBody = {
            PrivateToken: "private_token",
            Choice: OperatorRealityCheckPlayerChoice.Logout
        } as OperatorRealityCheckPlayerChoiceRequest;

        await this.regulationService.performRegulatoryAction(performRegulatoryAction);

        checkRequests(this.requestMock, [
            {
                body: performRegulatoryActionExpectedRequestBody,
                headers: expectedRequestHeader,
                method: "POST",
                query: {},
                url: "http://operator/API/UK/RealityCheck",
                keysToVoid: {
                    body: ["MessageID", "UTCTimeStamp"]
                }
            }
        ]);
    }
}
