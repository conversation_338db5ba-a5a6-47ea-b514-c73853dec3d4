import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationRealityCheckRequest, IntegrationRegulatoryActionRequest } from "@entities/integration.entities";
import {
    OperatorGetRealityCheckRequest,
    OperatorGetRealityCheckResponse,
    OperatorRealityCheckPlayerChoice,
    OperatorRealityCheckPlayerChoiceRequest,
    OperatorRealityCheckPlayerChoiceResponse
} from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { PlayerRegulatoryActionsAtServer } from "@skywind-group/sw-wallet-adapter-core";
import { buildRealityCheckError } from "@errors/operator.errors";

export class RealityCheckPlayerChoiceHttpHandler extends BaseHttpHandler
    implements HttpHandler<IntegrationRegulatoryActionRequest> {
    public async build(req: IntegrationRegulatoryActionRequest): Promise<HTTPOperatorRequest> {
        const refundRequest: OperatorRealityCheckPlayerChoiceRequest = {
            ...this.buildBaseRequest(),
            Choice:
                req.request.action === PlayerRegulatoryActionsAtServer.resetRealityCheck
                    ? OperatorRealityCheckPlayerChoice.Continue
                    : OperatorRealityCheckPlayerChoice.Logout,
            PrivateToken: req.gameTokenData.privateToken
        };

        return this.buildHttpRequest({
            path: "RealityCheck",
            method: "post",
            payload: refundRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: false
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<any> {
        return this.parseHttpResponse<OperatorRealityCheckPlayerChoiceResponse>(response);
    }
}

export class RealityCheckHttpHandler extends BaseHttpHandler implements HttpHandler<IntegrationRealityCheckRequest> {
    public async build(req: IntegrationRealityCheckRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);

        const refundRequest: OperatorGetRealityCheckRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: gameTokenData.privateToken
        };

        return this.buildHttpRequest({
            path: "RealityCheck",
            method: "get",
            payload: refundRequest,
            jurisdiction: gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: false
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<any> {
        await this.parseHttpResponse<OperatorGetRealityCheckResponse>(response);
        const error = buildRealityCheckError(response);
        if (error) {
            throw error;
        }
    }
}
