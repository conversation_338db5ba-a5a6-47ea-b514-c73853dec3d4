import { HttpGateway, RegulationSupport } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationRegulatoryActionRequest } from "@entities/integration.entities";
import { RealityCheckPlayerChoiceHttpHandler } from "./regulation.http.handler";
import { Injectable } from "@nestjs/common";
import { SWJurisdiction } from "@entities/sw.entities";

@Injectable()
export class RegulationService implements RegulationSupport<IntegrationGameTokenData> {
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly realityCheckPlayerChoiceHandler: RealityCheckPlayerChoiceHttpHandler
    ) {}

    async performRegulatoryAction(req: IntegrationRegulatoryActionRequest): Promise<any> {
        if (req.gameTokenData.jurisdiction === SWJurisdiction.IT) {
            return; // No regulatory action should be performed for Italy
        }
        if (req.gameTokenData.jurisdiction === SWJurisdiction.ES) {
            return; // No regulatory action should be performed for Spain
        }
        return this.httpGateway.request<IntegrationRegulatoryActionRequest, any>(
            req,
            this.realityCheckPlayerChoiceHandler
        );
    }
}
