import dotenv from 'dotenv';

dotenv.config();

export interface Config {
  server: {
    port: number;
    nodeEnv: string;
  };
  pces: {
    baseEngineUrl: string;
    vendorCode: string;
    genericId: string;
    genericSecretKey: string;
  };
  game: {
    gameUrl: string;
    demoGameUrl: string;
    freespinUrl?: string;
    roundDetailsUrl?: string;
  };
  security: {
    hashAlgorithm: string;
    tokenTimeoutMinutes: number;
  };
  logging: {
    level: string;
    file?: string;
  };
}

const requiredEnvVars = [
  'BASE_ENGINE_URL',
  'VENDOR_CODE',
  'GENERIC_ID',
  'GENERIC_SECRET_KEY',
  'GAME_URL',
  'DEMO_GAME_URL'
];

function validateConfig(): void {
  const missing = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

function getConfig(): Config {
  validateConfig();

  return {
    server: {
      port: parseInt(process.env.PORT || '3000', 10),
      nodeEnv: process.env.NODE_ENV || 'development',
    },
    pces: {
      baseEngineUrl: process.env.BASE_ENGINE_URL!,
      vendorCode: process.env.VENDOR_CODE!,
      genericId: process.env.GENERIC_ID!,
      genericSecretKey: process.env.GENERIC_SECRET_KEY!,
    },
    game: {
      gameUrl: process.env.GAME_URL!,
      demoGameUrl: process.env.DEMO_GAME_URL!,
      freespinUrl: process.env.FREESPIN_URL,
      roundDetailsUrl: process.env.ROUND_DETAILS_URL,
    },
    security: {
      hashAlgorithm: process.env.HASH_ALGORITHM || 'sha256',
      tokenTimeoutMinutes: parseInt(process.env.TOKEN_TIMEOUT_MINUTES || '5', 10),
    },
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      file: process.env.LOG_FILE,
    },
  };
}

export const config = getConfig();
