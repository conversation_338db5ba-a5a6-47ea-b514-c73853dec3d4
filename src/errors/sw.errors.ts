import {
    ConnectionError,
    escapeSomeHtmlChars,
    ExtraData,
    ExtraMessageImpl,
    LinkButtonImpl,
    MerchantAdapterAPIError,
    MrchExtraData,
    MrchExtraDataImpl,
    PlayerActionServerCallImpl,
    PlayerRegulatoryActionsAtServer,
    PopupButtonGameActions,
    PopupButtonImpl,
    RequireRefundBetError,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import { errors } from "@skywind-group/sw-utils";
import ERROR_LEVEL = errors.ERROR_LEVEL;

export class ValidationError extends SWError {
    constructor(message = "") {
        super(400, 40, message, ERROR_LEVEL.WARN, {}, { messages: [message] }); // to be translated
    }
}

export class InsufficientBalanceError extends SWError {
    constructor(message = "Player does not have sufficient balance to perform an operation") {
        super(400, 91, message);
    }
}

export class GameNotFoundError extends SWError {
    constructor(message = "Game not found") {
        super(404, 240, message);
    }
}

export class PlayerSessionExpiredError extends SWError {
    constructor(message = "Player session expired") {
        super(404, 733, message);
    }
}

export class MerchantInternalError extends SWError {
    constructor(reason?: string, responseStatus = 500, extraData?: ExtraData) {
        super(
            responseStatus,
            506,
            "Merchant internal error" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""),
            undefined,
            extraData
        );
        this.data.reason = reason || "";
    }
}

export class TransactionNotFound extends SWError {
    constructor(message = "Unknown transaction ID", public isResubmissionError: boolean) {
        super(404, 600, message);
    }
}

export class PlayerIsSuspended extends SWError {
    constructor(message = "Player is suspended") {
        super(400, 712, message);
    }
}

export class RGPlayerDepositLimitReachedError extends SWError {
    constructor(message = "Can't execute operation. Player has reached his deposit limit.") {
        super(403, 1502, message);
    }
}

export class AuthenticateFailedError extends SWError {
    constructor(message = "The credentials provided in the API are wrong") {
        super(403, 2004, message);
    }
}

export class RoundNotFoundError extends SWError {
    constructor(message = "Round not found") {
        super(404, 2005, message);
    }
}

export class PlayerNotFoundError extends SWError {
    constructor(message = "Player not found") {
        super(404, 2006, message);
    }
}

export class AmountNegativeError extends SWError {
    constructor(message = "The amount is equal or lower than zero") {
        super(400, 2008, message);
    }
}

export class RoundAlreadyEnded extends SWError {
    constructor(message = "The round is already ended") {
        super(400, 2009, message);
    }
}

export class ResponsibleGamingError extends SWError {
    constructor(message = "Responsible gaming error") {
        super(400, 2010, message);
    }
}

export class NotificationError extends SWError {
    constructor(message = "Notification error") {
        super(500, 2011, message);
    }
}

export class GeneralError extends SWError {
    constructor(message = "General error") {
        super(500, 2012, message);
    }
}

export class TimeoutError extends SWError {
    constructor(message = "Timeout error") {
        super(500, 2013, message);
    }
}

export class RollbackTransactionError extends SWError {
    constructor(message = "Rollback transaction") {
        super(400, 2014, message);
    }
}

export class EmptyResponseError extends SWError {
    constructor(message = "Empty response") {
        super(500, 2015, message);
    }
}

export class RoundIsAlreadyClosedError extends SWError {
    constructor(message = "Game round is closed") {
        super(400, 2016, message);
    }
}

export class IpLocationError extends SWError {
    constructor(message?: string) {
        super(500, 700, "Ip location lookup error: " + message);
    }
}

export class GameTokenExpired extends SWError {
    constructor() {
        super(400, 323, "Game token expired", ERROR_LEVEL.INFO);
    }
}

export class MerchantNonRetriableError extends SWError {
    constructor(reason?: string, extraData?: ExtraData) {
        super(
            400,
            762,
            "Merchant non-retriable error" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""),
            undefined,
            extraData
        );
        this.data.reason = reason || "";
    }
}

export class BrokenIntegrationError extends SWError {
    constructor() {
        super(501, 301, "Round is broken and should be force-finished");
    }
}

const DUPLICATE_KEY_ERROR_CODE: number = 23505;

export const rollbackCondition = (err: SWError): boolean =>
    err instanceof ConnectionError ||
    err instanceof TimeoutError ||
    err instanceof GeneralError ||
    err instanceof NotificationError ||
    err instanceof EmptyResponseError ||
    err instanceof MerchantInternalError ||
    (err instanceof MerchantAdapterAPIError && err.code === DUPLICATE_KEY_ERROR_CODE);

export const retryCondition = (err: SWError): boolean => rollbackCondition(err);

export const wrapWinError = (err: SWError): SWError =>
    !(err instanceof MerchantInternalError) ? new MerchantInternalError(err.message, err.responseStatus) : err;

export const wrapEndRoundError = (err: SWError): SWError => {
    if (err instanceof RoundAlreadyEnded || err instanceof MerchantInternalError) {
        return err;
    }

    return new MerchantInternalError(err.message, err.responseStatus);
};

export const wrapCancelBetError = (err: SWError): SWError =>
    retryCondition(err) || err instanceof PlayerSessionExpiredError
        ? new RequireRefundBetError(err.message)
        : new RollbackTransactionError(err.message);

export class RGRealityCheckError extends SWError {
    constructor(extraData: ExtraData) {
        super(
            403,
            1505,
            "We would like to notify you that you've already played for your reality check interval.",
            undefined,
            extraData
        );
    }
}

export class RGRealityCheckWithTimeError extends SWError {
    constructor(extraData: ExtraData, playerSessionTime: string = "") {
        super(
            403,
            1529,
            `Your session has now exceeded ${playerSessionTime}. We would like to notify you that you've already played for your reality check interval.`,
            undefined,
            extraData
        );
    }
}

export function getRealityCheckExtraData(historyUrl: string): MrchExtraData {
    const okButtonServerCall = PlayerActionServerCallImpl.create().setRegulatoryAction(
        PlayerRegulatoryActionsAtServer.resetRealityCheck
    );

    const stopButtonServerCall = PlayerActionServerCallImpl.create().setRegulatoryAction(
        PlayerRegulatoryActionsAtServer.closeSession
    );

    const keepPlayingButton = PopupButtonImpl.create()
        .setLabel("Keep Playing")
        .setServerCall(okButtonServerCall)
        .setGameAction(PopupButtonGameActions.continue)
        .setTranslate(true);

    const stopButton = PopupButtonImpl.create()
        .setLabel("Quit")
        .setServerCall(stopButtonServerCall)
        .setGameAction(PopupButtonGameActions.lobby)
        .setTranslate(true);
    const historyButton = getHistoryButton(historyUrl);

    const UKRealityCheckExtraMessage = ExtraMessageImpl.create()
        .setButtons([keepPlayingButton, stopButton, historyButton])
        .setTranslate(true)
        .setMessageTitle("");

    return MrchExtraDataImpl.create().addExtraMessage(UKRealityCheckExtraMessage).setUseServerMessage(false);
}

function getHistoryButton(historyLink: string): PopupButtonImpl {
    if (historyLink) {
        return LinkButtonImpl.create().setLink(historyLink).setLabel("Show game history").setTranslate(true);
    } else {
        return PopupButtonImpl.create()
            .setGameAction(PopupButtonGameActions.gameHistory)
            .setLabel("Show game history")
            .setTranslate(true)
            .setNoClose(true);
    }
}
