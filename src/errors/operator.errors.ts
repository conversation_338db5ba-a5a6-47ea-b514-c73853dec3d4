import { OperatorError, OperatorRealityCheckDetails, OperatorRealityCheckStatus } from "@entities/operator.entities";
import {
    AnotherGameInProgress,
    CannotCompletePayment,
    InsufficientFreebet,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import * as swErrors from "@errors/sw.errors";
import { GameTokenExpired, getRealityCheckExtraData, RoundIsAlreadyClosedError } from "@errors/sw.errors";
import * as superagent from "superagent";
import { safeGet } from "@skywind-group/sw-utils";
import { convertSecondsToTime } from "@utils/common";

export enum b365ErrorCodes {
    TechnicalError = "TechnicalError",
    AuthenticationFailed = "AuthenticationFailed",
    WrongTokenType = "WrongTokenType",
    TokenIsForDifferentPartner = "TokenIsForDifferentPartner",
    TransactionNotFound = "TransactionNotFound",
    InsufficientFunds = "InsufficientFunds",
    GamingLimits = "GamingLimits",
    AccountBlocked = "AccountBlocked",
    UserCannotAccessThisRegulatoryZone = "UserCannotAccessThisRegulatoryZone",
    UsernameDoesNotMatchToken = "UsernameDoesNotMatchToken",
    GamingIdDoesNotMatchToken = "GamingIdDoesNotMatchToken",
    GameRoundIsClosed = "GameRoundIsClosed",
    GameRoundIsForDifferentUser = "GameRoundIsForDifferentUser",
    ReservationIsForDifferentUser = "ReservationIsForDifferentUser",
    ReservationHasBeenReleased = "ReservationHasBeenReleased",
    ReservationHasBeenCancelled = "ReservationHasBeenCancelled",
    ReservationNotFound = "ReservationNotFound",
    RealityCheckPending = "RealityCheckPending",
    RealityCheckNotPending = "RealityCheckNotPending",
    PromotionIsClosed = "PromotionIsClosed",
    NoFreeSpinsRemaining = "NoFreeSpinsRemaining",
    GameNotAllowed = "GameNotAllowed",
    TransferAmountDifferentToConfiguration = "TransferAmountDifferentToConfiguration",
    GameRoundTransactionMismatch = "GameRoundTransactionMismatch",
    GoldenChipNotAllowed = "GoldenChipNotAllowed",
    GoldenChipLimitExceeded = "GoldenChipLimitExceeded",
    GoldenChipNotStaked = "GoldenChipNotStaked",
    GoldenChipRequired = "GoldenChipRequired",
    TransactionLimitExceeded = "TransactionLimitExceeded",
    WinLossDetailsNotFound = "WinLossDetailsNotFound",
    ExceededGamePlayDuration = "ExceededGamePlayDuration",
    UnknownCurrency = "UnknownCurrency",
    UnknownGameCode = "UnknownGameCode",
    UnknownChannelID = "UnknownChannelID",
    UnknownGameRoundID = "UnknownGameRoundID",
    UnknownFreeSpinToken = "UnknownFreeSpinToken",
    UnknownGoldenChipToken = "UnknownGoldenChipToken",
    HeaderRequired = "HeaderRequired",
    ParameterRequired = "ParameterRequired",
    InvalidRequest = "InvalidRequest",
    InvalidToken = "InvalidToken",
    InvalidAmount = "InvalidAmount",
    InvalidAAMSSessionID = "InvalidAAMSSessionID",
    InvalidAAMSParticipationID = "InvalidAAMSParticipationID",
    InvalidJackpotCode = "InvalidJackpotCode",
    InvalidJackpotNetworkId = "InvalidJackpotNetworkId",
    InvalidTotalBetAmount = "InvalidTotalBetAmount",
    InvalidTotalWinAmount = "InvalidTotalWinAmount",
    InvalidGamePhases = "InvalidGamePhases",
    InvalidMessageID = "InvalidMessageID",
    InvalidGameCode = "InvalidGameCode",
    InvalidTimeStamp = "InvalidTimeStamp",
    InvalidStartTime = "InvalidStartTime",
    InvalidEndTime = "InvalidEndTime",
    InvalidUsername = "InvalidUsername",
    InvalidTransactionID = "InvalidTransactionID",
    InvalidChannelID = "InvalidChannelID",
    InvalidGameRoundID = "InvalidGameRoundID",
    InvalidCurrencyCode = "InvalidCurrencyCode",
    InvalidTransactionType = "InvalidTransactionType",
    InvalidActionType = "InvalidActionType",
    InvalidRealityCheckChoice = "InvalidRealityCheckChoice",
    InvalidFreeSpinToken = "InvalidFreeSpinToken",
    InvalidGoldenChipToken = "InvalidGoldenChipToken",
    InvalidQuantity = "InvalidQuantity",
    InvalidValue = "InvalidValue",
    InvalidGeolocation = "InvalidGeolocation",
    PrizeCreationFailed = "PrizeCreationFailed",
    PrizeCancellationFailed = "PrizeCancellationFailed",
    PrizeNotFound = "PrizeNotFound",
    AnotherGameInProgress = "AnotherGameInProgress",
    TransactionFailed = "TransactionFailed",
    IgpSessionDoesNotExist = "IgpSessionDoesNotExist",
    IgpSessionIsNotActive = "IgpSessionIsNotActive"
}

export enum b365ErrorMessages {
    AuthenticationFailed = "The supplied partner username and password do not match the expected values",
    InvalidToken = "The token in the request is invalid."
}

export class b365Error extends Error {
    constructor(public ErrorCode: string, public ErrorMessage: string = "") {
        super(ErrorMessage);
    }
}

export class b365TechnicalError extends b365Error {
    constructor() {
        super("TechnicalError", "A technical error occurred when processing the request");
    }
}

export class b365InvalidAmount extends b365Error {
    constructor() {
        super("InvalidAmount", "The amount supplied is zero or negative.");
    }
}

export class b365InvalidTransactionType extends b365Error {
    constructor() {
        super("InvalidTransactionType", "The supplied transaction type is invalid.");
    }
}

export class b365UnknownFreeSpinToken extends b365Error {
    constructor() {
        super(b365ErrorCodes.UnknownFreeSpinToken, "The supplied transaction type is invalid.");
    }
}

export class b365NoFreeSpinsRemaining extends b365Error {
    constructor() {
        super("NoFreeSpinsRemaining", "The user has no free spins remaining");
    }
}

export class b365PromotionIsClosed extends b365Error {
    constructor() {
        super("PromotionIsClosed", "The promotion is closed");
    }
}

export class b365InsufficientFunds extends b365Error {
    constructor() {
        super("InsufficientFunds", "The player does not have sufficient funds for the bet");
    }
}

export class b365TransactionNotFound extends b365Error {
    constructor() {
        super("TransactionNotFound", "A transaction was not found");
    }
}

export function mapOperatorToSWError(
    { ErrorCode, ErrorMessage }: OperatorError,
    response?: superagent.Response
): SWError {
    switch (ErrorCode) {
        case b365ErrorCodes.TechnicalError:
            return new swErrors.GeneralError();
        case b365ErrorCodes.AuthenticationFailed:
        case b365ErrorCodes.WrongTokenType:
        case b365ErrorCodes.TokenIsForDifferentPartner:
            return new swErrors.AuthenticateFailedError();
        case b365ErrorCodes.TransactionNotFound:
            const resubmissionError = ErrorMessage && ErrorMessage.indexOf("Resubmission error") >= 0;
            return new swErrors.TransactionNotFound(undefined, resubmissionError);
        case b365ErrorCodes.InsufficientFunds:
            return new swErrors.InsufficientBalanceError();
        case b365ErrorCodes.GamingLimits:
            return new swErrors.RGPlayerDepositLimitReachedError();
        case b365ErrorCodes.AccountBlocked:
            return new swErrors.PlayerIsSuspended();
        case b365ErrorCodes.InvalidAmount:
            return new swErrors.AmountNegativeError();
        case b365ErrorCodes.NoFreeSpinsRemaining:
            return new InsufficientFreebet();
        case b365ErrorCodes.UnknownGameRoundID:
        case b365ErrorCodes.InvalidGameRoundID:
        case b365ErrorCodes.IgpSessionDoesNotExist:
            return new swErrors.RoundNotFoundError();
        case b365ErrorCodes.RealityCheckPending:
            return buildRealityCheckError(response);
        case b365ErrorCodes.UserCannotAccessThisRegulatoryZone:
        case b365ErrorCodes.UsernameDoesNotMatchToken:
        case b365ErrorCodes.GamingIdDoesNotMatchToken:
        case b365ErrorCodes.GameRoundIsForDifferentUser:
        case b365ErrorCodes.ReservationIsForDifferentUser:
        case b365ErrorCodes.ReservationHasBeenReleased:
        case b365ErrorCodes.ReservationHasBeenCancelled:
        case b365ErrorCodes.ReservationNotFound:
        case b365ErrorCodes.RealityCheckNotPending:
        case b365ErrorCodes.PromotionIsClosed:
        case b365ErrorCodes.GameNotAllowed:
        case b365ErrorCodes.TransferAmountDifferentToConfiguration:
        case b365ErrorCodes.GameRoundTransactionMismatch:
        case b365ErrorCodes.GoldenChipNotAllowed:
        case b365ErrorCodes.GoldenChipLimitExceeded:
        case b365ErrorCodes.GoldenChipNotStaked:
        case b365ErrorCodes.GoldenChipRequired:
        case b365ErrorCodes.WinLossDetailsNotFound:
        case b365ErrorCodes.ExceededGamePlayDuration:
        case b365ErrorCodes.UnknownCurrency:
        case b365ErrorCodes.UnknownGameCode:
        case b365ErrorCodes.UnknownChannelID:
        case b365ErrorCodes.UnknownFreeSpinToken:
        case b365ErrorCodes.UnknownGoldenChipToken:
        case b365ErrorCodes.HeaderRequired:
        case b365ErrorCodes.ParameterRequired:
        case b365ErrorCodes.InvalidRequest:
        case b365ErrorCodes.InvalidAAMSSessionID:
        case b365ErrorCodes.InvalidAAMSParticipationID:
        case b365ErrorCodes.InvalidJackpotCode:
        case b365ErrorCodes.InvalidJackpotNetworkId:
        case b365ErrorCodes.InvalidTotalBetAmount:
        case b365ErrorCodes.InvalidTotalWinAmount:
        case b365ErrorCodes.InvalidGamePhases:
        case b365ErrorCodes.InvalidMessageID:
        case b365ErrorCodes.InvalidGameCode:
        case b365ErrorCodes.InvalidTimeStamp:
        case b365ErrorCodes.InvalidStartTime:
        case b365ErrorCodes.InvalidEndTime:
        case b365ErrorCodes.InvalidUsername:
        case b365ErrorCodes.InvalidTransactionID:
        case b365ErrorCodes.InvalidChannelID:
        case b365ErrorCodes.InvalidCurrencyCode:
        case b365ErrorCodes.InvalidTransactionType:
        case b365ErrorCodes.InvalidActionType:
        case b365ErrorCodes.InvalidRealityCheckChoice:
        case b365ErrorCodes.InvalidFreeSpinToken:
        case b365ErrorCodes.InvalidGoldenChipToken:
        case b365ErrorCodes.InvalidQuantity:
        case b365ErrorCodes.InvalidValue:
        case b365ErrorCodes.InvalidGeolocation:
        case b365ErrorCodes.PrizeCreationFailed:
        case b365ErrorCodes.PrizeCancellationFailed:
        case b365ErrorCodes.PrizeNotFound:
        case b365ErrorCodes.TransactionFailed:
            return new swErrors.ValidationError(ErrorMessage);
        case b365ErrorCodes.TransactionLimitExceeded:
            return new swErrors.BrokenIntegrationError();
        case b365ErrorCodes.InvalidToken:
            return new GameTokenExpired();
        case b365ErrorCodes.GameRoundIsClosed:
            return new RoundIsAlreadyClosedError();
        case b365ErrorCodes.AnotherGameInProgress:
            return new AnotherGameInProgress();
        case b365ErrorCodes.IgpSessionIsNotActive:
            return new CannotCompletePayment(ErrorMessage);
        default:
            return new swErrors.MerchantInternalError(ErrorMessage);
    }
}

export function buildRealityCheckError(response: superagent.Response): SWError {
    const realityCheckDetails = safeGet(response, "body", "RealityCheck") as OperatorRealityCheckDetails;

    if (!realityCheckDetails) {
        return;
    }

    if (realityCheckDetails.RealityCheckStatus !== OperatorRealityCheckStatus.Due) {
        return;
    }

    const historyUrl = (realityCheckDetails && realityCheckDetails.AccountHistoryUrl) || undefined;
    const extraData = getRealityCheckExtraData(historyUrl);

    if (realityCheckDetails && realityCheckDetails.TimeSinceLogin) {
        const timeSinceLogin = convertSecondsToTime(realityCheckDetails.TimeSinceLogin);
        return new swErrors.RGRealityCheckWithTimeError(
            {
                ...extraData,
                sessionData: { timeSinceLogin } // TODO: add sessionData to MrchExtraDataImpl
            },
            timeSinceLogin
        );
    }

    return new swErrors.RGRealityCheckError(extraData);
}
