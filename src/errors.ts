import { SWError } from "@skywind-group/sw-wallet-adapter-core";

export class OperationForbidden extends SWError {
    constructor(reason = "") {
        super(403, 206, reason ? `Forbidden. ${reason}` : "Forbidden");
        this.data.reason = reason;
    }
}

export class OperatorTransientError extends SWError {
    constructor(message?: string, code = 2002) {
        super(500, code, message || " Operator communication error");
    }
}
