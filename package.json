{"name": "@skywind-group/sw-integration-core", "version": "1.0.83", "description": "Common wallet integration module", "author": "<PERSON>", "license": "Skywindgroup", "typings": "./lib/index.d.ts", "main": "./lib/index.js", "scripts": {"format": "prettier --write \"**/*.ts\"", "lint": "eslint \"src/**/*.ts\" --fix", "test": "mocha -r ts-node/register --exit src/**/*.spec.ts", "test:coverage": "nyc --reporter=lcov --reporter=lcovonly --reporter=text npm run test", "clean": "rm -rf lib", "compile": "tsc -p tsconfig.json", "compile:watch": "tsc -p tsconfig.json --watch", "build": "npm run clean && npm run compile", "precommit": "lint-staged", "prepublish:npm": "npm run build", "prepublish": "npm run build", "publish:npm": "npm publish", "publish:local": "npm run compile && npm link", "publish:pack": "npm run compile && npm pack"}, "devDependencies": {"@commitlint/cli": "^9.1.1", "@commitlint/config-angular": "9.1.1", "@nestjs/common": "7.4.2", "@nestjs/core": "7.4.2", "@nestjs/platform-fastify": "7.0.9", "@nestjs/testing": "7.4.2", "@skywind-group/sw-utils": "^1.0.3", "@skywind-group/sw-wallet-adapter-core": "0.6.126", "@types/chai": "^4.2.12", "@types/chai-as-promised": "^7.1.3", "@types/mocha": "^7.0.2", "@types/node": "7.10.9", "@types/request-ip": "^0.0.38", "@types/sinon": "^9.0.4", "@types/superagent": "^4.1.8", "@typescript-eslint/eslint-plugin": "3.8.0", "@typescript-eslint/parser": "3.8.0", "bole": "3.0.2", "bole-console": "0.1.10", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "commitlint-config-jira": "^1.4.1", "commitlint-plugin-jira-rules": "^1.4.0", "eslint": "7.6.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-import": "2.22.0", "fastify": "2.14.0", "husky": "4.2.5", "lint-staged": "10.2.11", "mocha": "^7.1.2", "mocha-typescript": "^1.1.17", "npm-install-peers": "^1.2.1", "nyc": "^15.0.1", "prettier": "2.0.5", "reflect-metadata": "0.1.13", "release-it": "13.6.6", "sinon": "^9.0.2", "superagent-mocker": "^0.5.2", "supertest": "^4.0.2", "ts-node": "^8.10.2", "typescript": "3.9.7"}, "peerDependencies": {"@skywind-group/sw-utils": "^1.0.3", "@skywind-group/sw-wallet-adapter-core": "^0.6.126"}, "lint-staged": {"*.ts": ["prettier --write"]}, "husky": {"hooks": {"commit-msg": "commitlint -c .commitlintrc.json -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "dependencies": {"@nestjs/serve-static": "^2.1.3", "@nestjs/swagger": "^4.6.0", "deepmerge": "^4.2.2", "fastify-swagger": "^2.1.1", "random-number": "0.0.9", "request-ip": "^3.3.0", "superagent": "^5.3.1", "uuid": "^3.3.2"}}