{"name": "sw-integration-b365", "version": "5.48.0", "description": "", "author": "<PERSON> (<EMAIL>) and <PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"clean": "rm -rf ./lib", "build": "npm run version && nest build  ", "format": "prettier --write \"**/*.ts\"", "build:dev": "nest build --watch", "start:launcher": "nest start launcher", "start:launcher:dev": "MEASURES_PROVIDER=prometheus nest start launcher --watch", "start:launcher:debug": "MEASURES_PROVIDER=prometheus nest start launcher --debug --watch", "start:operator": "nest start operator", "start:operator:dev": "MEASURES_PROVIDER=prometheus nest start operator --watch", "start:operator:debug": "MEASURES_PROVIDER=prometheus nest start operator --debug --watch", "start:wallet": "nest start wallet", "start:wallet:dev": "MEASURES_PROVIDER=prometheus nest start wallet --watch", "start:wallet:debug": "MEASURES_PROVIDER=prometheus nest start wallet --debug --watch", "start:mock": "nest start mock", "start:mock:dev": "MEASURES_PROVIDER=prometheus nest start mock --watch", "start:mock:debug": "MEASURES_PROVIDER=prometheus nest start mock --debug --watch", "lint": "eslint \"{src,apps,libs}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs}/**/*.ts\" --fix", "test": "mocha -r ts-node/register --exit src/**/**/*.spec.ts src/**/*spec.ts", "test:coverage": "nyc --reporter=lcov --reporter=lcovonly --reporter=text npm run test", "version": "mkdir -p lib && echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/version", "report-critical-files": "ts-node -r tsconfig-paths/register src/operator/reportCriticalFiles.ts"}, "dependencies": {"@nestjs/common": "7.4.2", "@nestjs/core": "7.4.2", "@nestjs/platform-fastify": "7.0.9", "@nestjs/schedule": "^1.1.0", "@nestjs/typeorm": "7.1.5", "@skywind-group/sw-currency-exchange": "1.5.3", "@skywind-group/sw-integration-core": "1.0.82", "@skywind-group/sw-utils": "1.0.4", "@skywind-group/sw-wallet-adapter-core": "0.6.194", "bole": "3.0.2", "bole-console": "0.1.10", "class-transformer": "^0.3.1", "class-validator": "^0.12.2", "country-code-lookup": "0.0.20", "fastify": "^2.14.1", "module-alias": "^2.2.2", "node-cache": "^5.1.2", "pg": "^8.3.3", "random-number": "0.0.9", "typeorm": "0.2.45"}, "devDependencies": {"@commitlint/cli": "^9.1.1", "@commitlint/config-angular": "9.1.1", "@nestjs/cli": "^7.0.0", "@nestjs/schematics": "^7.0.0", "@nestjs/testing": "7.4.2", "@types/chai": "^4.2.12", "@types/chai-as-promised": "^7.1.0", "@types/cron": "^1.7.3", "@types/lodash": "4.14.161", "@types/mocha": "^7.0.2", "@types/node": "7.10.9", "@types/random-number": "0.0.0", "@types/sinon": "^9.0.4", "@types/superagent": "^4.1.9", "@types/supertest": "^2.0.8", "@types/validator": "10.11.3", "@typescript-eslint/eslint-plugin": "3.8.0", "@typescript-eslint/parser": "3.8.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "commitlint-config-jira": "^1.4.1", "commitlint-plugin-jira-rules": "^1.4.0", "eslint": "7.6.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-import": "2.22.0", "husky": "4.2.5", "lint-staged": "10.2.11", "mocha": "^7.1.2", "mocha-typescript": "^1.1.17", "nyc": "^15.0.1", "prettier": "2.0.5", "reflect-metadata": "0.1.13", "release-it": "13.6.6", "sinon": "^9.0.2", "superagent-mocker": "^0.5.2", "supertest": "^4.0.2", "ts-loader": "^6.2.1", "ts-node": "^8.10.2", "tsconfig-paths": "^3.9.0", "typescript": "3.9.7"}, "lint-staged": {"*.ts": ["prettier --write"]}, "husky": {"hooks": {"commit-msg": "commitlint -c .commitlintrc.json -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "_moduleAliases": {"@entities": "src/entities", "@errors": "src/errors", "@utils": "src/utils", "@config": "src/config", "@names": "src/names", "@resources": "resources", "@wallet": "src/wallet", "@payment": "src/wallet/payment", "@launcher": "src/launcher", "@operator": "src/operator", "@db": "src/db", "@mock": "src/mock"}}