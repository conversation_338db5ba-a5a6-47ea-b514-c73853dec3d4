{"name": "sw-integration-pronet", "version": "1.0.0", "description": "PCES Generic Vendor API v5 Integration for ProNet Gaming", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["gaming", "casino", "api", "integration", "pronet"], "author": "ProNet Gaming Integration Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "winston": "^3.11.0", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.8.0", "@types/jest": "^29.5.5", "@types/cors": "^2.8.15", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}