version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - LOG_LEVEL=debug
      - BASE_ENGINE_URL=http://casinoengine.test.pronetgaming.com/
      - VENDOR_CODE=test_vendor
      - GENERIC_ID=test_generic_id
      - GENERIC_SECRET_KEY=test_secret_key
      - GAME_URL=http://test-game.example.com/game
      - DEMO_GAME_URL=http://test-game.example.com/demo
    volumes:
      - ./logs:/app/logs
      - ./src:/app/src:ro
    depends_on:
      - redis
    networks:
      - pronet-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pronet-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - pronet-network
    restart: unless-stopped

volumes:
  redis_data:

networks:
  pronet-network:
    driver: bridge
