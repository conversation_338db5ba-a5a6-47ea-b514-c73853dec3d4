# PCES Generic Vendor API v5 Configuration

# Server Configuration
PORT=3000
NODE_ENV=development

# PCES API Configuration
BASE_ENGINE_URL=http://casinoengine.test.pronetgaming.com/
VENDOR_CODE=your_vendor_code
GENERIC_ID=your_generic_id
GENERIC_SECRET_KEY=your_secret_key

# Game URLs
GAME_URL=https://your-game-provider.com/game
DEMO_GAME_URL=https://your-game-provider.com/demo
FREESPIN_URL=https://your-game-provider.com/freespin
ROUND_DETAILS_URL=https://your-game-provider.com/round-details

# Security
HASH_ALGORITHM=sha256
TOKEN_TIMEOUT_MINUTES=5

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Database (if needed)
# DATABASE_URL=postgresql://user:password@localhost:5432/pronet_integration

# Redis (for caching/sessions if needed)
# REDIS_URL=redis://localhost:6379

# Monitoring
# SENTRY_DSN=your_sentry_dsn
# NEW_RELIC_LICENSE_KEY=your_newrelic_key
