@Library('sw_common_library') _

pipeline {
    agent any

    options {
        disableConcurrentBuilds()
        timestamps()
    }

    environment {
        REPO = "skywindgroup"
        REGION = "asia.gcr.io"
        PROJECT = "gcpstg"
        REVISION = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
        BRANCH_NAME_NORMALIZED = "${BRANCH_NAME.toLowerCase().replace("/", "_")}"
        DEV_PROJECT = "sw-dev-qa"
        NAMESPACE = "dev"
        DEV_DEPLOMENTS_LIST = "sw-wallet-api"
        SERVICE = "sw-integration-b365"
    }

    stages {
         stage('Build Image') {
            steps {
                configFileProvider([configFile(fileId: 'npmInstall', targetLocation: '.npmrc')]) {
                sh "docker build --no-cache -f Dockerfile.prod -t ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ."  
              }
           }
        }
        

         stage('Tag Image') {
            steps {
                 sh "docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                 sh "docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${REVISION}"
            }
        }

        stage('Push Docker Image') {
            
            steps {
               
                sh "docker push ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                sh "docker push ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                sh "docker push ${REGION}/${PROJECT}/${SERVICE}:${REVISION}"
                script {
                        try {
                            GCR_IMAGE = sh(returnStdout: true, script: "docker image inspect '${REGION}/${PROJECT}/${SERVICE}:${REVISION}' | jq .[0] | jq -c '.RepoDigests' | jq .[0]").trim()
                            currentBuild.description = "${GCR_IMAGE}"
                        } catch (Exception e) {
                            echo 'Image inspection failed!'
                    }
                }
            }
        }
        stage('Clean up') {
            steps {
                script{
                    if (BRANCH_NAME ==~ /((?!PR-).*)/) {
              	        sh "docker rmi ${REGION}/${PROJECT}/${SERVICE}:${REVISION}"
                        sh "docker rmi ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                    }
                }
                sh "docker rmi ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
            }
        }
        stage('Deploy to Dev Env') {
            when {
                branch 'develop'
            }
            steps {
                deployDevEnv()
            }
        }
    }
    post {
        failure {
            emailext (
                to: '<EMAIL>',
                body: "Check console output at $BUILD_URL to view the results.",
                subject: 'Build $BUILD_STATUS! - $PROJECT_NAME',
                attachLog: true,
                compressLog: true
            )
        }
    }
}



