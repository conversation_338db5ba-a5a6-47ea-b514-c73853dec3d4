{"compilerOptions": {"module": "commonjs", "declaration": true, "noImplicitAny": false, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2018", "outDir": "./lib", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "baseUrl": "./", "paths": {"@entities/*": ["src/entities/*"], "@errors/*": ["src/errors/*"], "@utils/*": ["src/utils/*"], "@config": ["src/config"], "@names": ["src/names"], "@resources/*": ["resources/*"], "@wallet/*": ["src/wallet/*"], "@payment/*": ["src/wallet/payment/*"], "@launcher/*": ["src/launcher/*"], "@operator/*": ["src/operator/*"], "@db/*": ["src/db/*"], "@mock/*": ["src/mock/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules"]}