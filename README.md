# ProNet Gaming Integration - PCES Generic Vendor API v5

This project implements a comprehensive integration system for the PCES Generic Vendor API v5, providing a robust foundation for casino game provider integrations.

## 🏗️ Architecture Overview

The integration follows a modular, service-oriented architecture:

```
src/
├── config/           # Configuration management
├── services/         # Core business logic services
├── types/           # TypeScript type definitions
├── utils/           # Utility functions and helpers
├── test/            # Test files and setup
├── server.ts        # Express server setup
└── index.ts         # Application entry point
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- TypeScript knowledge

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment configuration:
   ```bash
   cp .env.example .env
   ```

4. Configure your environment variables in `.env`

5. Build the project:
   ```bash
   npm run build
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

## 📋 Configuration

### Required Environment Variables

```env
# PCES API Configuration
BASE_ENGINE_URL=http://casinoengine.test.pronetgaming.com/
VENDOR_CODE=your_vendor_code
GENERIC_ID=your_generic_id
GENERIC_SECRET_KEY=your_secret_key

# Game URLs
GAME_URL=https://your-game-provider.com/game
DEMO_GAME_URL=https://your-game-provider.com/demo
```

### Optional Configuration

```env
# Optional Services
FREESPIN_URL=https://your-game-provider.com/freespin
ROUND_DETAILS_URL=https://your-game-provider.com/round-details

# Server Configuration
PORT=3000
NODE_ENV=development

# Security
TOKEN_TIMEOUT_MINUTES=5

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
```

## 🔧 Core Services

### 1. Wallet Service (`src/services/wallet.ts`)

Handles all wallet-related operations:

- **Authentication** (`/auth`) - Player balance retrieval
- **Debit-Credit** (`/debit-credit`) - Combined bet placement and settlement
- **Debit** (`/debit`) - Bet placement
- **Credit** (`/credit`) - Bet settlement
- **Rollback** (`/rollback`) - Transaction reversal
- **Promo** (`/promo`) - Promotional wins

**Usage Example:**
```typescript
const walletService = new WalletService();
const authResult = await walletService.authenticate({
  customer: "player123",
  token: "session_token",
  hash: "" // Auto-generated
});
```

### 2. Game Service (`src/services/game.ts`)

Manages game URL generation and validation:

**Usage Example:**
```typescript
const gameService = new GameService();
const gameUrl = gameService.buildGameUrl({
  currency: "USD",
  customer: "player123",
  demo: false,
  gameId: "slot_game_1",
  lang: "en",
  lobby: "https://casino.com/lobby",
  platform: "d",
  trader: "CASINO1"
});
```

### 3. Security Utils (`src/utils/security.ts`)

Provides cryptographic functions:

- Hash calculation (HMAC SHA-256)
- Hash validation
- Transaction ID generation
- Token expiration checking

### 4. Integration Service (`src/services/integration.ts`)

High-level orchestration service that combines all functionality:

```typescript
const integration = new IntegrationService();

// Authenticate player
const auth = await integration.authenticatePlayer("player123", "token");

// Place bet and settle immediately
const result = await integration.placeBetAndSettle(
  "player123", "token", "game1", 10.0, 25.0, "USD"
);
```

## 🔐 Security Implementation

### Hash Calculation

All API requests require HMAC SHA-256 hash validation:

1. Serialize request body to JSON
2. Concatenate with secret key
3. Calculate SHA-256 hash
4. Include in request headers

**Example:**
```typescript
const hash = SecurityUtils.calculateHash(requestBody);
// Hash automatically added to requests by HttpClient
```

### Token Management

- Tokens expire after 5 minutes (configurable)
- Each debit request refreshes token expiration
- Credit/rollback accepted even after expiration

## 🎮 Game Integration Patterns

### Real Money Games

```typescript
// 1. Open game with parameters
const gameUrl = integration.buildGameUrl({
  currency: "USD",
  customer: "player123",
  demo: false,
  gameId: "slot_1",
  token: "session_token",
  // ... other params
});

// 2. Player places bet
const betResult = await integration.placeBet(
  "player123", "token", "slot_1", 10.0, "USD"
);

// 3. Settle bet when round completes
const settleResult = await integration.settleBet(
  "player123", "token", "slot_1", 25.0, "USD", betResult.betId
);
```

### Demo Games

```typescript
const demoUrl = integration.buildDemoGameUrl({
  currency: "FUN",
  demo: true,
  gameId: "slot_1",
  // ... other params
});
```

### Combined Bet/Settle (Preferred)

```typescript
const result = await integration.placeBetAndSettle(
  "player123", "token", "slot_1", 10.0, 25.0, "USD"
);
```

## 🎁 Promotional Features

### Freespin Management

```typescript
// Give freespins
await integration.giveFreespins({
  customers: ["player123"],
  games: ["slot_1"],
  numberOfFreespins: 10,
  freespinRef: "promo_123",
  wagerRequirement: 1.0,
  validFrom: new Date(),
  validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
});

// Cancel freespins
await integration.cancelFreespins("promo_123");
```

### Promotional Wins

```typescript
await integration.processPromotion(
  "player123", "token", "slot_1", 100.0, "USD", "TW", "tournament_123"
);
```

## 📊 Error Handling

### Wallet Errors

The system provides comprehensive error handling with specific error codes:

```typescript
try {
  await walletService.debit(request);
} catch (error) {
  if (error instanceof WalletError) {
    if (error.isRetryable()) {
      // Implement retry logic
    } else if (error.shouldStopSending()) {
      // Stop sending requests
    } else if (error.shouldRevertRound()) {
      // Revert the game round
    }
  }
}
```

### Common Error Codes

- `0` - SUCCESS
- `-20306` - INSUFFICIENT_FUNDS
- `-20310` - BET_ALREADY_SETTLED
- `-20311` - TOKEN_TIMEOUT
- `-20304` - CUSTOMER_NOT_FOUND

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm test -- --coverage
```

### Test Structure

- Unit tests for utilities and services
- Integration tests for API endpoints
- Mock implementations for external dependencies

### Example Test

```typescript
describe('SecurityUtils', () => {
  it('should calculate hash correctly', () => {
    const requestBody = { test: 'data' };
    const hash = SecurityUtils.calculateHash(requestBody);
    
    expect(hash).toBeDefined();
    expect(typeof hash).toBe('string');
    expect(hash.length).toBe(64);
  });
});
```

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Setup

1. Set `NODE_ENV=production`
2. Configure production URLs and credentials
3. Set up logging and monitoring
4. Configure reverse proxy (nginx/Apache)

### Health Checks

The server provides a health check endpoint:

```
GET /health
Response: { "status": "OK", "timestamp": "2024-01-01T00:00:00.000Z" }
```

## 📈 Monitoring and Logging

### Structured Logging

All operations are logged with structured data:

```typescript
logger.info('API Request', {
  endpoint: '/auth',
  customer: 'player123',
  // Sensitive data automatically redacted
});
```

### Log Levels

- `error` - Errors and exceptions
- `warn` - Warnings and deprecated usage
- `info` - General information
- `debug` - Detailed debugging information

## 🔄 API Endpoints

### Wallet Operations

- `POST /api/auth` - Authenticate player
- `POST /api/bet-settle` - Place bet and settle
- `POST /api/bet` - Place bet only
- `POST /api/settle` - Settle bet only
- `POST /api/rollback` - Rollback transaction
- `POST /api/promo` - Process promotion

### Game Operations

- `POST /api/game-url` - Generate game URL
- `POST /api/demo-game-url` - Generate demo game URL

### Freespin Operations

- `POST /api/freespins` - Give freespins
- `DELETE /api/freespins/:ref` - Cancel freespins

### Utility Operations

- `GET /api/round-details` - Get round details
- `GET /health` - Health check

## 🛠️ Development Guidelines

### Code Style

- Use TypeScript strict mode
- Follow ESLint configuration
- Write comprehensive tests
- Document public APIs

### Adding New Features

1. Define types in `src/types/`
2. Implement service in `src/services/`
3. Add tests
4. Update documentation
5. Add API endpoints if needed

### Best Practices

- Always validate input parameters
- Use structured logging
- Handle errors gracefully
- Implement proper retry logic
- Follow security best practices

## 📚 Additional Resources

- [PCES Generic Vendor API v5 Documentation](./PCES-Generic%20Vendor%20API%20v5-120525-143606.md)
- [TypeScript Documentation](https://www.typescriptlang.org/)
- [Express.js Guide](https://expressjs.com/)
- [Jest Testing Framework](https://jestjs.io/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
