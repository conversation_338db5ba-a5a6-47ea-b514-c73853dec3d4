# PCES Generic Vendor API v5 Integration Guidelines

## 📖 Overview

This document provides comprehensive guidelines for building casino game provider integrations using the PCES Generic Vendor API v5. It serves as a blueprint for creating robust, scalable, and maintainable integration systems.

## 🎯 Integration Objectives

### Primary Goals
- **Seamless Wallet Integration**: Reliable bet placement, settlement, and balance management
- **Secure Communication**: HMAC SHA-256 hash validation for all transactions
- **Game Launch Management**: Support for both real money and demo games
- **Promotional Features**: Freespin and bonus management capabilities
- **Error Resilience**: Comprehensive error handling and retry mechanisms
- **Monitoring & Logging**: Full audit trail and operational visibility

### Success Criteria
- 99.9% uptime for wallet operations
- Sub-100ms response times for authentication
- Zero data loss during transaction processing
- Complete audit trail for all financial operations

## 🏗️ Architecture Principles

### 1. Modular Design
```
Integration System
├── Configuration Layer    # Environment and settings management
├── Security Layer        # Hash validation and token management  
├── Service Layer         # Business logic and API communication
├── Transport Layer       # HTTP client and request handling
└── Monitoring Layer      # Logging, metrics, and health checks
```

### 2. Separation of Concerns
- **Configuration**: Centralized environment management
- **Security**: Isolated cryptographic operations
- **Business Logic**: Domain-specific service implementations
- **Communication**: Dedicated HTTP client with interceptors
- **Error Handling**: Consistent error processing across all services

### 3. Fail-Safe Operations
- Idempotent transaction processing
- Automatic retry with exponential backoff
- Circuit breaker patterns for external dependencies
- Graceful degradation for non-critical features

## 🔧 Implementation Strategy

### Phase 1: Foundation (Week 1-2)
1. **Project Setup**
   - Initialize TypeScript project with strict configuration
   - Set up testing framework (Jest) with comprehensive coverage
   - Configure linting (ESLint) and formatting (Prettier)
   - Implement CI/CD pipeline

2. **Core Infrastructure**
   - Configuration management system
   - Logging and monitoring setup
   - Security utilities (hash calculation, validation)
   - HTTP client with interceptors

3. **Basic Wallet Integration**
   - Authentication endpoint
   - Simple debit/credit operations
   - Error handling framework

### Phase 2: Core Features (Week 3-4)
1. **Complete Wallet API**
   - All wallet endpoints (/auth, /debit-credit, /debit, /credit, /rollback, /promo)
   - Transaction state management
   - Retry logic implementation

2. **Game Integration**
   - Game URL generation
   - Parameter validation
   - Demo game support

3. **Testing & Validation**
   - Unit tests for all services
   - Integration tests with mock PCES API
   - Load testing for performance validation

### Phase 3: Advanced Features (Week 5-6)
1. **Promotional Features**
   - Freespin API implementation
   - Promotional win processing
   - Campaign management

2. **Round Details**
   - Round information retrieval
   - Multiple response format support (JSON, Image, HTML)

3. **Monitoring & Operations**
   - Health check endpoints
   - Metrics collection
   - Alert configuration

### Phase 4: Production Readiness (Week 7-8)
1. **Security Hardening**
   - Security audit and penetration testing
   - Rate limiting implementation
   - Input validation strengthening

2. **Performance Optimization**
   - Response time optimization
   - Memory usage optimization
   - Database query optimization (if applicable)

3. **Documentation & Training**
   - API documentation
   - Operational runbooks
   - Team training materials

## 🔐 Security Implementation

### Hash Validation Process
```typescript
// 1. Prepare request body (without hash)
const requestBody = {
  customer: "player123",
  token: "session_token",
  gameId: "slot_1",
  amount: 10.0,
  currency: "USD"
};

// 2. Calculate hash
const jsonString = JSON.stringify(requestBody);
const toHash = jsonString + GENERIC_SECRET_KEY;
const hash = crypto.createHash('sha256').update(toHash).digest('hex');

// 3. Add hash to request
const finalRequest = { ...requestBody, hash };

// 4. Include hash in headers
headers['Hash'] = hash;
```

### Token Management
- **Creation**: Tokens created on game launch
- **Expiration**: 5-minute sliding window
- **Refresh**: Extended on each debit operation
- **Validation**: Required for all wallet operations

### Security Best Practices
- Never log sensitive data (tokens, hashes, secret keys)
- Use HTTPS for all communications
- Implement request rate limiting
- Validate all input parameters
- Use secure random number generation

## 🎮 Game Integration Patterns

### Pattern 1: Instant Settlement (Recommended)
```typescript
// Single API call for bet placement and settlement
const result = await walletService.debitCredit({
  customer: "player123",
  token: "session_token",
  gameId: "slot_1",
  amount: 10.0,        // Bet amount
  creditAmount: 25.0,  // Win amount
  currency: "USD",
  betId: generateBetId(),
  trxId: generateTrxId(),
  creditTrxId: generateTrxId()
});
```

**Use Cases:**
- Slot games
- Instant win games
- Simple table games

**Benefits:**
- Atomic operations
- No open bets
- Simplified error handling

### Pattern 2: Separate Debit/Credit
```typescript
// 1. Place bet
const debitResult = await walletService.debit({
  customer: "player123",
  token: "session_token",
  gameId: "poker_1",
  amount: 10.0,
  currency: "USD",
  betId: "bet_123",
  trxId: "debit_456"
});

// 2. Settle bet (when round completes)
const creditResult = await walletService.credit({
  customer: "player123",
  token: "session_token",
  gameId: "poker_1",
  amount: 25.0,
  currency: "USD",
  betId: "bet_123",
  trxId: "credit_789"
});
```

**Use Cases:**
- Live dealer games
- Poker games
- Multi-stage games

**Benefits:**
- Flexible timing
- Support for complex game flows
- Real-time balance updates

### Pattern 3: Bonus Buy Features
```typescript
// 1. Debit for bonus buy
const buyResult = await walletService.debit({
  customer: "player123",
  token: "session_token",
  gameId: "slot_1",
  amount: 100.0,  // Bonus buy cost
  currency: "USD",
  betId: "bonus_buy_123",
  trxId: "buy_456"
});

// 2. Credit when bonus completes
const bonusResult = await walletService.credit({
  customer: "player123",
  token: "session_token",
  gameId: "slot_1",
  amount: 250.0,  // Bonus winnings
  currency: "USD",
  betId: "bonus_buy_123",
  trxId: "bonus_win_789"
});
```

## 🎁 Promotional Integration

### Freespin Implementation
```typescript
// 1. Receive freespin grant from PCES
const freespinRequest = {
  customers: ["player123"],
  games: ["slot_1"],
  numberOfFreespins: 10,
  freespinRef: "promo_abc123",
  wagerRequirement: 1.0,
  validFrom: new Date(),
  validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  maxWin: 500.0
};

// 2. Store freespin data in game system
await storeFreespinData(freespinRequest);

// 3. When player uses freespin, credit with special marking
const freespinWin = await walletService.promo({
  customer: "player123",
  token: "session_token",
  gameId: "slot_1",
  amount: 25.0,
  currency: "USD",
  betId: "freespin_bet_123",
  trxId: "freespin_win_456",
  promo: {
    promoType: "FSW",
    promoRef: "promo_abc123",
    freeSpinData: {
      remainingRounds: 9,
      totalWinnings: 25.0,
      requested: true
    }
  }
});
```

### Promotional Win Types
- **FSW**: Freespin wins
- **JPW**: Jackpot wins  
- **CB**: Cashback
- **TW**: Tournament wins
- **RW**: Reward wins
- **REW**: Red envelope wins
- **CDW**: Cash drop wins
- **RB**: Rakeback

## 📊 Error Handling Strategy

### Error Classification
```typescript
class WalletError extends Error {
  constructor(
    public readonly code: number,
    public readonly status: string
  ) {
    super(`Wallet Error ${code}: ${status}`);
  }

  isRetryable(): boolean {
    // Errors that should be retried
    const retryableCodes = [1, 6, -20101, -20304];
    return retryableCodes.includes(this.code);
  }

  shouldStopSending(): boolean {
    // Errors that require stopping requests
    const stopCodes = [2, 3, 4, 5, 7, -20120, -20130];
    return stopCodes.includes(this.code);
  }

  shouldRevertRound(): boolean {
    // Errors that require game round reversion
    const revertCodes = [-20201, -20202, -20203, -20204];
    return revertCodes.includes(this.code);
  }
}
```

### Retry Strategy
```typescript
async function retryableOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 5
): Promise<T> {
  const delays = [30, 60, 300, 900, 1800]; // seconds
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof WalletError && error.isRetryable() && attempt < maxRetries) {
        await sleep(delays[attempt] * 1000);
        continue;
      }
      throw error;
    }
  }
  
  throw new Error('Max retries exceeded');
}
```

### Circuit Breaker Pattern
```typescript
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
```

## 📈 Monitoring and Observability

### Key Metrics
```typescript
// Business Metrics
- transaction_count{type="debit|credit|rollback"}
- transaction_amount{currency="USD|EUR|GBP"}
- transaction_duration_seconds
- error_rate{error_code="xxx"}
- active_players_count
- game_session_duration

// Technical Metrics
- http_request_duration_seconds
- http_request_count{status_code="xxx"}
- memory_usage_bytes
- cpu_usage_percent
- database_connection_pool_size
```

### Health Checks
```typescript
interface HealthCheck {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  lastChecked: Date;
  details?: Record<string, unknown>;
}

const healthChecks = [
  {
    name: 'pces_api',
    check: () => pcesApiHealthCheck(),
    timeout: 5000
  },
  {
    name: 'database',
    check: () => databaseHealthCheck(),
    timeout: 3000
  },
  {
    name: 'redis',
    check: () => redisHealthCheck(),
    timeout: 2000
  }
];
```

### Structured Logging
```typescript
// Request/Response Logging
logger.info('wallet_request', {
  endpoint: '/debit',
  customer: 'player123',
  amount: 10.0,
  currency: 'USD',
  request_id: 'req_abc123',
  duration_ms: 150
});

// Error Logging
logger.error('wallet_error', {
  endpoint: '/debit',
  error_code: -20306,
  error_status: 'INSUFFICIENT_FUNDS',
  customer: 'player123',
  request_id: 'req_abc123'
});

// Business Event Logging
logger.info('game_round_completed', {
  customer: 'player123',
  game_id: 'slot_1',
  bet_amount: 10.0,
  win_amount: 25.0,
  currency: 'USD',
  round_id: 'round_xyz789'
});
```

## 🧪 Testing Strategy

### Test Pyramid
```
                    E2E Tests (5%)
                 ┌─────────────────┐
                 │  Full Integration │
                 │  Real API Calls   │
                 └─────────────────┘

              Integration Tests (15%)
           ┌─────────────────────────┐
           │   Service Integration    │
           │   Mock External APIs     │
           └─────────────────────────┘

         Unit Tests (80%)
    ┌─────────────────────────────────┐
    │      Individual Functions       │
    │      Pure Logic Testing         │
    │      Fast & Isolated           │
    └─────────────────────────────────┘
```

### Test Categories

#### 1. Unit Tests
```typescript
describe('SecurityUtils', () => {
  describe('calculateHash', () => {
    it('should produce consistent hashes', () => {
      const data = { test: 'data' };
      const hash1 = SecurityUtils.calculateHash(data);
      const hash2 = SecurityUtils.calculateHash(data);
      expect(hash1).toBe(hash2);
    });

    it('should produce different hashes for different data', () => {
      const hash1 = SecurityUtils.calculateHash({ test: 'data1' });
      const hash2 = SecurityUtils.calculateHash({ test: 'data2' });
      expect(hash1).not.toBe(hash2);
    });
  });
});
```

#### 2. Integration Tests
```typescript
describe('WalletService Integration', () => {
  let mockServer: MockServer;

  beforeEach(() => {
    mockServer = new MockServer();
    mockServer.start();
  });

  it('should handle successful authentication', async () => {
    mockServer.mockAuth({ code: 0, balance: 100.0 });

    const result = await walletService.authenticate({
      customer: 'test_player',
      token: 'test_token',
      hash: 'calculated_hash'
    });

    expect(result.code).toBe(0);
    expect(result.balance).toBe(100.0);
  });
});
```

## 🚀 Deployment Guidelines

### Environment Configuration

#### Development
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  app:
    build: .
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - BASE_ENGINE_URL=http://test-api.example.com/
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
```

#### Production
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  app:
    image: pronet-integration:latest
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 📋 Common Issues and Solutions

### Issue: Hash Validation Failures
**Symptoms**: Consistent authentication failures with error code 2
**Causes**:
- Incorrect secret key
- JSON serialization differences
- Character encoding issues

**Solutions**:
```typescript
// Ensure consistent JSON serialization
const jsonString = JSON.stringify(requestBody, Object.keys(requestBody).sort());

// Verify hash calculation
console.log('Request body:', requestBody);
console.log('JSON string:', jsonString);
console.log('To hash:', jsonString + secretKey);
console.log('Calculated hash:', hash);
```

### Issue: Token Timeout Errors
**Symptoms**: Error code -20311 during gameplay
**Causes**:
- Long game rounds exceeding 5-minute limit
- Network delays
- Server processing delays

**Solutions**:
- Implement token refresh mechanism
- Reduce game round duration
- Optimize server response times
- Request extended token timeout from PCES

This comprehensive guide provides the foundation for building robust, scalable, and maintainable PCES Generic Vendor API v5 integrations.
```
